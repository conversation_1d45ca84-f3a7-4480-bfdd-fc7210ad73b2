<template>
  <StarryBackground />
  <div class="profile-page">
    <div class="profile-container">
      <div class="profile-header">
        <h1>{{ $t('profile.title') }}</h1>
        <router-link to="/" class="home-btn">
          <i class="fas fa-home"></i> {{ $t('profile.home') }}
        </router-link>
      </div>
      
      <div class="profile-content">
        <!-- User Avatar Section -->
        <div class="avatar-section">
          <div class="avatar-wrapper">
            <img :src="userInfo.userAvatar || 'https://via.placeholder.com/150'" alt="User Avatar" class="profile-avatar">
            <button 
              class="upload-avatar-icon-btn" 
              @click="handleUploadAvatar" 
              :disabled="isUploadingAvatar"
              :title="$t('profile.uploadAvatar')"
            >
              <i class="fas fa-camera"></i>
            </button>
          </div>
          <p class="upload-avatar-text">{{ isUploadingAvatar ? $t('profile.uploading') : $t('profile.clickToChange') }}</p>
          <input
            type="file"
            ref="avatarInput"
            style="display: none"
            accept="image/jpeg,image/png,image/jpg"
            @change="handleAvatarFileChange"
          />
          <h2 class="profile-username">{{ userInfo.userName || 'Username' }}</h2>
          <p class="profile-email">{{ userInfo.email || '<EMAIL>' }}</p>
        </div>
        
        <!-- User Info Section -->
        <div class="info-section">
          <div class="info-card">
            <h3>{{ $t('profile.basicInfo') }}</h3>
            <div class="info-item">
              <label>{{ $t('profile.firstName') }}</label>
              <div class="info-value">{{ userInfo.firstName || $t('profile.notSet') }}</div>
            </div>
            <div class="info-item">
              <label>{{ $t('profile.lastName') }}</label>
              <div class="info-value">{{ userInfo.lastName || $t('profile.notSet') }}</div>
            </div>
            <div class="info-item">
              <label>{{ $t('profile.phone') }}</label>
              <div class="info-value">{{ userInfo.phone || $t('profile.notSet') }}</div>
            </div>
            <div class="info-item">
              <label>{{ $t('profile.preferredCurrency') }}</label>
              <div class="info-value">{{ userInfo.currency || 'USD' }}</div>
            </div>
          </div>
        </div>
        
        <!-- Account Section -->
        <div class="account-section">
          <div class="info-card">
            <h3>{{ $t('profile.accountSettings') }}</h3>
            <div class="action-buttons">
              <button class="action-btn" @click="showChangePasswordDialog = true">
                <i class="fas fa-lock"></i> {{ $t('profile.changePassword') }}
              </button>
              <button class="action-btn" @click="openEditProfileDialog">
                <i class="fas fa-cog"></i> {{ $t('profile.editProfile') }}
              </button>
              <button class="action-btn logout-btn" @click="logout">
                <i class="fas fa-sign-out-alt"></i> {{ $t('profile.logout') }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Edit Profile Dialog -->
    <el-dialog
      v-model="showEditProfileDialog"
      title="Edit Profile"
      width="500px"
      class="custom-dialog"
      :show-close="true"
      :close-on-click-modal="false"
    >
      <el-form 
        :model="profileForm" 
        ref="profileFormRef" 
        label-width="120px"
        :rules="profileRules"
      >
        <el-form-item label="First Name" prop="firstName">
          <el-input v-model="profileForm.firstName" placeholder="Enter your first name" />
        </el-form-item>
        
        <el-form-item label="Last Name" prop="lastName">
          <el-input v-model="profileForm.lastName" placeholder="Enter your last name" />
        </el-form-item>
        
        <el-form-item label="Phone" prop="phone">
          <el-input v-model="profileForm.phone" placeholder="Enter your phone number" />
        </el-form-item>
        
        <el-form-item label="Currency" prop="currency">
          <el-select v-model="profileForm.currency" placeholder="Select currency">
            <el-option label="USD" value="USD" />
            <el-option label="EUR" value="EUR" />
            <el-option label="GBP" value="GBP" />
            <el-option label="CNY" value="CNY" />
            <el-option label="JPY" value="JPY" />
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <div class="dialog-buttons">
            <el-button @click="showEditProfileDialog = false">{{ $t('profile.cancel') }}</el-button>
            <el-button type="primary" @click="updateProfile" :loading="isLoading">{{ $t('profile.save') }}</el-button>
          </div>
        </el-form-item>
      </el-form>
    </el-dialog>
    
    <!-- Change Password Dialog -->
    <el-dialog
      v-model="showChangePasswordDialog"
      title="Change Password"
      width="500px"
      class="custom-dialog"
      :show-close="true"
      :close-on-click-modal="false"
    >
      <el-form 
        :model="passwordForm" 
        ref="passwordFormRef" 
        label-width="160px"
        :rules="passwordRules"
      >
        <el-form-item label="Current Password" prop="currentPassword">
          <el-input 
            v-model="passwordForm.currentPassword" 
            type="password"
            :placeholder="$t('profile.enterCurrentPassword')" 
            show-password
          />
        </el-form-item>
        
        <el-form-item label="New Password" prop="newPassword">
          <el-input
            v-model="passwordForm.newPassword"
            type="password"
            :placeholder="$t('profile.enterNewPassword')"
            show-password
          />
          <div v-if="passwordForm.newPassword" class="password-strength-meter">
            <div class="strength-meter">
              <div 
                class="strength-meter-fill" 
                :class="passwordStrengthClass"
                :style="{ width: passwordStrengthPercent + '%' }"
              ></div>
            </div>
            <div class="strength-text" :class="passwordStrengthClass">
              Password Strength: {{ passwordStrengthLabel }}
            </div>
          </div>
        </el-form-item>
        
        <el-form-item label="Confirm Password" prop="confirmPassword">
          <el-input 
            v-model="passwordForm.confirmPassword" 
            type="password"
            :placeholder="$t('profile.confirmNewPasswordPlaceholder')" 
            show-password
          />
        </el-form-item>
        
        <el-form-item>
          <div class="dialog-buttons password-dialog-buttons">
            <el-button @click="showChangePasswordDialog = false" class="password-btn">{{ $t('profile.cancel') }}</el-button>
            <el-button type="primary" @click="changePassword" :loading="isLoading" class="password-btn">{{ $t('profile.changePassword') }}</el-button>
          </div>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { ElMessage, ElForm, ElFormItem, ElInput, ElButton, ElSelect, ElOption, ElDialog } from 'element-plus';
import { emitter } from '@/utils/eventBus';
import { updatePassword, getUserByEmail, changeUserInformation, uploadImage } from '@/api/users';
import { ref } from 'vue';
import StarryBackground from '@/components/StarryBackground.vue';

export default {
  name: 'ProfileView',
  components: {
    ElForm,
    ElFormItem,
    ElInput,
    ElButton,
    ElSelect,
    ElOption,
    ElDialog,
    StarryBackground
  },
  setup() {
    // 在setup中创建表单引用
    const profileFormRef = ref(null);
    const passwordFormRef = ref(null);
    
    return {
      profileFormRef,
      passwordFormRef
    };
  },
  data() {
    // 确认密码的验证函数
    const validateConfirmPassword = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('Please confirm your password'));
      } else if (value !== this.passwordForm.newPassword) {
        callback(new Error('Passwords do not match'));
      } else {
        callback();
      }
    };
    
    return {
      userInfo: {},
      isLoading: false,
      isUploadingAvatar: false,
      isDarkMode: true, // 添加主题状态变量
      
      // 编辑个人资料对话框
      showEditProfileDialog: false,
      profileForm: {
        firstName: '',
        lastName: '',
        phone: '',
        currency: 'USD'
      },
      profileRules: {
        phone: [
          { pattern: /^[0-9+\-\s]*$/, message: 'Please enter a valid phone number', trigger: 'blur' }
        ]
      },
      
      // 修改密码对话框
      showChangePasswordDialog: false,
      passwordForm: {
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      },
      passwordRules: {
        currentPassword: [
          { required: true, message: 'Please enter your current password', trigger: 'blur' }
        ],
        newPassword: [
          { required: true, message: 'Please enter your new password', trigger: 'blur' },
          { min: 6, message: 'Password must be at least 6 characters', trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, message: 'Please confirm your password', trigger: 'blur' },
          { validator: validateConfirmPassword, trigger: 'blur' }
        ]
      }
    };
  },
  created() {
    this.loadUserInfo();
    this.checkCurrentTheme(); // 检查当前主题
  },
  mounted() {
    // 添加主题变化监听
    document.addEventListener('themechange', this.checkCurrentTheme);
    
    // 如果存在emitter，也添加监听
    if (window.emitter) {
      window.emitter.on('theme-changed', this.handleThemeChange);
    }
    
    // 监听HTML元素的data-theme属性变化
    const htmlElement = document.documentElement;
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.attributeName === 'data-theme') {
          this.checkCurrentTheme();
        }
      });
    });
    
    observer.observe(htmlElement, { attributes: true, attributeFilter: ['data-theme'] });
    
    // 组件卸载时移除监听
    this.$options.unmounted = () => {
      document.removeEventListener('themechange', this.checkCurrentTheme);
      if (window.emitter) {
        window.emitter.off('theme-changed', this.handleThemeChange);
      }
      observer.disconnect();
    };
  },
  methods: {
    // 检测当前主题
    checkCurrentTheme() {
      // 检查HTML元素的data-theme属性
      const htmlElement = document.documentElement;
      const theme = htmlElement.getAttribute('data-theme');
      this.isDarkMode = theme === 'dark';
    },
    
    // 处理主题变化
    handleThemeChange(data) {
      if (data && typeof data.isDarkMode !== 'undefined') {
        this.isDarkMode = data.isDarkMode;
      }
    },
    
    async loadUserInfo() {
      // 检查用户是否已登录
      const isLoggedIn = localStorage.getItem('isLoggedIn') === 'true';
      if (!isLoggedIn) {
        ElMessage({
          message: 'Please login to view your profile',
          type: 'warning',
          duration: 3000
        });
        this.$router.push('/login');
        return;
      }
      
      this.isLoading = true;
      
      try {
        // 从localStorage获取基本用户信息
        const userInfoStr = localStorage.getItem('userInfo');
        let localUserInfo = {};
        
        if (userInfoStr) {
          localUserInfo = JSON.parse(userInfoStr);
        }
        
        // 如果有邮箱，使用API获取完整用户信息
        if (localUserInfo.email) {
          const response = await getUserByEmail(localUserInfo.email);
          
          if (response && response.code === 200 && response.data) {
            // 合并API返回的用户数据和本地存储的数据
            this.userInfo = {
              ...localUserInfo,
              ...response.data,
              // 确保以下字段存在
              userId: response.data.userId || localUserInfo.userId || localUserInfo.id,
              userName: response.data.username || localUserInfo.userName,
              userAvatar: localUserInfo.userAvatar || null,
              phone: response.data.phoneNumber || localUserInfo.phone || ''
            };
            
            // 更新本地存储的用户信息
            localStorage.setItem('userInfo', JSON.stringify(this.userInfo));
          } else {
            // 如果API获取失败，使用本地数据
            this.userInfo = localUserInfo;
          }
        } else {
          // 如果没有邮箱，使用本地数据
          this.userInfo = localUserInfo;
        }
      } catch (e) {
        ElMessage.error('Failed to load user information');
        
        // 尝试从localStorage加载
        try {
          const userInfoStr = localStorage.getItem('userInfo');
          if (userInfoStr) {
            this.userInfo = JSON.parse(userInfoStr);
          }
        } catch (localError) {
          // Handle error silently
        }
      } finally {
        this.isLoading = false;
      }
    },
    
    // 打开编辑个人资料对话框
    openEditProfileDialog() {
      // 初始化表单数据
      this.profileForm = {
        firstName: this.userInfo.firstName || '',
        lastName: this.userInfo.lastName || '',
        phone: this.userInfo.phone || '',
        currency: this.userInfo.currency || 'USD'
      };
      
      this.showEditProfileDialog = true;
    },
    
    // 更新个人资料
    updateProfile() {
      if (!this.profileFormRef) {
        return;
      }
      
      this.profileFormRef.validate(async (valid) => {
        if (valid) {
          this.isLoading = true;
          
          try {
            // 构造更新数据，确保字段名与后端VO类匹配
            const profileData = {
              firstName: this.profileForm.firstName,
              lastName: this.profileForm.lastName,
              phoneNumber: this.profileForm.phone, // 确保与后端VO类字段名匹配
              currency: this.profileForm.currency,
              email: this.userInfo.email, // 使用邮箱作为用户标识
              // 确保不缺少任何必要的字段
              username: this.userInfo.userName || this.userInfo.username // 用户名字段
            };
            
            // 调用更新API
            const response = await changeUserInformation(profileData);
            
            if (response && response.code === 200) {
              ElMessage({
                message: 'Profile updated successfully',
                type: 'success',
                duration: 3000
              });
              
              // 更新本地存储的用户信息
              const updatedUserInfo = {
                ...this.userInfo,
                firstName: this.profileForm.firstName,
                lastName: this.profileForm.lastName,
                phone: this.profileForm.phone, // 保持本地存储中使用phone字段
                currency: this.profileForm.currency
              };
              
              localStorage.setItem('userInfo', JSON.stringify(updatedUserInfo));
              this.userInfo = updatedUserInfo;
              
              // 关闭对话框
              this.showEditProfileDialog = false;
              
              // 刷新用户数据
              this.loadUserInfo();
            } else {
              ElMessage({
                message: response?.msg || 'Failed to update profile',
                type: 'error',
                duration: 3000
              });
            }
          } catch (error) {
            ElMessage.error(`Failed to update profile: ${error.message}`);
          } finally {
            this.isLoading = false;
          }
        }
      });
    },
    
    // 修改密码
    changePassword() {
      if (!this.passwordFormRef) return;
      
      this.passwordFormRef.validate(async (valid) => {
        if (valid) {
          // Check password strength
          const passwordStrength = this.checkPasswordStrength(this.passwordForm.newPassword);
          if (passwordStrength.class === 'very-weak' || passwordStrength.class === 'weak') {
            ElMessage({
              message: 'Please use a stronger password',
              type: 'warning',
              duration: 3000
            });
            return;
          }
          
          this.isLoading = true;
          
          try {
            // 构造请求数据
            const passwordData = {
              email: this.userInfo.email, // 使用email代替userId
              currentPassword: this.passwordForm.currentPassword,
              newPassword: this.passwordForm.newPassword
            };
            
            // 调用修改密码接口
            const response = await updatePassword(passwordData);
            
            if (response && response.code === 200) {
              ElMessage({
                message: 'Password updated successfully',
                type: 'success',
                duration: 3000
              });
              
              // 重置表单
              this.passwordForm = {
                currentPassword: '',
                newPassword: '',
                confirmPassword: ''
              };
              
              // 关闭对话框
              this.showChangePasswordDialog = false;
            } else {
              ElMessage({
                message: response?.msg || 'Failed to update password',
                type: 'error',
                duration: 3000
              });
            }
          } catch (error) {
            ElMessage.error('An error occurred while updating your password');
          } finally {
            this.isLoading = false;
          }
        }
      });
    },
    
    // Helper method to check password strength
    checkPasswordStrength(password) {
      let strength = 0;
      let label = '';
      let className = '';
      
      // If password is empty, return early
      if (!password) {
        return {
          percentage: 0,
          label: '',
          class: ''
        };
      }

      // Increase strength for length
      if (password.length >= 8) {
        strength += 1;
      }
      if (password.length >= 12) {
        strength += 1;
      }

      // Increase strength for character variety
      if (/[A-Z]/.test(password)) {
        strength += 1;
      }
      if (/[a-z]/.test(password)) {
        strength += 1;
      }
      if (/[0-9]/.test(password)) {
        strength += 1;
      }
      if (/[^A-Za-z0-9]/.test(password)) {
        strength += 1;
      }

      // Calculate percentage and determine label
      const percentage = Math.min(100, Math.round((strength / 6) * 100));
      
      if (percentage <= 20) {
        label = 'Very Weak';
        className = 'very-weak';
      } else if (percentage <= 40) {
        label = 'Weak';
        className = 'weak';
      } else if (percentage <= 60) {
        label = 'Medium';
        className = 'medium';
      } else if (percentage <= 80) {
        label = 'Strong';
        className = 'strong';
      } else {
        label = 'Very Strong';
        className = 'very-strong';
      }

      return {
        percentage,
        label,
        class: className
      };
    },
    
    // 退出登录
    logout() {
      // 清除登录信息
      localStorage.removeItem('isLoggedIn');
      localStorage.removeItem('userInfo');
      
      // 发送登出事件
      emitter.emit('logout-success');
      
      ElMessage({
        message: 'You have been logged out successfully',
        type: 'success',
        duration: 3000
      });
      
      // 重定向到首页
      this.$router.push('/');
    },
    handleUploadAvatar() {
      // 触发文件选择器打开
      this.$refs.avatarInput.click();
    },
    async handleAvatarFileChange(event) {
      const file = event.target.files[0];
      if (!file) return;

      // 文件类型和大小验证保持不变...

      this.isUploadingAvatar = true;

      try {
        const formData = new FormData();
        formData.append('file', file);
        
        // 调用上传接口
        const response = await uploadImage(formData);

        if (response && response.code === 200) {
          // 直接从响应对象获取url（注意：根据实际结构可能需要response.url）
          const avatarUrl = response.url; 

          // 构造更新用户信息的参数（根据后端要求调整字段名）
          const userData = {
            email: this.userInfo.email,
            username: this.userInfo.userName || this.userInfo.username,
            avatarUrl: avatarUrl  // 字段名可能需要改为avatarUrl（根据后端接口定义）
          };
          
          // 调用更新接口
          const updateResponse = await changeUserInformation(userData);
          
          if (updateResponse && updateResponse.code === 200) {
            // 更新本地数据
            this.userInfo.userAvatar = avatarUrl;
            localStorage.setItem('userInfo', JSON.stringify(this.userInfo));
            
            // 强制刷新头像显示
            const avatar = document.querySelector('.profile-avatar');
            if (avatar) {
              avatar.src = avatarUrl + '?t=' + Date.now(); // 加时间戳避免缓存
            }
            
            // 使用事件总线通知应用程序更新头像
            emitter.emit('avatar-updated', { userAvatar: avatarUrl });
            
            ElMessage.success(this.$t('profile.messages.avatarUploadSuccess'));
            this.loadUserInfo(); // 刷新用户数据
          } else {
            throw new Error(updateResponse?.msg || '头像信息更新失败');
          }
        } else {
          throw new Error(response?.msg || '上传失败');
        }
      } catch (error) {
        ElMessage.error(this.$t('profile.messages.avatarUploadFailed'));
      } finally {
        this.isUploadingAvatar = false;
        this.$refs.avatarInput.value = '';
      }
    }
  },
  computed: {
    passwordStrength() {
      if (!this.passwordForm.newPassword) {
        return {
          percentage: 0,
          label: '',
          class: ''
        };
      }
      return this.checkPasswordStrength(this.passwordForm.newPassword);
    },
    passwordStrengthPercent() {
      return this.passwordStrength.percentage;
    },
    passwordStrengthLabel() {
      return this.passwordStrength.label;
    },
    passwordStrengthClass() {
      return this.passwordStrength.class;
    }
  }
};
</script>

<style scoped>
.profile-page {
  padding: 2rem;
  background: transparent;
  min-height: 100vh;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

/* 深色主题样式 */
[data-theme="dark"] .profile-page {
  color: #e0e0e0;
}

/* 浅色主题样式 */
[data-theme="light"] .profile-page {
  color: #333;
}

.profile-container {
  max-width: 900px;
  margin: 0 auto;
  padding: 2rem;
  border-radius: 15px;
  transition: all 0.3s ease;
}

/* 深色主题容器样式 */
[data-theme="dark"] .profile-container {
  background: rgba(30, 30, 35, 0.9);
  border: 1px solid rgba(80, 80, 100, 0.3);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.4);
}

/* 浅色主题容器样式 */
[data-theme="light"] .profile-container {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(200, 200, 220, 0.3);
  box-shadow: 0 5px 15px rgba(100, 100, 100, 0.1);
}

.profile-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  flex-wrap: wrap;
  gap: 2.5rem;
  border-radius: 12px;
  padding: 1.2rem 2.5rem 1.2rem 2.5rem;
  transition: all 0.3s ease;
}

/* 深色主题头部样式 */
[data-theme="dark"] .profile-header {
  border-bottom: 1px solid rgba(120, 70, 200, 0.3);
  background: linear-gradient(90deg, #2d1e4a 0%, #7b88ff22 100%);
  box-shadow: 0 2px 12px #7b88ff22;
}

/* 浅色主题头部样式 */
[data-theme="light"] .profile-header {
  border-bottom: 1px solid rgba(142, 45, 226, 0.2);
  background: linear-gradient(90deg, #f0d5fc 0%, #e8d0f522 100%);
  box-shadow: 0 2px 12px rgba(142, 45, 226, 0.1);
}

.profile-header h1 {
  font-size: 1.7rem;
  margin: 0 0 0 0.2rem;
  letter-spacing: 2px;
  transition: all 0.3s ease;
}

/* 深色主题标题样式 */
[data-theme="dark"] .profile-header h1 {
  color: #c3a3ff;
  text-shadow: 0 0 12px #a080ff55;
}

/* 浅色主题标题样式 */
[data-theme="light"] .profile-header h1 {
  color: #8e2de2;
  text-shadow: 0 0 12px rgba(142, 45, 226, 0.2);
}

.home-btn {
  padding: 0.6rem 1.5rem;
  border-radius: 30px;
  border: none;
  font-weight: 600;
  cursor: pointer;
  text-decoration: none;
  transition: all 0.3s, box-shadow 0.3s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  white-space: nowrap;
  margin-right: 0.2rem;
}

/* 深色主题按钮样式 */
[data-theme="dark"] .home-btn {
  background: linear-gradient(135deg, #7b88ff, #c4a4ff);
  color: #fff;
  box-shadow: 0 2px 8px #a080ff33;
}

/* 浅色主题按钮样式 */
[data-theme="light"] .home-btn {
  background: linear-gradient(135deg, #9d4de8, #8e2de2);
  color: #fff;
  box-shadow: 0 2px 8px rgba(142, 45, 226, 0.2);
}

/* 深色主题按钮悬停样式 */
[data-theme="dark"] .home-btn:hover {
  background: linear-gradient(135deg, #c4a4ff, #7b88ff);
  transform: translateY(-2px) scale(1.04);
  box-shadow: 0 5px 15px #a080ff55;
}

/* 浅色主题按钮悬停样式 */
[data-theme="light"] .home-btn:hover {
  background: linear-gradient(135deg, #8e2de2, #9d4de8);
  transform: translateY(-2px) scale(1.04);
  box-shadow: 0 5px 15px rgba(142, 45, 226, 0.3);
}

.profile-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  margin-bottom: 1rem;
}

.avatar-wrapper {
  position: relative;
  margin-bottom: 1rem;
  width: 140px;
  height: 140px;
  border-radius: 50%;
  overflow: visible;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

/* 深色主题头像容器样式 */
[data-theme="dark"] .avatar-wrapper {
  box-shadow: 0 0 30px 8px #a080ff55, 0 0 0 8px #2d1e4a33;
  background: linear-gradient(135deg, #7b88ff 0%, #c4a4ff 100%);
}

/* 浅色主题头像容器样式 */
[data-theme="light"] .avatar-wrapper {
  box-shadow: 0 0 30px 8px rgba(142, 45, 226, 0.2), 0 0 0 8px rgba(232, 208, 245, 0.3);
  background: linear-gradient(135deg, #9d4de8 0%, #8e2de2 100%);
}

.profile-avatar {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
  transition: transform 0.3s;
}

/* 深色主题头像样式 */
[data-theme="dark"] .profile-avatar {
  border: 4px solid #fff;
  box-shadow: 0 0 20px #a080ff88;
}

/* 浅色主题头像样式 */
[data-theme="light"] .profile-avatar {
  border: 4px solid #fff;
  box-shadow: 0 0 20px rgba(142, 45, 226, 0.3);
}

.avatar-wrapper:hover .profile-avatar {
  transform: scale(1.05);
}

.upload-avatar-icon-btn {
  position: absolute;
  bottom: 10px;
  right: 10px;
  width: 38px;
  height: 38px;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 18px;
  transition: background 0.3s, transform 0.2s;
  z-index: 2;
}

/* 深色主题上传按钮样式 */
[data-theme="dark"] .upload-avatar-icon-btn {
  background: linear-gradient(135deg, #7b88ff, #c4a4ff);
  color: #fff;
  box-shadow: 0 2px 8px #a080ff55;
}

/* 浅色主题上传按钮样式 */
[data-theme="light"] .upload-avatar-icon-btn {
  background: linear-gradient(135deg, #9d4de8, #8e2de2);
  color: #fff;
  box-shadow: 0 2px 8px rgba(142, 45, 226, 0.2);
}

/* 深色主题上传按钮悬停样式 */
[data-theme="dark"] .upload-avatar-icon-btn:hover {
  background: linear-gradient(135deg, #c4a4ff, #7b88ff);
  transform: scale(1.1);
}

/* 浅色主题上传按钮悬停样式 */
[data-theme="light"] .upload-avatar-icon-btn:hover {
  background: linear-gradient(135deg, #8e2de2, #9d4de8);
  transform: scale(1.1);
}

.upload-avatar-text {
  margin: 10px 0;
  font-size: 0.95rem;
  text-align: center;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
}

/* 深色主题上传文本样式 */
[data-theme="dark"] .upload-avatar-text {
  color: #bb86fc;
}

/* 浅色主题上传文本样式 */
[data-theme="light"] .upload-avatar-text {
  color: #8e2de2;
}

.profile-username {
  margin: 0.5rem 0;
  font-size: 1.6rem;
  font-weight: 700;
  word-break: break-word;
  transition: all 0.3s ease;
}

/* 深色主题用户名样式 */
[data-theme="dark"] .profile-username {
  color: #fff;
  text-shadow: 0 0 8px #a080ff55;
}

/* 浅色主题用户名样式 */
[data-theme="light"] .profile-username {
  color: #333;
  text-shadow: 0 0 8px rgba(142, 45, 226, 0.2);
}

.profile-email {
  margin: 0;
  font-size: 1.1rem;
  word-break: break-word;
  transition: all 0.3s ease;
}

/* 深色主题邮箱样式 */
[data-theme="dark"] .profile-email {
  color: #bb86fc;
}

/* 浅色主题邮箱样式 */
[data-theme="light"] .profile-email {
  color: #8e2de2;
}

.info-section, .account-section {
  width: 100%;
}

.info-card {
  border-radius: 18px;
  padding: 2rem 1.5rem;
  margin-bottom: 1.5rem;
  backdrop-filter: blur(8px);
  transition: all 0.3s ease;
}

/* 深色主题卡片样式 */
[data-theme="dark"] .info-card {
  background: rgba(41, 31, 77, 0.85);
  border: 1.5px solid #a080ff44;
  box-shadow: 0 8px 32px 0 #7b88ff22, 0 1.5px 8px #a080ff33;
}

/* 浅色主题卡片样式 */
[data-theme="light"] .info-card {
  background: rgba(255, 255, 255, 0.85);
  border: 1.5px solid rgba(142, 45, 226, 0.2);
  box-shadow: 0 8px 32px 0 rgba(142, 45, 226, 0.1), 0 1.5px 8px rgba(142, 45, 226, 0.15);
}

.info-card h3 {
  font-size: 1.3rem;
  margin-bottom: 1.2rem;
  letter-spacing: 1px;
  transition: all 0.3s ease;
}

/* 深色主题卡片标题样式 */
[data-theme="dark"] .info-card h3 {
  color: #bb86fc;
  text-shadow: 0 0 8px #a080ff55;
}

/* 浅色主题卡片标题样式 */
[data-theme="light"] .info-card h3 {
  color: #8e2de2;
  text-shadow: 0 0 8px rgba(142, 45, 226, 0.2);
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.7rem 0;
  font-size: 1.05rem;
  transition: all 0.3s ease;
}

/* 深色主题信息项样式 */
[data-theme="dark"] .info-item {
  border-bottom: 1px solid #a080ff22;
}

/* 浅色主题信息项样式 */
[data-theme="light"] .info-item {
  border-bottom: 1px solid rgba(142, 45, 226, 0.1);
}

.info-item:last-child {
  border-bottom: none;
}

.info-item label {
  font-weight: 500;
  transition: all 0.3s ease;
}

/* 深色主题标签样式 */
[data-theme="dark"] .info-item label {
  color: #bb86fc;
}

/* 浅色主题标签样式 */
[data-theme="light"] .info-item label {
  color: #8e2de2;
}

.info-value {
  font-weight: 600;
  transition: all 0.3s ease;
}

/* 深色主题值样式 */
[data-theme="dark"] .info-value {
  color: #fff;
}

/* 浅色主题值样式 */
[data-theme="light"] .info-value {
  color: #333;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.action-btn {
  border: none;
  border-radius: 8px;
  padding: 0.8rem 1.2rem;
  font-weight: 600;
  margin-bottom: 0.7rem;
  transition: background 0.3s, transform 0.2s;
}

/* 深色主题按钮样式 */
[data-theme="dark"] .action-btn {
  background: linear-gradient(135deg, #7b88ff, #c4a4ff);
  color: #fff;
  box-shadow: 0 2px 8px #a080ff33;
}

/* 浅色主题按钮样式 */
[data-theme="light"] .action-btn {
  background: linear-gradient(135deg, #9d4de8, #8e2de2);
  color: #fff;
  box-shadow: 0 2px 8px rgba(142, 45, 226, 0.2);
}

/* 深色主题按钮悬停样式 */
[data-theme="dark"] .action-btn:hover {
  background: linear-gradient(135deg, #c4a4ff, #7b88ff);
  transform: translateY(-2px) scale(1.04);
}

/* 浅色主题按钮悬停样式 */
[data-theme="light"] .action-btn:hover {
  background: linear-gradient(135deg, #8e2de2, #9d4de8);
  transform: translateY(-2px) scale(1.04);
}

/* 深色主题退出按钮样式 */
[data-theme="dark"] .logout-btn {
  background: linear-gradient(135deg, #ff71ce, #a080ff);
  color: #fff;
}

/* 浅色主题退出按钮样式 */
[data-theme="light"] .logout-btn {
  background: linear-gradient(135deg, #ff5c8a, #8e2de2);
  color: #fff;
}

/* 深色主题退出按钮悬停样式 */
[data-theme="dark"] .logout-btn:hover {
  background: linear-gradient(135deg, #a080ff, #ff71ce);
}

/* 浅色主题退出按钮悬停样式 */
[data-theme="light"] .logout-btn:hover {
  background: linear-gradient(135deg, #8e2de2, #ff5c8a);
}

/* 对话框样式 */
:deep(.el-dialog) {
  border-radius: 18px !important;
  transition: all 0.3s ease;
}

/* 深色主题对话框样式 */
[data-theme="dark"] :deep(.el-dialog) {
  background: rgba(41, 31, 77, 0.98) !important;
  box-shadow: 0 8px 32px 0 #7b88ff22, 0 1.5px 8px #a080ff33;
  border: 1.5px solid #a080ff44;
}

/* 浅色主题对话框样式 */
[data-theme="light"] :deep(.el-dialog) {
  background: rgba(255, 255, 255, 0.98) !important;
  box-shadow: 0 8px 32px 0 rgba(142, 45, 226, 0.1), 0 1.5px 8px rgba(142, 45, 226, 0.15);
  border: 1.5px solid rgba(142, 45, 226, 0.2);
}

:deep(.el-dialog__header) {
  border-radius: 18px 18px 0 0;
  text-align: center;
  font-size: 1.3rem;
  letter-spacing: 1px;
}

/* 深色主题对话框头部样式 */
[data-theme="dark"] :deep(.el-dialog__header) {
  background: linear-gradient(135deg, #7b88ff, #c4a4ff);
  color: #fff;
}

/* 浅色主题对话框头部样式 */
[data-theme="light"] :deep(.el-dialog__header) {
  background: linear-gradient(135deg, #9d4de8, #8e2de2);
  color: #fff;
}

:deep(.el-input__wrapper) {
  transition: all 0.3s ease;
}

/* 深色主题输入框样式 */
[data-theme="dark"] :deep(.el-input__wrapper) {
  background: rgba(41, 31, 77, 0.85) !important;
  border: 1.5px solid #a080ff44 !important;
  color: #fff !important;
  box-shadow: 0 1.5px 8px #a080ff22;
}

/* 浅色主题输入框样式 */
[data-theme="light"] :deep(.el-input__wrapper) {
  background: rgba(255, 255, 255, 0.85) !important;
  border: 1.5px solid rgba(142, 45, 226, 0.2) !important;
  color: #333 !important;
  box-shadow: 0 1.5px 8px rgba(142, 45, 226, 0.1);
}

/* 深色主题输入框焦点样式 */
[data-theme="dark"] :deep(.el-input__wrapper:focus-within) {
  border-color: #bb86fc !important;
  box-shadow: 0 0 8px #bb86fc55;
}

/* 浅色主题输入框焦点样式 */
[data-theme="light"] :deep(.el-input__wrapper:focus-within) {
  border-color: #8e2de2 !important;
  box-shadow: 0 0 8px rgba(142, 45, 226, 0.3);
}

/* 响应式优化 */
@media (max-width: 768px) {
  .profile-page {
    padding: 1rem;
  }
  .profile-container {
    padding: 1.2rem;
  }
  .profile-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1.2rem;
    padding: 1rem 1rem 1rem 1rem;
  }
  .profile-header h1 {
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
  }
  .home-btn {
    padding: 0.5rem 1.1rem;
    font-size: 0.95rem;
    margin-right: 0;
  }
  .avatar-wrapper {
    width: 100px;
    height: 100px;
  }
  .profile-avatar {
    width: 80px;
    height: 80px;
  }
  .upload-avatar-icon-btn {
    width: 28px;
    height: 28px;
    font-size: 14px;
    bottom: 4px;
    right: 4px;
  }
  .profile-username {
    font-size: 1.2rem;
  }
  .profile-email {
    font-size: 0.95rem;
  }
  .info-card {
    padding: 1.2rem 0.7rem;
    border-radius: 12px;
  }
  .info-card h3 {
    font-size: 1.1rem;
  }
  .info-item {
    font-size: 0.95rem;
    padding: 0.5rem 0;
  }
  .action-btn {
    padding: 0.7rem 1rem;
    font-size: 0.95rem;
  }
}
@media (max-width: 480px) {
  .profile-container {
    padding: 0.5rem;
    border-radius: 8px;
  }
  .avatar-wrapper {
    width: 70px;
    height: 70px;
  }
  .profile-avatar {
    width: 56px;
    height: 56px;
  }
  .upload-avatar-icon-btn {
    width: 20px;
    height: 20px;
    font-size: 11px;
    bottom: 2px;
    right: 2px;
  }
  .profile-username {
    font-size: 1rem;
  }
  .profile-email {
    font-size: 0.8rem;
  }
  .info-card {
    padding: 0.7rem 0.3rem;
    border-radius: 7px;
  }
  .info-card h3 {
    font-size: 0.95rem;
  }
  .info-item {
    font-size: 0.8rem;
    padding: 0.3rem 0;
  }
  .action-btn {
    padding: 0.5rem 0.7rem;
    font-size: 0.8rem;
  }
}
</style>
