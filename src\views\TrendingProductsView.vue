<template>
  <div class="trending-products-view">
    <div class="dark-bg" :class="{ 'light-bg': !isDarkMode }">
      <!-- 添加背景遮罩 -->
      <div class="dropdown-backdrop" v-if="isMobile && selectedCategory !== null" @click="selectedCategory = null"></div>
      
      <!-- 添加"Beyond The Spreadsheet"区域 -->
      <div class="hero-section">
        <div class="hero-content">
          <h1 class="hero-title">Beyond The Spreadsheet</h1>
          <p class="hero-subtitle">Every Product has QC</p>
          
          <div class="search-container">
            <span class="search-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="11" cy="11" r="8"></circle>
                <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
              </svg>
            </span>
            <input type="text" placeholder="Search" class="search-input" v-model="searchQuery" @keyup.enter="handleSearch">
          </div>
          
          <!-- 移动端分类和过滤按钮 -->
          <div class="mobile-filter-category" v-if="isMobile">
            <div class="filter-icon" @click="toggleFilterDropdown">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" :class="{ 'rotate': showFilterDropdown }">
                <polygon points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"></polygon>
              </svg>
            </div>
            
            <button class="category-btn" @click="toggleCategoryMenu">
              CATEGORY
            </button>
            
            <!-- 移动端过滤下拉框 -->
            <div v-if="showFilterDropdown && isMobile" class="mobile-filter-dropdown">
              <div class="filter-options">
                <button class="filter-option" :class="{ 'active': activeFilter === 'recommend' }" @click="setActiveFilter('recommend')">
                  <span>Recommend</span>
                </button>
                <button class="filter-option" :class="{ 'active': activeFilter === 'favorite' }" @click="setActiveFilter('favorite')">
                  <span>Favorite</span>
                </button>
                <button class="filter-option" :class="{ 'active': activeFilter === 'amountSold' }" @click="setActiveFilter('amountSold')">
                  <span>Amount Sold</span>
                </button>
                <button class="filter-option" :class="{ 'active': activeFilter === 'price' }" @click="setActiveFilter('price')">
                  <span>Price</span>
                </button>
                <div class="filter-option seller-option" :class="{ 'active': activeFilter === 'seller' }" @click="setActiveFilter('seller')">
                  <span>Seller</span>
                  <div class="arrow-container">
                    <svg class="arrow-up" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" @click.stop="setSortDirection('asc')">
                      <polyline points="18 15 12 9 6 15"></polyline>
                    </svg>
                    <svg class="arrow-down" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" @click.stop="setSortDirection('desc')">
                      <polyline points="6 9 12 15 18 9"></polyline>
                    </svg>
                  </div>
                </div>
                <button class="filter-option refresh" @click="refreshFilter">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="refresh-icon">
                    <path d="M23 4v6h-6"></path>
                    <path d="M20.49 15a9 9 0 11-2.12-9.36L23 10"></path>
                  </svg>
                </button>
              </div>
            </div>
          </div>
          
          <div class="category-filter">
            <!-- 添加背景遮罩 -->
            <div v-if="showFilterDropdown" class="filter-dropdown-backdrop" @click="showFilterDropdown = false"></div>
            
            <!-- PC端过滤图标 -->
            <div class="filter-icon" v-if="!isMobile" @click="toggleFilterDropdown">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" :class="{ 'rotate': showFilterDropdown }">
                <polygon points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"></polygon>
              </svg>
            </div>
            
            <!-- PC端过滤下拉框 -->
            <div v-if="showFilterDropdown && !isMobile" class="filter-dropdown">
              <div class="filter-options">
                <button class="filter-option" :class="{ 'active': activeFilter === 'recommend' }" @click="setActiveFilter('recommend')">
                  <span>Recommend</span>
                </button>
                <button class="filter-option" :class="{ 'active': activeFilter === 'favorite' }" @click="setActiveFilter('favorite')">
                  <span>Favorite</span>
                </button>
                <button class="filter-option" :class="{ 'active': activeFilter === 'amountSold' }" @click="setActiveFilter('amountSold')">
                  <span>Amount Sold</span>
                </button>
                <button class="filter-option" :class="{ 'active': activeFilter === 'price' }" @click="setActiveFilter('price')">
                  <span>Price</span>
                </button>
                <div class="filter-option seller-option" :class="{ 'active': activeFilter === 'seller' }" @click="setActiveFilter('seller')">
                  <span>Seller</span>
                  <div class="arrow-container">
                    <svg class="arrow-up" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" @click.stop="setSortDirection('asc')">
                      <polyline points="18 15 12 9 6 15"></polyline>
                    </svg>
                    <svg class="arrow-down" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" @click.stop="setSortDirection('desc')">
                      <polyline points="6 9 12 15 18 9"></polyline>
                    </svg>
                  </div>
                </div>
                <button class="filter-option refresh" @click="refreshFilter">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="refresh-icon">
                    <path d="M23 4v6h-6"></path>
                    <path d="M20.49 15a9 9 0 11-2.12-9.36L23 10"></path>
                  </svg>
                </button>
              </div>
            </div>
            
            <!-- PC端分类标签 -->
            <div class="filter-chips-container" v-if="!isMobile">
              <div class="filter-chips">
                <div 
                  v-for="(category, index) in categories" 
                  :key="`cat-${index}`" 
                  class="filter-chip"
                  :class="{ 'active': selectedCategory === category.id }"
                  @click="toggleCategoryDropdown(category.id)"
                >
                  {{ category.name }}
                  <span v-if="selectedCategory === category.id" class="dropdown-arrow">▼</span>
                  <span v-else class="dropdown-arrow">▼</span>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 品牌下拉菜单 - 作为单独元素展示 -->
          <div 
            class="brands-dropdown-container" 
            v-if="selectedCategory !== null && categoryBrands.length > 0" 
            :style="dropdownPosition"
            :class="{ 'mobile-dropdown': isMobile }"
          >
            <div class="brands-dropdown">
              <div class="dropdown-header" v-if="isMobile">
                <span>选择品牌</span>
                <button class="close-btn" @click="selectedCategory = null">✕</button>
              </div>
              <div 
                v-for="(brand, bIndex) in categoryBrands" 
                :key="`brand-${bIndex}`" 
                class="brand-dropdown-item"
                :class="{ 'active': selectedBrand === brand.brandId }"
                @click="selectBrand(brand.brandId)"
              >
                {{ brand.name }}
              </div>
            </div>
          </div>
        </div>
        
        <!-- 修改hero section中的轮播图部分 -->
        <div class="hero-slider" v-show="!isMobile">
          <!-- 轮播图加载状态 -->
          <div v-if="bannersLoading" class="slider-loading">
            <div class="loading-spinner"></div>
            <p>加载轮播图中...</p>
          </div>
          
          <!-- 轮播图错误状态 -->
          <div v-else-if="bannersError" class="slider-error">
            <p>{{ bannersError }}</p>
            <button class="retry-btn" @click="fetchBanners">重试</button>
          </div>
          
          <!-- 轮播图内容 -->
          <template v-else>
          <div class="slider-controls">
              <button class="slider-arrow prev" @click="prevBanner">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="15 18 9 12 15 6"></polyline>
              </svg>
            </button>
              <button class="slider-arrow next" @click="nextBanner(true)">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="9 18 15 12 9 6"></polyline>
              </svg>
            </button>
          </div>
            
            <!-- 桌面轮播图内容 -->
            <div class="slider-content" 
              :style="currentBanner.isVideo ? {} : getCurrentBannerStyle"
              @click="handleBannerClick(currentBanner)"
            >
              <!-- 视频元素 -->
              <video 
                v-if="currentBanner.isVideo" 
                :id="`banner-video-${currentBannerIndex}`"
                class="banner-video" 
                :src="currentBanner.videoUrl" 
                :poster="currentBanner.postImage"
                preload="metadata"
                muted
                @play="handleVideoPlayPause"
                @pause="handleVideoPlayPause"
                @ended="handleVideoEnded"
              ></video>
              
              <div class="slider-overlay"></div>
              <h2 class="slider-title">{{ currentBanner.postTitle || 'This is a title anything you can type' }}</h2>
              <!-- 桌面轮播图内容的图标部分 -->
              <div class="slider-actions" @click.stop>
                <!-- 1. 喜欢/点赞图标 - postLikes -->
              <button class="action-button">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
                </svg>
                  <span class="count">{{ currentBanner.postLikes || '0' }}</span>
              </button>
                
                <!-- 2. 评论图标 - commentCount -->
              <button class="action-button">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                </svg>
                  <span class="count">{{ currentBanner.commentCount || '0' }}</span>
              </button>
                
                <!-- 3. 查看图标 - postViews -->
              <button class="action-button">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                  <circle cx="12" cy="12" r="3"></circle>
                </svg>
                  <span class="count">{{ currentBanner.postViews || '0' }}</span>
              </button>
                
                <!-- 4. 分享图标 - 固定值或不显示值 -->
              <button class="action-button">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <line x1="22" y1="2" x2="11" y2="13"></line>
                  <polygon points="22 2 15 22 11 13 2 9 22 2"></polygon>
                </svg>
              </button>
            </div>
          </div>
            
            <!-- 轮播图指示器 -->
            <div class="slider-indicators" v-if="banners.length > 1">
              <span 
                v-for="(banner, index) in banners" 
                :key="banner.id" 
                class="indicator"
                :class="{ active: currentBannerIndex === index }"
                @click="handleIndicatorClick(index)"
              ></span>
            </div>
          </template>
        </div>
        
        <!-- 移动端轮播图 -->
        <div class="mobile-hero-slider" v-show="isMobile">
          <!-- 移动端轮播图加载状态 -->
          <div v-if="bannersLoading" class="slider-loading">
            <div class="loading-spinner"></div>
            <p>加载轮播图中...</p>
          </div>
          
          <!-- 移动端轮播图错误状态 -->
          <div v-else-if="bannersError" class="slider-error">
            <p>{{ bannersError }}</p>
            <button class="retry-btn" @click="fetchBanners">重试</button>
          </div>
          
          <!-- 移动端轮播图内容 -->
          <template v-else>
            <button class="mobile-slider-arrow prev" @click="prevBanner">
            <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round">
              <polyline points="15 18 9 12 15 6"></polyline>
            </svg>
          </button>
            <button class="mobile-slider-arrow next" @click="nextBanner(true)">
            <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round">
              <polyline points="9 18 15 12 9 6"></polyline>
            </svg>
          </button>
          
            <!-- 移动端轮播图内容 -->
            <div class="mobile-slider-content" 
              :style="currentBanner.isVideo ? {} : getCurrentBannerStyle"
              @click="handleBannerClick(currentBanner)"
            >
              <!-- 移动端视频元素 -->
              <video 
                v-if="currentBanner.isVideo" 
                :id="`banner-video-mobile-${currentBannerIndex}`"
                class="banner-video" 
                :src="currentBanner.videoUrl" 
                :poster="currentBanner.postImage"
                preload="metadata"
                muted
                playsinline
                webkit-playsinline="true"
                x5-playsinline="true"
                x5-video-player-type="h5"
                x5-video-player-fullscreen="true"
                x-webkit-airplay="allow"
                @play="handleVideoPlayPause"
                @pause="handleVideoPlayPause"
                @ended="handleVideoEnded"
              ></video>
              
              <div class="slider-overlay"></div>
              <h2 class="mobile-slider-title">{{ currentBanner.postTitle || 'This is a title anything you can type' }}</h2>
              <!-- 移动端轮播图内容的图标部分 -->
              <div class="mobile-slider-actions" @click.stop>
                <!-- 1. 喜欢/点赞图标 - postLikes -->
              <button class="mobile-action-button">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
                </svg>
                  <span class="count">{{ currentBanner.postLikes || '0' }}</span>
              </button>
                
                <!-- 2. 评论图标 - commentCount -->
              <button class="mobile-action-button">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                </svg>
                  <span class="count">{{ currentBanner.commentCount || '0' }}</span>
              </button>
                
                <!-- 3. 查看图标 - postViews -->
              <button class="mobile-action-button">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                  <circle cx="12" cy="12" r="3"></circle>
                </svg>
                  <span class="count">{{ currentBanner.postViews || '0' }}</span>
              </button>
                
                <!-- 4. 分享图标 - 固定值或不显示值 -->
              <button class="mobile-action-button">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
                  <line x1="22" y1="2" x2="11" y2="13"></line>
                  <polygon points="22 2 15 22 11 13 2 9 22 2"></polygon>
                </svg>
              </button>
            </div>
          </div>
            
            <!-- 移动端轮播图指示器 -->
            <div class="mobile-slider-indicators" v-if="banners.length > 1">
              <span 
                v-for="(banner, index) in banners" 
                :key="banner.id" 
                class="indicator"
                :class="{ active: currentBannerIndex === index }"
                @click="handleIndicatorClick(index)"
              ></span>
            </div>
          </template>
        </div>
      </div>
      
      <!-- 现有的TRENDING NOW区域 -->
      <div class="trending-products-container">
        <div class="header-section">
          <h2 class="trending-title">TRENDING NOW</h2>
          <div class="discount-code">Discount Code: OMGBUY 15% OFF</div>
        </div>
        
        <div class="products-grid">
          <ProductCard 
            v-for="(product, index) in products" 
            :key="`product-${index}`" 
            :product="product" 
            @click="goToProductDetail(product.productId || product.id)"
          />
          <!-- 添加"更多"卡片 -->
          <div class="more-card" @click="$router.push('/products')">
            <div class="more-content">
              <div class="more-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <line x1="12" y1="5" x2="12" y2="19"></line>
                  <line x1="5" y1="12" x2="19" y2="12"></line>
                </svg>
              </div>
              <div class="more-text">{{ $t('common.more') || 'More' }}</div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 新增 FRESHLY QC 部分 -->
      <div class="freshly-qc-container">
        <h2 class="freshly-qc-title">FRESHLY QC</h2>
        
        <div v-if="qcLoading" class="qc-loading">
          <div class="loading-spinner"></div>
          <p>加载QC数据中...</p>
        </div>
        
        <div v-else-if="validQcGroups && validQcGroups.length > 0" class="qc-groups-grid">
          <!-- 轮播控制按钮 -->
          <div class="qc-carousel-controls">
            <button class="qc-carousel-btn prev" @click="nextImage">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="15 18 9 12 15 6"></polyline>
              </svg>
            </button>
            <button class="qc-carousel-btn next" @click="nextImage">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="9 18 15 12 9 6"></polyline>
              </svg>
            </button>
          </div>
          
          <!-- 各个QC组 -->
          <div 
            class="qc-group" 
            v-for="(group, groupIndex) in validQcGroups" 
            :key="groupIndex"
          >
            <template v-if="group.length > 0">
              <div class="qc-group-header">
                <span>{{ group[0]?.groupName || `Group ${groupIndex + 1}` }}</span>
                <span class="group-count">
                  {{ (currentImageIndex % group.length) + 1 }}/{{ group.length }}
                </span>
              </div>
              <div class="qc-group-content">
                <img 
                  :src="getImageUrl(group, currentImageIndex)" 
                  :alt="getImageAlt(group, currentImageIndex)"
                  class="qc-image"
                  @error="handleImageError"
                >
                <!-- 移动端每个QC组单独的箭头控制 -->
                <div class="qc-item-controls" v-if="isMobile">
                  <button class="qc-item-btn prev" @click="nextImage">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <polyline points="15 18 9 12 15 6"></polyline>
                    </svg>
                  </button>
                  <button class="qc-item-btn next" @click="nextImage">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <polyline points="9 18 15 12 9 6"></polyline>
                    </svg>
                  </button>
                </div>
              </div>
            </template>
          </div>
        </div>
        
        <div v-else class="qc-empty">
          <p>暂无QC数据</p>
        </div>
      </div>
      
      <!-- 新增 CREATOR CHOICE 部分 -->
      <div class="creator-choice-container">
        <h2 class="creator-choice-title">CREATOR CHOICE</h2>
        
        <!-- 添加加载状态 -->
        <div v-if="videosLoading" class="creator-loading">
          <div class="loading-spinner"></div>
          <p>加载创作者视频中...</p>
        </div>
        
        <!-- 添加错误状态 -->
        <div v-else-if="videosError" class="creator-error">
          <p>{{ videosError }}</p>
          <button class="retry-btn" @click="fetchCreatorVideos">重试</button>
        </div>
        
        <!-- 正常显示状态 -->
        <div v-else class="creator-choice-carousel">
          <!-- 轮播控制按钮 -->
          <button class="carousel-arrow prev" @click="scrollCreatorCarousel('prev')">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <polyline points="15 18 9 12 15 6"></polyline>
            </svg>
          </button>
          <button class="carousel-arrow next" @click="scrollCreatorCarousel('next')">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <polyline points="9 18 15 12 9 6"></polyline>
            </svg>
          </button>
          
          <!-- 轮播内容 -->
          <div class="creator-choice-slides">
            <div class="creator-choice-slide" v-for="video in creatorVideos" :key="video.id">
              <div class="creator-video">
                <div class="video-container">
                  <div class="video-placeholder" @click="playCreatorVideo(video.id)">
                    <div class="play-button" :id="`play-button-${video.id}`">
                      <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="white" stroke="none">
                        <polygon points="5 3 19 12 5 21 5 3"></polygon>
                      </svg>
                    </div>
                    <img :src="video.thumbnail" alt="Creator video thumbnail" :id="`thumbnail-${video.id}`">
                    <video 
                      :id="`video-${video.id}`" 
                      :src="video.videoUrl" 
                      class="creator-video-element" 
                      preload="auto"
                      playsinline
                      controls
                      muted
                      webkit-playsinline
                      x5-playsinline
                    ></video>
                  </div>
                  <div class="creator-avatar">
                    <img :src="video.avatar" alt="Creator avatar">
                  </div>
                </div>
                <div class="creator-info">
                  <div class="creator-name">{{ video.username }}</div>
                  <div class="creator-description">{{ video.description }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 添加全屏分类菜单 -->
    <div class="category-fullscreen" v-if="showCategoryMenu">
      <div class="category-fullscreen-header">
        <h2>CATEGORY</h2>
        <button class="close-btn" @click="toggleCategoryMenu">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>
      
      <div class="category-fullscreen-content">
        <div class="category-sidebar">
          <!-- 动态分类列表 -->
          <div
            v-for="category in categories"
            :key="category.id"
            class="category-sidebar-item"
            :class="{ 'active': activeSidebarItem === category.id }"
            @click="setActiveSidebarItem(category.id)"
          >
            <div class="check-icon" v-if="activeSidebarItem === category.id">✓</div>
            <span :class="{ 'active-text': activeSidebarItem === category.id }">{{ category.name }}</span>
          </div>
        </div>
        
        <div class="category-content">
          <div class="category-grid">
            <div class="category-card" v-for="(item, index) in currentCategoryItems" :key="item.id || index" @click="selectCategoryItem(item)">
              <div class="category-image">
                <img :src="item.image || 'https://cdn.findqc.com/bt/20250607/mbme4p4h_lete.jpg'" :alt="item.name">
              </div>
              <div class="category-name">{{ item.name }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 商家选择弹窗 -->
    <div v-if="showSellersModal" class="sellers-modal-backdrop" @click="closeSellersModal">
      <div class="sellers-modal" @click.stop>
        <div class="sellers-modal-header">
          <h3>选择商家</h3>
          <button class="close-btn" @click="closeSellersModal">×</button>
        </div>

        <div class="sellers-modal-content">
          <div v-if="isLoadingSellers" class="loading-container">
            <div class="loading-spinner"></div>
            <p>加载商家数据中...</p>
          </div>

          <div v-else-if="sellers.length === 0" class="no-sellers">
            <p>暂无商家数据</p>
          </div>

          <div v-else class="sellers-grid">
            <!-- 全部商家选项 -->
            <div class="seller-item"
                :class="{ 'selected': !selectedSeller }"
                @click="selectSeller(null)">
              <div class="seller-logo all-sellers">
                <i class="fas fa-store"></i>
              </div>
              <div class="seller-info">
                <h4>所有商家</h4>
                <p>显示所有商品</p>
              </div>
            </div>

            <!-- 商家列表 -->
            <div v-for="seller in sellers"
                :key="seller.id"
                class="seller-item"
                :class="{ 'selected': selectedSeller && selectedSeller.id === seller.id }"
                @click="selectSeller(seller)">
              <div class="seller-logo">
                <img v-if="seller.logoUrl"
                    :src="seller.logoUrl"
                    :alt="seller.name"
                    @error="$event.target.style.display='none'">
                <div v-else class="seller-initials">
                  {{ (seller.name || 'N').charAt(0).toUpperCase() }}
                </div>
              </div>
              <div class="seller-info">
                <h4>{{ seller.name || '未知商家' }}</h4>
                <p v-if="seller.description">{{ seller.description }}</p>
                <p class="seller-id">商家ID: {{ seller.originalIndex }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ProductCard from '@/components/ProductCard.vue'
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { getSummerNewProductsList } from '@/api/summerNewProducts'
import productsApi from '@/api/products'
import { getQcGroupsList } from '@/api/qcGroups'
import videosApi from '@/api/videos' // 添加视频API导入
import bannersApi from '@/api/banners' // 添加轮播图API导入
import postsApi from '@/api/posts' // 添加帖子API导入
import { useRouter, useRoute } from 'vue-router' // 导入路由
import { emitter } from '@/utils/eventBus' // 导入事件总线
import apiClient from '@/services/api' // 添加API客户端导入
import { ElMessage } from 'element-plus' // 添加Element Plus消息组件

export default {
  name: 'TrendingProductsView',
  components: {
    ProductCard
  },
  setup() {
    const products = ref([])
    const loading = ref(false)
    const error = ref(null)
    const categories = ref([])
    const selectedCategory = ref(null)
    const showAllCategories = ref(true)
    const categoryBrands = ref([])
    const selectedBrand = ref(null)
    const brandsByCategory = ref({}) // 存储每个分类对应的品牌列表
    const isMobile = ref(window.innerWidth <= 480)
    const isDarkMode = ref(document.documentElement.getAttribute('data-theme') === 'dark') // 添加主题状态
    // 添加搜索查询状态
    const searchQuery = ref('')
    
    // 设置路由
    const router = useRouter()
    const route = useRoute() // 添加route引用
    
    // 商家相关数据
    const sellers = ref([]);
    const isLoadingSellers = ref(false);
    const showSellersModal = ref(false);
    const selectedSeller = ref(null);
    
    // 添加导航到商品详情页的方法
    const goToProductDetail = (productId) => {
      if (!productId) {
        return;
      }
      router.push(`/product/${productId}`);
    }
    
    // 轮播图相关状态
    const banners = ref([])
    const bannersLoading = ref(false)
    const bannersError = ref(null)
    const currentBannerIndex = ref(0)
    const bannerTimer = ref(null)
    const isVideoPlaying = ref(false) // 当前是否有视频正在播放
    
    // 获取轮播图数据
    const fetchBanners = async () => {
      bannersLoading.value = true
      bannersError.value = null
      
      try {
        const response = await bannersApi.getHomeBannerList()
        
        if (response && response.code === 200 && Array.isArray(response.data)) {
          // 预处理轮播图数据，添加类型标识和预加载图片
          banners.value = response.data.map(banner => {
            // 判断是否为视频类型 (检查常见视频扩展名)
            const isVideo = banner.postImage && typeof banner.postImage === 'string' && 
              (banner.postImage.endsWith('.mp4') || banner.postImage.endsWith('.webm') || 
               banner.postImage.endsWith('.mov') || banner.postType === 'video');
            
            // 返回带有类型标识的处理后数据
            return {
              ...banner,
              isVideo: isVideo,
              // 如果是视频，设置视频URL和缩略图
              videoUrl: isVideo ? banner.postImage : null,
              // 如果是视频但没有缩略图，使用占位图
              postImage: isVideo ? (banner.thumbnail || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAwIiBoZWlnaHQ9IjQwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iODAwIiBoZWlnaHQ9IjQwMCIgZmlsbD0iIzMzMyIvPjx0ZXh0IHg9IjQwMCIgeT0iMjAwIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMzAiIGZpbGw9IiNmZmYiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGFsaWdubWVudC1iYXNlbGluZT0ibWlkZGxlIj5WaWRlbyBMb2FkaW5nLi4uPC90ZXh0Pjwvc3ZnPg==') : banner.postImage,
              // 添加加载状态
              isLoaded: false
            };
          });
          
          // 预加载图片，提高轮播效率
          preloadBannerImages();
          
          // 如果有轮播图数据，启动自动轮播
          if (banners.value.length > 1) {
            startBannerAutoplay();
          }
        } else {
          // 如果API返回的数据格式不正确，使用模拟数据
          useDefaultBannerData();
        }
      } catch (err) {
        bannersError.value = '获取轮播图数据失败';
        useDefaultBannerData();
      } finally {
        bannersLoading.value = false;
      }
    };
    
    // 预加载轮播图图片
    const preloadBannerImages = () => {
      banners.value.forEach((banner, index) => {
        // 如果是图片类型，则预加载
        if (!banner.isVideo && banner.postImage) {
          const img = new Image();
          img.onload = () => {
            // 图片加载完成，更新加载状态
            if (banners.value[index]) {
              banners.value[index].isLoaded = true;
            }
          };
          img.onerror = () => {
            // 图片加载失败，使用占位图
            if (banners.value[index]) {
              banners.value[index].postImage = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAwIiBoZWlnaHQ9IjQwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iODAwIiBoZWlnaHQ9IjQwMCIgZmlsbD0iIzMzMyIvPjx0ZXh0IHg9IjQwMCIgeT0iMjAwIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMzAiIGZpbGw9IiNmZmYiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGFsaWdubWVudC1iYXNlbGluZT0ibWlkZGxlIj5JbWFnZSBMb2FkIEVycm9yPC90ZXh0Pjwvc3ZnPg==';
              banners.value[index].isLoaded = true;
            }
          };
          img.src = banner.postImage;
        }
      });
    };
    
    // 使用默认模拟数据
    const useDefaultBannerData = () => {
      banners.value = [
        {
          id: '1',
          postTitle: 'This is a title anything you can type',
          postContent: '这是第一个轮播图的内容',
          postImage: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAwIiBoZWlnaHQ9IjQwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iODAwIiBoZWlnaHQ9IjQwMCIgZmlsbD0iIzY2NiIvPjx0ZXh0IHg9IjQwMCIgeT0iMjAwIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMzAiIGZpbGw9IiNmZmYiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGFsaWdubWVudC1iYXNlbGluZT0ibWlkZGxlIj5CYW5uZXIgSW1hZ2UgUGxhY2Vob2xkZXI8L3RleHQ+PC9zdmc+',
          postLikes: '0',
          postFavorites: '0',
          postViews: '0',
          commentCount: '0',
          isLoaded: true,
          isVideo: false
        },
        {
          id: '2',
          postTitle: 'Another great title for your banner',
          postContent: '这是第二个轮播图的内容',
          postImage: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAwIiBoZWlnaHQ9IjQwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iODAwIiBoZWlnaHQ9IjQwMCIgZmlsbD0iIzQ0NCIvPjx0ZXh0IHg9IjQwMCIgeT0iMjAwIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMzAiIGZpbGw9IiNmZmYiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGFsaWdubWVudC1iYXNlbGluZT0ibWlkZGxlIj5CYW5uZXIgSW1hZ2UgUGxhY2Vob2xkZXI8L3RleHQ+PC9zdmc+',
          postLikes: '0',
          postFavorites: '0',
          postViews: '0',
          commentCount: '0',
          isLoaded: true,
          isVideo: false
        }
      ];
      
      // 启动自动轮播
      if (banners.value.length > 1) {
        startBannerAutoplay();
      }
    };
    
    // 切换到下一个轮播图
    const nextBanner = (isManual = false) => {
      if (banners.value.length <= 1) return;
      
      // 如果当前轮播是视频且正在播放，且不是手动切换，则不自动切换
      const currentBanner = banners.value[currentBannerIndex.value];
      if (!isManual && currentBanner && currentBanner.isVideo && isVideoPlaying.value) {
        return;
      }
      
      // 如果是手动切换且视频正在播放，需要停止视频
      if (isManual && isVideoPlaying.value) {
        const videoElement = isMobile.value 
          ? document.getElementById(`banner-video-mobile-${currentBannerIndex.value}`)
          : document.getElementById(`banner-video-${currentBannerIndex.value}`);
        
        if (videoElement) {
          videoElement.pause();
          videoElement.currentTime = 0; // 重置播放位置
          isVideoPlaying.value = false;
        }
      }
      
      // 重置视频结束后轮播图标志
      isPostVideoSlide.value = false;
      
      currentBannerIndex.value = (currentBannerIndex.value + 1) % banners.value.length;
      
      // 检查是否切换到视频类型，如果是则自动播放
      checkAndPlayVideo();
    };
    
    // 切换到上一个轮播图
    const prevBanner = () => {
      if (banners.value.length <= 1) return;
      
      // 重置视频结束后轮播图标志
      isPostVideoSlide.value = false;
      
      currentBannerIndex.value = (currentBannerIndex.value - 1 + banners.value.length) % banners.value.length;
      
      // 检查是否切换到视频类型，如果是则自动播放
      checkAndPlayVideo();
    };
    
    // 检查当前轮播项是否为视频，如果是则自动播放
    const checkAndPlayVideo = () => {
      // 延迟执行，确保DOM已更新
      setTimeout(() => {
        const currentBanner = banners.value[currentBannerIndex.value];
        if (currentBanner && currentBanner.isVideo) {
          // 查找视频元素 - 同时检查桌面版和移动版
          const desktopVideoElement = document.getElementById(`banner-video-${currentBannerIndex.value}`);
          const mobileVideoElement = document.getElementById(`banner-video-mobile-${currentBannerIndex.value}`);
          
          // 根据当前设备类型选择适当的视频元素
          const videoElement = isMobile.value ? mobileVideoElement : desktopVideoElement;
          
          if (videoElement) {
            // 添加控制台日志以便调试
            videoElement.src = currentBanner.videoUrl;
            videoElement.load();
            
            // 自动播放视频
            videoElement.play().then(() => {
              isVideoPlaying.value = true;
            }).catch(error => {
              console.log("视频播放失败", error);
            
              isVideoPlaying.value = false;
            });
          } else {
            isVideoPlaying.value = false;
          }
        } else {
          isVideoPlaying.value = false;
        }
      }, 100);
    };
    
    // 视频播放状态改变事件处理
    const handleVideoPlayPause = (event) => {
      isVideoPlaying.value = !event.target.paused;
    };
    
    // 添加一个标记，表示是否是视频结束后的轮播图
    const isPostVideoSlide = ref(false);
    
    // 视频结束事件处理
    const handleVideoEnded = () => {
      isVideoPlaying.value = false;
      
      // 直接设置到下一个轮播项，而不是调用nextBanner函数
      // 避免可能的重复跳转问题
      const nextIndex = (currentBannerIndex.value + 1) % banners.value.length;
      currentBannerIndex.value = nextIndex;
      
      // 设置标记，表示这是视频结束后的轮播图
      isPostVideoSlide.value = true;
      
      // 延迟一下再检查当前轮播项，确保DOM已更新
      setTimeout(() => {
        // 检查是否切换到了视频，如果是则播放
        checkAndPlayVideo();
        
        // 重新启动轮播定时器，但给予更长的停留时间
        stopBannerAutoplay();
        
        // 设置一个一次性的定时器，确保当前轮播图有足够的停留时间
        setTimeout(() => {
          isPostVideoSlide.value = false;
          startBannerAutoplay(); // 恢复正常的轮播
        }, 5000); // 确保停留5秒
      }, 100);
    };
    
    // 启动自动轮播
    const startBannerAutoplay = () => {
      // 先清除可能存在的定时器
      stopBannerAutoplay();
      
      // 设置新的定时器，每5秒切换一次
      bannerTimer.value = setInterval(() => {
        // 只有当前不是视频或视频没有播放时，且不是视频结束后的轮播图时才自动切换
        if (!isVideoPlaying.value && !isPostVideoSlide.value) {
          nextBanner();
        }
      }, 5000);
    };
    
    // 停止自动轮播
    const stopBannerAutoplay = () => {
      if (bannerTimer.value) {
        clearInterval(bannerTimer.value);
        bannerTimer.value = null;
      }
    };
    
    // 当前轮播图
    const currentBanner = computed(() => {
      return banners.value[currentBannerIndex.value] || {};
    });
    
    // 安全获取当前轮播图的样式
    const getCurrentBannerStyle = computed(() => {
      const banner = currentBanner.value;
      if (!banner) return {};
      
      // 如果是视频，则不设置背景图
      if (banner.isVideo) {
        return { 
          backgroundImage: `url(${banner.postImage})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center'
        };
      }
      
      // 如果图片尚未加载完成，使用低质量占位图
      if (!banner.isLoaded) {
        return { 
          backgroundImage: `url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAwIiBoZWlnaHQ9IjQwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iODAwIiBoZWlnaHQ9IjQwMCIgZmlsbD0iIzMzMyIvPjx0ZXh0IHg9IjQwMCIgeT0iMjAwIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMzAiIGZpbGw9IiNmZmYiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGFsaWdubWVudC1iYXNlbGluZT0ibWlkZGxlIj5Mb2FkaW5nLi4uPC90ZXh0Pjwvc3ZnPg==)`,
          backgroundSize: 'cover',
          backgroundPosition: 'center'
        };
      }
      
      // 正常显示加载完成的图片
      return { 
        backgroundImage: `url(${banner.postImage})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center'
      };
    });
    
    // 全屏分类菜单相关状态
    const showCategoryMenu = ref(false)
    const activeSidebarItem = ref(null)
    // 分类菜单数据 - 使用API数据
    const categoryItems = ref({})
    
    // 计算当前显示的分类项目
    const currentCategoryItems = computed(() => {
      // 显示选中分类对应的品牌数据
      const categoryId = activeSidebarItem.value;
      return brandsByCategory.value[categoryId] || [];
    })
    
    // 切换分类菜单显示状态
    const toggleCategoryMenu = () => {
      showCategoryMenu.value = !showCategoryMenu.value
      
      // 如果打开菜单，添加body的overflow:hidden防止滚动
      if (showCategoryMenu.value) {
        document.body.style.overflow = 'hidden'
        // 添加show类来触发动画
        setTimeout(() => {
          const menuElement = document.querySelector('.category-fullscreen')
          if (menuElement) {
            menuElement.classList.add('show')
          }
        }, 10)
      } else {
        // 先移除show类，等动画结束后再恢复滚动
        const menuElement = document.querySelector('.category-fullscreen')
        if (menuElement) {
          menuElement.classList.remove('show')
          setTimeout(() => {
            document.body.style.overflow = ''
          }, 300) // 等待动画结束
        } else {
          document.body.style.overflow = ''
        }
      }
    }
    
    // 设置活动侧边栏项目
    const setActiveSidebarItem = async (item) => {
      activeSidebarItem.value = item

      // 获取对应分类的品牌数据
      if (!brandsByCategory.value[item]) {
        try {
          await fetchBrandsByCategory(item);
        } catch (error) {
          console.error('获取分类品牌数据失败:', error);
        }
      }
    }
    
    // 选择分类项目
    const selectCategoryItem = async (item) => {
      selectedCategory.value = activeSidebarItem.value; // 设置当前选中的分类
      selectedBrand.value = item.brandId; // 设置选中的品牌
      
      // 导航到ProductsTwo.vue，携带分类ID和品牌ID
      router.push({
        name: 'products',
        query: {
          categoryId: activeSidebarItem.value,
          brandId: item.brandId
        }
      });

      toggleCategoryMenu(); // 选择后关闭菜单
    }
    
    // 为FRESHLY QC部分添加数据
    const freshlyQcGroups = ref([])
    const qcLoading = ref(false)
    
    // 添加当前显示的图片索引状态（使用单一索引控制所有组）
    const currentImageIndex = ref(0)
    
    // 计算所有组中图片的最大数量
    const maxImagesPerGroup = computed(() => {
      if (!validQcGroups.value || validQcGroups.value.length === 0) {
        return 0
      }
      
      return validQcGroups.value.reduce((max, group) => 
        Math.max(max, group.length), 0)
    })
    
    // 自动轮播定时器
    const autoplayTimer = ref(null)
    const autoplayInterval = 3000 // 3秒切换一次
    
    // 切换分类显示状态
    const toggleCategoriesVisibility = () => {
      showAllCategories.value = !showAllCategories.value;
    }
    
    // 选择分类
    const toggleCategoryDropdown = async (categoryId) => {
      // 检查categoryId是否有效
      if (categoryId === undefined || categoryId === null) {
        return;
      }
      
      if (selectedCategory.value === categoryId) {
        // 如果点击的是当前选中的分类，则关闭下拉菜单
        selectedCategory.value = null;
        categoryBrands.value = [];
        
        // 导航到ProductsTwo.vue，仅携带分类ID
        router.push({
          name: 'products',
          query: {
            categoryId: categoryId
          }
        });
      } else {
        // 切换到新的分类
        selectedCategory.value = categoryId;
        selectedBrand.value = null;
        
        // 计算下拉菜单位置
        calculateDropdownPosition();
        
        // 如果已经缓存了该分类的品牌数据，直接使用缓存
        if (brandsByCategory.value[categoryId]) {
          categoryBrands.value = brandsByCategory.value[categoryId];
        } else {
          // 否则，获取该分类下的品牌
          await fetchBrandsByCategory(categoryId);
        }
      }
    }
    
    // 下拉菜单位置
    const dropdownPosition = ref({
      position: 'absolute',
      top: '0px',
      left: '0px',
      bottom: 'auto',
      width: 'auto'
    });
    
    // 计算下拉菜单位置
    const calculateDropdownPosition = () => {
      // 延迟执行，确保DOM已更新
      setTimeout(() => {
        const selectedChip = document.querySelector(`.filter-chip.active`);
        if (selectedChip) {
          const rect = selectedChip.getBoundingClientRect();
          
          // 根据设备类型设置下拉菜单位置
          if (isMobile.value) {
            // 移动端显示在分类正下方，紧贴分类
            dropdownPosition.value = {
              position: 'fixed',
              top: `${rect.bottom + 5}px`,
              left: '50%',
              transform: 'translateX(-50%)',
              width: '90%',
              maxWidth: '350px',
              bottom: 'auto'
            };
          } else {
            // PC端显示在分类下方
            dropdownPosition.value = {
              position: 'absolute',
              top: `${rect.bottom + window.scrollY + 5}px`,
              left: `${rect.left + window.scrollX}px`,
              bottom: 'auto',
              width: '180px',
              transform: 'none'
            };
          }
        }
      }, 10);
    };
    
    // 选择品牌
    const selectBrand = (brandId) => {
      if (selectedBrand.value === brandId) {
        selectedBrand.value = null;
      } else {
        selectedBrand.value = brandId;
      }
      
      // 导航到ProductsTwo.vue，携带分类ID和品牌ID
      router.push({
        name: 'products',
        query: {
          categoryId: selectedCategory.value,
          brandId: brandId
        }
      });
    }
    
    // 获取分类下的品牌
    const fetchBrandsByCategory = async (categoryId) => {
      try {
        // 检查categoryId是否有效
        if (categoryId === undefined || categoryId === null) {
          return;
        }
        
        // 确保categoryId是字符串类型
        const catId = categoryId.toString();
        
        // 调用API获取品牌，确保传递categoryId参数
        try {
          const response = await productsApi.getBrandsByCategory(catId);
          
          if (response && response.data && Array.isArray(response.data)) {
            categoryBrands.value = response.data.map(brand => ({
              brandId: brand.brandId || brand.id,
              name: brand.name,
              logoUrl: brand.logoUrl || null,
              image: brand.image || null,
              parentCategoryId: categoryId
            }));
            brandsByCategory.value[categoryId] = categoryBrands.value;
          } else if (Array.isArray(response)) {
            categoryBrands.value = response.map(brand => ({
              brandId: brand.brandId || brand.id,
              name: brand.name,
              logoUrl: brand.logoUrl || null,
              image: brand.image || null,
              parentCategoryId: categoryId
            }));
            brandsByCategory.value[categoryId] = categoryBrands.value;
          } else {
            // 如果API返回的数据无效，使用模拟数据
            const mockData = [
              { brandId: 1, name: 'Hoodies', parentCategoryId: categoryId },
              { brandId: 2, name: 'T-Shirt', parentCategoryId: categoryId },
              { brandId: 3, name: 'Pants', parentCategoryId: categoryId },
              { brandId: 4, name: 'Sets', parentCategoryId: categoryId }
            ];
            categoryBrands.value = mockData;
            brandsByCategory.value[categoryId] = mockData;
          }
        } catch (apiErr) {
          // 使用模拟数据
          const mockData = [
            { brandId: 1, name: 'Hoodies', parentCategoryId: categoryId },
            { brandId: 2, name: 'T-Shirt', parentCategoryId: categoryId },
            { brandId: 3, name: 'Pants', parentCategoryId: categoryId },
            { brandId: 4, name: 'Sets', parentCategoryId: categoryId }
          ];
          categoryBrands.value = mockData;
          brandsByCategory.value[categoryId] = mockData;
        }
      } catch (err) {
        // 发生错误时使用模拟数据
        const mockData = [
          { brandId: 1, name: 'Hoodies', parentCategoryId: categoryId },
          { brandId: 2, name: 'T-Shirt', parentCategoryId: categoryId },
          { brandId: 3, name: 'Pants', parentCategoryId: categoryId },
          { brandId: 4, name: 'Sets', parentCategoryId: categoryId }
        ];
        categoryBrands.value = mockData;
        brandsByCategory.value[categoryId] = mockData;
      }
    };
    
    // 处理下一张图片按钮点击
    const nextImage = () => {
      if (!validQcGroups.value || validQcGroups.value.length === 0) return
      
      // 更新当前索引，循环显示
      currentImageIndex.value = (currentImageIndex.value + 1) % maxImagesPerGroup.value
    }
    
    // 启动自动轮播
    const startAutoplay = () => {
      // 防止在服务器端渲染时调用
      if (typeof window === 'undefined') return
      
      // 检查是否有有效的QC组
      if (!validQcGroups.value || validQcGroups.value.length === 0) return
      
      // 先清除可能存在的定时器
      if (autoplayTimer.value) {
        clearInterval(autoplayTimer.value)
      }
      
      // 设置新的定时器
      autoplayTimer.value = setInterval(() => {
        nextImage()
      }, autoplayInterval)
    }
    
    // 停止自动轮播
    const stopAutoplay = () => {
      if (autoplayTimer.value) {
        clearInterval(autoplayTimer.value)
        autoplayTimer.value = null
      }
    }
    
    // 获取QC组数据
    const fetchQcGroups = async () => {
      qcLoading.value = true
      try {
        const qcData = await getQcGroupsList()
        
        if (Array.isArray(qcData) && qcData.length > 0) {
          // 过滤掉空组或无效组
          const validGroups = qcData.filter(group => Array.isArray(group) && group.length > 0)
          freshlyQcGroups.value = validGroups
          
          // 只有在有有效数据时才启动轮播
          if (validGroups.length > 0) {
            // 延迟启动自动轮播，确保DOM已渲染
            setTimeout(() => {
              if (maxImagesPerGroup.value > 1) {
                startAutoplay()
              }
            }, 500)
          }
        } else {
          // 使用模拟数据作为备用
          useMockQcData()
        }
      } catch (err) {
        // 使用模拟数据作为备用
        useMockQcData()
      } finally {
        qcLoading.value = false
      }
    }
    
    // 使用模拟数据
    const useMockQcData = () => {
      try {
        const mockGroups = [
          [
            {
              id: '1_0',
              groupId: '1',
              groupName: 'Group 1',
              count: '1/4',
              description: 'Group 1 Image 1',
              imageUrl: 'https://cdn.findqc.com/bt/20250607/mbme4p4h_lete.jpg',
              position: '1'
            },
            {
              id: '1_1',
              groupId: '1',
              groupName: 'Group 1',
              count: '2/4',
              description: 'Group 1 Image 2',
              imageUrl: 'https://cdn.findqc.com/bt/20250607/mbme4p4h_lete.jpg',
              position: '1'
            },
            {
              id: '1_2',
              groupId: '1',
              groupName: 'Group 1',
              count: '3/4',
              description: 'Group 1 Image 3',
              imageUrl: 'https://cdn.findqc.com/bt/20250607/mbme4p4h_lete.jpg',
              position: '1'
            },
            {
              id: '1_3',
              groupId: '1',
              groupName: 'Group 1',
              count: '4/4',
              description: 'Group 1 Image 4',
              imageUrl: 'https://cdn.findqc.com/bt/20250607/mbme4p4h_lete.jpg',
              position: '1'
            }
          ],
          [
            {
              id: '2_0',
              groupId: '2',
              groupName: 'Group 2',
              count: '1/4',
              description: 'Group 2 Image 1',
              imageUrl: 'https://cdn.findqc.com/bt/20250607/mbme4p4h_lete.jpg',
              position: '2'
            },
            {
              id: '2_1',
              groupId: '2',
              groupName: 'Group 2',
              count: '2/4',
              description: 'Group 2 Image 2',
              imageUrl: 'https://cdn.findqc.com/bt/20250607/mbme4p4h_lete.jpg',
              position: '2'
            },
            {
              id: '2_2',
              groupId: '2',
              groupName: 'Group 2',
              count: '3/4',
              description: 'Group 2 Image 3',
              imageUrl: 'https://cdn.findqc.com/bt/20250607/mbme4p4h_lete.jpg',
              position: '2'
            },
            {
              id: '2_3',
              groupId: '2',
              groupName: 'Group 2',
              count: '4/4',
              description: 'Group 2 Image 4',
              imageUrl: 'https://cdn.findqc.com/bt/20250607/mbme4p4h_lete.jpg',
              position: '2'
            }
          ],
          [
            {
              id: '3_0',
              groupId: '3',
              groupName: 'Group 3',
              count: '1/4',
              description: 'Group 3 Image 1',
              imageUrl: 'https://cdn.findqc.com/bt/20250607/mbme4p4h_lete.jpg',
              position: '3'
            },
            {
              id: '3_1',
              groupId: '3',
              groupName: 'Group 3',
              count: '2/4',
              description: 'Group 3 Image 2',
              imageUrl: 'https://cdn.findqc.com/bt/20250607/mbme4p4h_lete.jpg',
              position: '3'
            },
            {
              id: '3_2',
              groupId: '3',
              groupName: 'Group 3',
              count: '3/4',
              description: 'Group 3 Image 3',
              imageUrl: 'https://cdn.findqc.com/bt/20250607/mbme4p4h_lete.jpg',
              position: '3'
            },
            {
              id: '3_3',
              groupId: '3',
              groupName: 'Group 3',
              count: '4/4',
              description: 'Group 3 Image 4',
              imageUrl: 'https://cdn.findqc.com/bt/20250607/mbme4p4h_lete.jpg',
              position: '3'
            }
          ],
          [
            {
              id: '4_0',
              groupId: '4',
              groupName: 'Group 4',
              count: '1/4',
              description: 'Group 4 Image 1',
              imageUrl: 'https://cdn.findqc.com/bt/20250607/mbme4p4h_lete.jpg',
              position: '4'
            },
            {
              id: '4_1',
              groupId: '4',
              groupName: 'Group 4',
              count: '2/4',
              description: 'Group 4 Image 2',
              imageUrl: 'https://cdn.findqc.com/bt/20250607/mbme4p4h_lete.jpg',
              position: '4'
            },
            {
              id: '4_2',
              groupId: '4',
              groupName: 'Group 4',
              count: '3/4',
              description: 'Group 4 Image 3',
              imageUrl: 'https://cdn.findqc.com/bt/20250607/mbme4p4h_lete.jpg',
              position: '4'
            },
            {
              id: '4_3',
              groupId: '4',
              groupName: 'Group 4',
              count: '4/4',
              description: 'Group 4 Image 4',
              imageUrl: 'https://cdn.findqc.com/bt/20250607/mbme4p4h_lete.jpg',
              position: '4'
            }
          ]
        ]
        
        freshlyQcGroups.value = mockGroups
        
        // 延迟启动自动轮播，确保DOM已渲染
        if (typeof window !== 'undefined') {
          setTimeout(() => {
            if (maxImagesPerGroup.value > 1) {
              startAutoplay()
            }
          }, 500)
        }
      } catch (error) {
        // 出错时设置为空数组，不启动轮播
        freshlyQcGroups.value = []
      }
    }
    
    const fetchProducts = async () => {
      loading.value = true
      try {
        // 使用summerNewProducts.js中的接口获取数据
        const response = await getSummerNewProductsList()
        
        if (response && response.code === 200 && Array.isArray(response.data)) {
          // 使用API返回的标准格式数据
          products.value = response.data.map((item) => {
            // 获取视频URL，优先使用video字段，然后是videoUrl字段
            const videoUrl = item.video || item.videoUrl || null;

            // 调试日志：显示每个商品的视频信息
            console.log(`商品 ${item.name} (ID: ${item.id || item.productId}) 的视频URL:`, videoUrl);

            return {
              id: item.id || item.productId,
              productId: item.productId || item.id,
              name: item.name,
              price: item.price,
              mainImage: item.mainImage || item.imageUrl,
              image: item.mainImage || item.imageUrl,
              platform: item.platform || 'OMG',
              merchant: item.merchant,
              categoryId: item.categoryId,
              originalPrice: item.originalPrice,
              status: item.status,
              stock: item.stock,
              sku: item.sku,
              likes: item.likes || 0,
              views: item.views || 0,
              qc: item.qc || '0', // 确保qc字段被正确传递
              virtualViews: item.virtualViews || '0', // 添加virtualViews字段
              comments: item.comments || [],
              rating: item.rating || 0,
              totalRatings: item.totalRatings || 0,
              createdAt: item.createdAt,
              updatedAt: item.updatedAt,
              productStatus: item.productStatus || 0,
              // 使用接口返回的video字段，如果没有则使用null
              videoUrl: videoUrl,
              videoFitMode: item.videoFitMode || 'contain' // 添加视频适配模式
            };
          });

          // 调试日志：显示处理后的商品数据
          console.log('处理后的商品数据:', products.value);
        } else if (Array.isArray(response) && response.length > 0) {
          // 直接使用返回的数组数据
          products.value = response.map((item) => {
            // 获取视频URL，优先使用video字段，然后是videoUrl字段
            const videoUrl = item.video || item.videoUrl || null;

            // 调试日志：显示每个商品的视频信息
            console.log(`商品 ${item.name} (ID: ${item.id || item.productId}) 的视频URL:`, videoUrl);

            return {
              id: item.id || item.productId,
              productId: item.productId || item.id,
              name: item.name,
              price: item.price,
              mainImage: item.mainImage || item.imageUrl,
              image: item.mainImage || item.imageUrl,
              platform: item.platform || 'OMG',
              merchant: item.merchant,
              categoryId: item.categoryId,
              originalPrice: item.originalPrice,
              status: item.status,
              stock: item.stock,
              sku: item.sku,
              likes: item.likes || 0,
              views: item.views || 0,
              qc: item.qc || '0', // 确保qc字段被正确传递
              virtualViews: item.virtualViews || '0', // 添加virtualViews字段
              comments: item.comments || [],
              rating: item.rating || 0,
              totalRatings: item.totalRatings || 0,
              createdAt: item.createdAt,
              updatedAt: item.updatedAt,
              productStatus: item.productStatus || 0,
              // 使用接口返回的video字段，如果没有则使用null
              videoUrl: videoUrl,
              videoFitMode: item.videoFitMode || 'contain' // 添加视频适配模式
            };
          });

          // 调试日志：显示处理后的商品数据
          console.log('处理后的商品数据:', products.value);
        } else {
          // 如果API返回空数据，使用模拟数据
          products.value = [
            {
              id: "389",
              productId: "19156406507417155582",
              name: "Nike Suit (5+)",
              price: 11.11,
              originalPrice: 14,
              likes: 0,
              comments: [],
              views: 2,
              qc: '5', // 添加qc字段
              virtualViews: '120', // 添加virtualViews字段
              mainImage: "https://si.geilicdn.com/pcitem1899152727-337d00001979225371110a2304ae-unadjust_750_751.png",
              image: "https://si.geilicdn.com/pcitem1899152727-337d00001979225371110a2304ae-unadjust_750_751.png",
              platform: "微店",
              merchant: "OMG",
              categoryId: 22,
              status: "active",
              stock: 0,
              sku: "7492363522",
              rating: 0,
              totalRatings: 0,
              createdAt: "2025-06-24T10:20:41",
              updatedAt: "2025-07-08T10:32:51",
              // 模拟数据：第一个商品有视频
              videoUrl: 'https://agtfind.oss-eu-central-1.aliyuncs.com/ylkj-uploads/2025/07/05/ebf080c2105e2d35e25e41ee8ab07b79.mp4',
              videoFitMode: 'contain'
            },
            {
              id: "390",
              productId: "19156406507417155584",
              name: "Dior Suit (25+)",
              price: 30.16,
              originalPrice: 40,
              likes: 0,
              comments: [],
              views: 0,
              qc: '3', // 添加qc字段
              virtualViews: '85', // 添加virtualViews字段
              mainImage: "https://si.geilicdn.com/pcitem1899152727-337d00001979225371110a2304ae-unadjust_750_751.png",
              image: "https://si.geilicdn.com/pcitem1899152727-337d00001979225371110a2304ae-unadjust_750_751.png",
              platform: "微店",
              merchant: "OMG",
              categoryId: 22,
              status: "active",
              stock: 0,
              sku: "7492363523",
              rating: 0,
              totalRatings: 0,
              createdAt: "2025-06-24T10:20:41",
              updatedAt: "2025-07-08T10:32:51",
              // 模拟数据：第二个商品没有视频
              videoUrl: null,
              videoFitMode: 'contain'
            },
            {
              id: "391",
              productId: "19156406507417155585",
              name: "Nike Suit (5+)",
              price: 22.06,
              originalPrice: 30,
              likes: 0,
              comments: [],
              views: 0,
              qc: '8', // 添加qc字段
              virtualViews: '210', // 添加virtualViews字段
              mainImage: "https://si.geilicdn.com/pcitem1899152727-337d00001979225371110a2304ae-unadjust_750_751.png",
              image: "https://si.geilicdn.com/pcitem1899152727-337d00001979225371110a2304ae-unadjust_750_751.png",
              platform: "微店",
              merchant: "OMG",
              categoryId: 22,
              status: "active",
              stock: 0,
              sku: "7492363524",
              rating: 0,
              totalRatings: 0,
              createdAt: "2025-06-24T10:20:41",
              updatedAt: "2025-07-08T10:32:51",
              // 模拟数据：第三个商品有视频
              videoUrl: 'https://agtfind.oss-eu-central-1.aliyuncs.com/ylkj-uploads/2025/07/05/ebf080c2105e2d35e25e41ee8ab07b79.mp4',
              videoFitMode: 'contain'
            }
          ]
        }
      } catch (err) {
        error.value = 'Failed to load trending products'
        // 使用模拟数据作为备用
        products.value = [
          {
            id: "389",
            productId: "19156406507417155582",
            name: "Nike Suit (5+)",
            price: 11.11,
            originalPrice: 14,
            likes: 0,
            comments: [],
            views: 2,
            qc: '4', // 添加qc字段
            virtualViews: '150', // 添加virtualViews字段
            mainImage: "https://si.geilicdn.com/pcitem1899152727-337d00001979225371110a2304ae-unadjust_750_751.png",
            image: "https://si.geilicdn.com/pcitem1899152727-337d00001979225371110a2304ae-unadjust_750_751.png",
            platform: "微店",
            merchant: "OMG",
            categoryId: 22,
            status: "active",
            stock: 0,
            sku: "7492363522",
            rating: 0,
            totalRatings: 0,
            createdAt: "2025-06-24T10:20:41",
            updatedAt: "2025-07-08T10:32:51",
            // 错误处理模拟数据：第一个商品有视频
            videoUrl: 'https://agtfind.oss-eu-central-1.aliyuncs.com/ylkj-uploads/2025/07/05/ebf080c2105e2d35e25e41ee8ab07b79.mp4',
            videoFitMode: 'contain'
          },
          {
            id: "390",
            productId: "19156406507417155584",
            name: "Dior Suit (25+)",
            price: 30.16,
            originalPrice: 40,
            likes: 0,
            comments: [],
            views: 0,
            qc: '6', // 添加qc字段
            virtualViews: '95', // 添加virtualViews字段
            mainImage: "https://si.geilicdn.com/pcitem1899152727-337d00001979225371110a2304ae-unadjust_750_751.png",
            image: "https://si.geilicdn.com/pcitem1899152727-337d00001979225371110a2304ae-unadjust_750_751.png",
            platform: "微店",
            merchant: "OMG",
            categoryId: 22,
            status: "active",
            stock: 0,
            sku: "7492363523",
            rating: 0,
            totalRatings: 0,
            createdAt: "2025-06-24T10:20:41",
            updatedAt: "2025-07-08T10:32:51",
            // 错误处理模拟数据：第二个商品没有视频
            videoUrl: null,
            videoFitMode: 'contain'
          }
        ]
      } finally {
        loading.value = false
      }
    }

    // 获取所有分类
    const fetchCategories = async () => {
      try {
        const response = await productsApi.getAllCategories()
        
        // Handle different response structures
        if (response && response.data && Array.isArray(response.data)) {
          // 确保每个分类对象都有id属性
          categories.value = response.data.map(cat => {
            return {
              ...cat,
              id: cat.id || cat.categoryId || cat._id || String(Math.random()).substring(2, 10)
            };
          });

          // 设置第一个分类为默认选中
          if (categories.value.length > 0 && !activeSidebarItem.value) {
            activeSidebarItem.value = categories.value[0].id;
          }

          // 预加载前几个分类的品牌数据
          const preloadCategories = categories.value.slice(0, 3);
          for (const category of preloadCategories) {
            try {
              await fetchBrandsByCategory(category.id);
            } catch (error) {
              console.warn(`预加载分类 ${category.name} 的品牌数据失败:`, error);
            }
          }
        } else if (Array.isArray(response)) {
          // 确保每个分类对象都有id属性
          categories.value = response.map(cat => {
            return {
              ...cat,
              id: cat.id || cat.categoryId || cat._id || String(Math.random()).substring(2, 10)
            };
          });

          // 设置第一个分类为默认选中
          if (categories.value.length > 0 && !activeSidebarItem.value) {
            activeSidebarItem.value = categories.value[0].id;
          }

          // 预加载前几个分类的品牌数据
          const preloadCategories = categories.value.slice(0, 3);
          for (const category of preloadCategories) {
            try {
              await fetchBrandsByCategory(category.id);
            } catch (error) {
              console.warn(`预加载分类 ${category.name} 的品牌数据失败:`, error);
            }
          }
        } else {
          // Fallback to mock data if API returns unexpected format
          console.warn('API返回格式不符合预期，使用模拟数据');
          categories.value = [
            { id: 1, name: '夏季新品' },
            { id: 2, name: '最新上架' },
            { id: 3, name: '服装' },
            { id: 4, name: '鞋靴' },
            { id: 5, name: '配饰' },
            { id: 6, name: '手表' },
            { id: 7, name: '眼镜' },
            { id: 8, name: '手包' },
            { id: 9, name: '运动' },
            { id: 10, name: '休闲' }
          ]
        }
      } catch (err) {
        error.value = 'Failed to load categories'
        // Fallback to mock data
        categories.value = [
          { id: 1, name: '夏季新品' },
          { id: 2, name: '最新上架' },
          { id: 3, name: '服装' },
          { id: 4, name: '鞋靴' },
          { id: 5, name: '配饰' },
          { id: 6, name: '手表' },
          { id: 7, name: '眼镜' },
          { id: 8, name: '手包' },
          { id: 9, name: '运动' },
          { id: 10, name: '休闲' }
        ]
      }
    }

    // 添加点击外部关闭下拉菜单
    const handleClickOutside = (event) => {
      // 如果点击的不是分类或其子元素，也不是品牌下拉菜单
      const filterChips = document.querySelectorAll('.filter-chip');
      const brandsDropdown = document.querySelector('.brands-dropdown-container');
      let clickedInside = false;
      
      // 检查是否点击在分类上
      filterChips.forEach(chip => {
        if (chip.contains(event.target)) {
          clickedInside = true;
        }
      });
      
      // 检查是否点击在品牌下拉菜单上
      if (brandsDropdown && brandsDropdown.contains(event.target)) {
        clickedInside = true;
      }
      
      // 如果点击在外部，关闭下拉菜单
      if (!clickedInside && selectedCategory.value !== null) {
        selectedCategory.value = null;
        categoryBrands.value = [];
      }
    };

    // 添加窗口尺寸变化监听
    const handleResize = () => {
      isMobile.value = window.innerWidth <= 480;
      // 重新设置分类样式
      setupCategoriesStyle();
      if (selectedCategory.value !== null) {
        // 重新计算下拉菜单位置
        calculateDropdownPosition();
      }
    };

    // 设置分类样式
    const setupCategoriesStyle = () => {
      if (typeof window === 'undefined') return
      
      setTimeout(() => {
        const filterChips = document.querySelector('.filter-chips')
        if (filterChips) {
          // 根据设备类型设置样式
          if (isMobile.value) {
            filterChips.style.flexWrap = 'wrap'
            filterChips.style.width = '100%'
          } else {
            // PC端可以根据需要设置不同样式
            filterChips.style.flexWrap = 'wrap'
          }
        }
      }, 100)
    }
    
    // 初始化视频元素
    const initVideoElements = () => {
      // 初始化创作者视频元素
      const initCreatorVideos = () => {
        if (creatorVideos.value.length > 0) {
          setTimeout(() => {
            creatorVideos.value.forEach(video => {
              const videoElement = document.getElementById(`video-${video.id}`)
              const thumbnail = document.getElementById(`thumbnail-${video.id}`)
              const playButton = document.getElementById(`play-button-${video.id}`)
              
              if (videoElement && thumbnail && playButton) {
                // 确保视频元素初始状态正确
                videoElement.style.display = 'none'
                thumbnail.style.display = 'block'
                playButton.style.display = 'flex'
                
                // 添加视频点击事件（视频播放时点击暂停/继续）
                videoElement.addEventListener('click', () => {
                  if (videoElement.paused) {
                    videoElement.play()
                  } else {
                    videoElement.pause()
                  }
                })
                
                // 预加载视频
                videoElement.load()
              } else {
                // 如果视频数据还未加载，等待200ms后重试
                setTimeout(initCreatorVideos, 200)
              }
            })
          }, 500)
        } else {
          // 如果视频数据还未加载，等待200ms后重试
          setTimeout(initCreatorVideos, 200)
        }
      }
      
      // 初始化轮播图视频元素
      const initBannerVideos = () => {
        // 遍历所有轮播项，预处理视频元素
        if (banners.value && banners.value.length > 0) {
          banners.value.forEach((banner, index) => {
            if (banner.isVideo) {
              // 分别查找桌面端和移动端视频元素
              const desktopVideoElement = document.getElementById(`banner-video-${index}`);
              const mobileVideoElement = document.getElementById(`banner-video-mobile-${index}`);
              
              // 初始化桌面端视频
              if (desktopVideoElement) {
                desktopVideoElement.src = banner.videoUrl;
                desktopVideoElement.load();
              }
              
              // 初始化移动端视频
              if (mobileVideoElement) {
                mobileVideoElement.src = banner.videoUrl;
                mobileVideoElement.load();
              }
            }
          });
        }
        
        // 检查当前轮播项是否为视频，如果是则准备播放
        setTimeout(() => {
          checkAndPlayVideo();
        }, 1000);
      }
      
      // 开始初始化流程
      initCreatorVideos()
      initBannerVideos()
    }
    
    onMounted(() => {
      // 确保在浏览器环境中执行
      if (typeof window !== 'undefined') {
        fetchProducts()
        fetchCategories()
        fetchQcGroups() // 获取QC组数据
        fetchCreatorVideos() // 获取视频数据
        fetchBanners() // 获取轮播图数据
        initVideoElements() // 初始化视频元素
        // 设置分类样式
        setupCategoriesStyle()
        // 添加全局点击事件
        document.addEventListener('click', handleClickOutside)
        // 添加窗口尺寸变化事件
        window.addEventListener('resize', handleResize)
        
        // 监听主题变化
        emitter.on('theme-changed', handleThemeChange)
        // 初始检查主题
        checkCurrentTheme()

        // 延迟检查组件初始化状态，修复Cannot read properties of null (reading 'component')错误
        setTimeout(() => {
          // 强制重新渲染以确保组件正确初始化
          if (document.querySelector('.hero-slider') || document.querySelector('.mobile-hero-slider')) {
            forceRerender();
          }
        }, 500);
      }
    })
    
    onUnmounted(() => {
      // 确保在浏览器环境中执行
      if (typeof window !== 'undefined') {
        // 移除全局点击事件
        document.removeEventListener('click', handleClickOutside)
        // 移除窗口尺寸变化事件
        window.removeEventListener('resize', handleResize)
        // 清除自动轮播定时器
        stopAutoplay()
        // 清除banner轮播定时器
        stopBannerAutoplay()
        // 如果分类菜单打开，关闭它
        if (showCategoryMenu.value) {
          document.body.style.overflow = ''
        }
        // 移除主题变化监听
        emitter.off('theme-changed', handleThemeChange)
      }
    })
    
    // 处理主题变化
    const handleThemeChange = (data) => {
      if (data && typeof data.isDarkMode !== 'undefined') {
        isDarkMode.value = data.isDarkMode
      }
    }
    
    // 检查当前主题
    const checkCurrentTheme = () => {
      const theme = document.documentElement.getAttribute('data-theme')
      isDarkMode.value = theme === 'dark'
    }
    


    // 获取当前组的当前图片
    const getCurrentImageForGroup = (group, currentIndex) => {
      if (!group || !Array.isArray(group) || group.length === 0) {
        return null
      }
      
      const index = currentIndex % group.length
      return group[index] || null
    }
    
    // 默认图片URL
    const defaultImageUrl = 'https://cdn.findqc.com/bt/20250607/mbme4p4h_lete.jpg'
    
    // 获取图片URL
    const getImageUrl = (group, index) => {
      if (!group || !Array.isArray(group) || group.length === 0) {
        return defaultImageUrl
      }
      
      const currentIndex = index % group.length
      const image = group[currentIndex]
      
      return image?.imageUrl || defaultImageUrl
    }
    
    // 获取图片描述
    const getImageAlt = (group, index) => {
      if (!group || !Array.isArray(group) || group.length === 0) {
        return 'Default image'
      }
      
      const currentIndex = index % group.length
      const image = group[currentIndex]
      
      return image?.description || 'QC image'
    }
    
    // 处理图片加载错误
    const handleImageError = (e) => {
      e.target.src = defaultImageUrl
    }
    
    // 添加计算属性，过滤有效的QC组
    const validQcGroups = computed(() => {
      if (!freshlyQcGroups.value || !Array.isArray(freshlyQcGroups.value)) {
        return []
      }
      
      return freshlyQcGroups.value.filter(group => 
        Array.isArray(group) && group.length > 0
      )
    })

    // 过滤下拉框显示状态
    const showFilterDropdown = ref(false)
    
    // 当前激活的过滤选项
    const activeFilter = ref('recommend')
    
    // 排序方向
    const sortDirection = ref('asc')
    
    // 切换过滤下拉框显示状态
    const toggleFilterDropdown = () => {
      // 恢复原有功能，不再区分移动端和PC端
      showFilterDropdown.value = !showFilterDropdown.value
      
      // 点击外部区域关闭下拉框
      if (showFilterDropdown.value) {
        setTimeout(() => {
          document.addEventListener('click', handleOutsideClick)
        }, 10)
      } else {
        document.removeEventListener('click', handleOutsideClick)
      }
    }
    
    // 设置激活的过滤选项
    const setActiveFilter = (filter) => {
      activeFilter.value = filter
      
      // 如果切换到价格排序，默认为升序
      if (filter === 'price' && sortDirection.value !== 'asc' && sortDirection.value !== 'desc') {
        sortDirection.value = 'asc'
      }
      
      // 如果选择的是seller，获取商家列表
      if (filter === 'seller') {
        fetchAllSellers();
        return; // 不关闭下拉框，等待用户选择商家
      }
      
      // 导航到ProductsTwo.vue，携带排序参数
      const queryParams = {
        sort: filter,
        direction: sortDirection.value
      };
      
      // 对recommend选项特殊处理，添加recommended=true参数
      if (filter === 'recommend') {
        queryParams.recommended = 'true';
      }
      
      router.push({
        name: 'products',
        query: queryParams
      });
    }
    
    /**
     * 获取所有商家数据
     */
    const fetchAllSellers = async () => {
      isLoadingSellers.value = true;

      try {
        const response = await apiClient.get('/omg/products/getAllMerchant');

        if (response && response.code === 200 && response.data) {
          // 处理商家数据 - 适配对象格式 {0: "OMG", 1: "Crazy Koala", ...}
          const sellersData = response.data;
          sellers.value = Object.entries(sellersData).map(([index, sellerName]) => ({
            id: `seller_${index}`,
            name: sellerName || `商家 ${parseInt(index) + 1}`,
            platform: sellerName, 
            logoUrl: null,
            description: `${sellerName}的商品`,
            originalIndex: index 
          }));

          showSellersModal.value = true; // 显示商家选择模态框
        } else {
          console.error('获取商家数据失败:', response ? response.msg : '未知错误');
          ElMessage.error(`获取商家数据失败: ${response ? response.msg : '未知错误'}`);
        }
      } catch (err) {
        console.error('获取商家数据错误:', err);
        ElMessage.error(`获取商家数据失败: ${err.message || '网络错误'}`);
      } finally {
        isLoadingSellers.value = false;
      }
    };

    /**
     * 选择商家并跳转到商品页
     */
    const selectSeller = (seller) => {
      selectedSeller.value = seller;

      // 跳转到商品页面并携带商家参数
      router.push({
        name: 'products',
        query: { 
          merchant: seller ? seller.name : undefined,  // 直接使用商家名称作为merchant参数
          sort: activeFilter.value,
          direction: sortDirection.value
        }
      });

      // 关闭商家选择模态框
      showSellersModal.value = false;
    };

    /**
     * 关闭商家选择模态框
     */
    const closeSellersModal = () => {
      showSellersModal.value = false;
    };
    
    // 刷新过滤
    const refreshFilter = () => {
      // 重置过滤选项
      activeFilter.value = 'recommend'
      sortDirection.value = 'asc'
      
      // 导航到ProductsTwo.vue，不携带排序参数
      router.push({
        name: 'products',
        query: {}
      })
      
      // 不关闭下拉框
    }
    
    // 处理点击外部区域
    const handleOutsideClick = (event) => {
      const filterIcon = document.querySelector('.filter-icon')
      const filterDropdown = document.querySelector('.filter-dropdown')
      
      if (filterIcon && filterDropdown) {
        // 如果点击的不是过滤图标或下拉框内的元素，则关闭下拉框
        if (!filterIcon.contains(event.target) && !filterDropdown.contains(event.target)) {
          showFilterDropdown.value = false
          document.removeEventListener('click', handleOutsideClick)
        }
      }
    }
    
    // 组件卸载时移除事件监听器
    onUnmounted(() => {
      document.removeEventListener('click', handleOutsideClick)
    })

    // 设置排序方向
    const setSortDirection = (direction) => {
      sortDirection.value = direction
      activeFilter.value = 'seller' // 自动选择seller作为过滤条件
      
      // 导航到ProductsTwo.vue，携带排序参数
      router.push({
        name: 'products',
        query: {
          sort: activeFilter.value,
          direction: direction
        }
      })
    }

    // 为CREATOR CHOICE部分添加数据
    const creatorVideos = ref([])
    const videosLoading = ref(false)
    const videosError = ref(null)

    // 获取视频数据
    const fetchCreatorVideos = async () => {
      videosLoading.value = true
      videosError.value = null
      
      try {
        const response = await videosApi.getHomeVideoList()
        
        if (response && response.code === 200 && Array.isArray(response.data)) {
          // 使用API返回的数据
          creatorVideos.value = response.data.map(video => ({
            ...video,
            // 确保有缩略图，如果API没有返回thumbnail，使用视频第一帧作为缩略图
            thumbnail: video.thumbnail || video.imageUrl || video.avatar || 'https://cdn.findqc.com/bt/20250607/mbme4p4h_lete.jpg'
          }))
        } else {
          // 如果API返回的数据格式不正确，使用模拟数据
          useDefaultVideoData()
        }
      } catch (err) {
        videosError.value = '获取视频数据失败'
        useDefaultVideoData()
      } finally {
        videosLoading.value = false
      }
    }
    
    // 使用默认模拟数据
    const useDefaultVideoData = () => {
      creatorVideos.value = [
      {
        id: 1,
        videoUrl: 'https://agtfind.oss-eu-central-1.aliyuncs.com/ylkj-uploads/2025/07/05/ebf080c2105e2d35e25e41ee8ab07b79.mp4',
        thumbnail: 'https://cdn.findqc.com/bt/20250607/mbme4p4h_lete.jpg',
        avatar: 'https://cdn.findqc.com/bt/20250607/mbme4p4h_lete.jpg',
        username: '@craig_love',
        description: 'The most satisfying job #fyp #satisfying #roadmarking'
      },
      {
        id: 2,
        videoUrl: 'https://agtfind.oss-eu-central-1.aliyuncs.com/ylkj-uploads/2025/07/05/ebf080c2105e2d35e25e41ee8ab07b79.mp4',
        thumbnail: 'https://cdn.findqc.com/bt/20250607/mbme4p4h_lete.jpg', 
        avatar: 'https://cdn.findqc.com/bt/20250607/mbme4p4h_lete.jpg',
        username: '@craig_love',
        description: 'The most satisfying job #fyp #satisfying #roadmarking'
      },
      {
        id: 3,
        videoUrl: 'https://agtfind.oss-eu-central-1.aliyuncs.com/ylkj-uploads/2025/07/05/ebf080c2105e2d35e25e41ee8ab07b79.mp4',
        thumbnail: 'https://cdn.findqc.com/bt/20250607/mbme4p4h_lete.jpg',
        avatar: 'https://cdn.findqc.com/bt/20250607/mbme4p4h_lete.jpg',
        username: '@craig_love',
        description: 'The most satisfying job #fyp #satisfying #roadmarking'
      },
      {
        id: 4,
        videoUrl: 'https://agtfind.oss-eu-central-1.aliyuncs.com/ylkj-uploads/2025/07/05/ebf080c2105e2d35e25e41ee8ab07b79.mp4',
        thumbnail: 'https://cdn.findqc.com/bt/20250607/mbme4p4h_lete.jpg',
        avatar: 'https://cdn.findqc.com/bt/20250607/mbme4p4h_lete.jpg',
        username: '@craig_love',
        description: 'The most satisfying job #fyp #satisfying #roadmarking'
      },
      {
        id: 5,
        videoUrl: 'https://agtfind.oss-eu-central-1.aliyuncs.com/ylkj-uploads/2025/07/05/ebf080c2105e2d35e25e41ee8ab07b79.mp4',
        thumbnail: 'https://cdn.findqc.com/bt/20250607/mbme4p4h_lete.jpg',
        avatar: 'https://cdn.findqc.com/bt/20250607/mbme4p4h_lete.jpg',
        username: '@craig_love',
        description: 'The most satisfying job #fyp #satisfying #roadmarking'
      },
      {
        id: 6,
        videoUrl: 'https://agtfind.oss-eu-central-1.aliyuncs.com/ylkj-uploads/2025/07/05/ebf080c2105e2d35e25e41ee8ab07b79.mp4',
        thumbnail: 'https://cdn.findqc.com/bt/20250607/mbme4p4h_lete.jpg',
        avatar: 'https://cdn.findqc.com/bt/20250607/mbme4p4h_lete.jpg',
        username: '@craig_love',
        description: 'The most satisfying job #fyp #satisfying #roadmarking'
      }
      ]
    }
    
    // 视频播放状态
    const playingVideoId = ref(null)
    
    // 播放视频
    const playCreatorVideo = (videoId) => {
      const videoElement = document.getElementById(`video-${videoId}`)
      const thumbnail = document.getElementById(`thumbnail-${videoId}`)
      const playButton = document.getElementById(`play-button-${videoId}`)
      
      if (!videoElement || !thumbnail || !playButton) {
        return
      }
      
      // 如果点击的是当前正在播放的视频，则切换播放/暂停状态
      if (playingVideoId.value === videoId) {
        if (videoElement.paused) {
          videoElement.play()
        } else {
          videoElement.pause()
        }
        return
      }
      
      // 如果有其他视频正在播放，先停止它
      if (playingVideoId.value !== null) {
        const prevVideo = document.getElementById(`video-${playingVideoId.value}`)
        const prevThumbnail = document.getElementById(`thumbnail-${playingVideoId.value}`)
        const prevPlayButton = document.getElementById(`play-button-${playingVideoId.value}`)
        
        if (prevVideo) {
          prevVideo.pause()
          prevVideo.currentTime = 0
          prevVideo.style.display = 'none'
        }
        
        if (prevThumbnail) prevThumbnail.style.display = 'block'
        if (prevPlayButton) prevPlayButton.style.display = 'flex'
      }
      
      // 设置当前播放的视频ID
      playingVideoId.value = videoId
      
      // 隐藏缩略图和播放按钮
      thumbnail.style.display = 'none'
      playButton.style.display = 'none'
      
      // 显示视频并播放
      videoElement.style.display = 'block'
      
      // 确保视频适合容器
      videoElement.style.objectFit = 'cover'
      
      // 尝试播放视频
      try {
        const playPromise = videoElement.play()
        
        if (playPromise !== undefined) {
          playPromise
            .then(() => {
              videoElement.style.display = 'block'
              thumbnail.style.display = 'none'
              playButton.style.display = 'flex'
              playingVideoId.value = videoId
            })
            .catch(err => {
              console.log("视频播放失败", err);
              thumbnail.style.display = 'block'
              playButton.style.display = 'flex'
              videoElement.style.display = 'none'
              playingVideoId.value = null
            })
        }
      } catch (err) {
        thumbnail.style.display = 'block'
        playButton.style.display = 'flex'
        videoElement.style.display = 'none'
        playingVideoId.value = null
      }
      
      // 视频结束后恢复缩略图
      videoElement.onended = () => {
        videoElement.style.display = 'none'
        thumbnail.style.display = 'block'
        playButton.style.display = 'flex'
        playingVideoId.value = null
      }
    }

    // 轮播相关方法
    const scrollCreatorCarousel = (direction) => {
      const container = document.querySelector('.creator-choice-slides')
      if (!container) return
      
      const slideWidth = container.querySelector('.creator-choice-slide')?.offsetWidth || 0
      const gap = 20 // 设定的间距
      const scrollAmount = slideWidth + gap // 每次滚动一个视频的宽度加上间距
      
      // 当前滚动位置
      const currentScroll = container.scrollLeft
      
      if (direction === 'prev') {
        // 向前滚动一个视频的宽度
        container.scrollTo({ 
          left: Math.max(currentScroll - scrollAmount, 0), 
          behavior: 'smooth' 
        })
      } else {
        // 向后滚动一个视频的宽度
        const maxScroll = container.scrollWidth - container.clientWidth
        container.scrollTo({ 
          left: Math.min(currentScroll + scrollAmount, maxScroll), 
          behavior: 'smooth' 
        })
      }
    }

    // 点击轮播图跳转到帖子详情
    const handleBannerClick = async (banner) => {
      if (!banner || !banner.postId) return
      
      try {
        // 显示加载提示
        const loadingToast = window.ElMessage ? 
          window.ElMessage.loading('加载帖子详情中...', 0) :
          { close: () => {} }
          
        // 调用帖子详情API
        const response = await postsApi.getPostDetail(banner.postId)
        
        // 关闭加载提示
        loadingToast.close()
        
        if (response && response.code === 200 && response.data) {
          // 成功获取帖子数据，跳转到帖子详情页
          router.push({
            name: 'post-detail',
            params: {
              id: banner.postId
            }
          })
        } else {
          // API返回错误
          window.ElMessage && window.ElMessage.error('获取帖子详情失败：' + (response?.msg || '未知错误'))
        }
      } catch (err) {
        window.ElMessage && window.ElMessage.error('获取帖子详情失败：' + (err.message || '未知错误'))
      }
    }

    // 处理搜索
    const handleSearch = () => {
      if (searchQuery.value.trim()) {
        // 导航到产品页面，携带搜索参数
        router.push({
          path: '/products',
          query: { keyword: searchQuery.value.trim() }
        });
      }
    };

    // 强制重新渲染组件
    const forceRerender = () => {
      if (typeof window !== 'undefined') {
        // Use nextTick to force re-render in Vue 3
        import('vue').then(vue => {
          vue.nextTick(() => {
            // Force reactive updates by making a small change to state
            // This is a safe way to trigger re-rendering in Vue 3
            const temp = currentBannerIndex.value;
            currentBannerIndex.value = -1;
            setTimeout(() => {
              currentBannerIndex.value = temp;
            }, 0);
          });
        });
      }
    };

    // 处理用户点击轮播指示器的事件
    const handleIndicatorClick = (index) => {
      // 如果当前正在播放视频，需要停止
      if (isVideoPlaying.value) {
        // 查找当前的视频元素
        const currentVideoElement = isMobile.value 
          ? document.getElementById(`banner-video-mobile-${currentBannerIndex.value}`)
          : document.getElementById(`banner-video-${currentBannerIndex.value}`);
        
        // 如果找到了视频元素，停止播放
        if (currentVideoElement) {
          currentVideoElement.pause();
          currentVideoElement.currentTime = 0; // 重置播放位置
        }
        
        // 重置播放状态
        isVideoPlaying.value = false;
      }
      
      // 重置视频结束后轮播图标志
      isPostVideoSlide.value = false;
      
      // 记录从哪个索引切换到哪个索引，便于调试
      console.log(`用户点击指示器，从轮播项${currentBannerIndex.value}切换到${index}`);
      
      // 设置新的轮播索引
      currentBannerIndex.value = index;
      
      // 重新启动轮播计时器
      stopBannerAutoplay();
      startBannerAutoplay();
      
      // 检查是否切换到了视频，如果是则尝试播放
      setTimeout(() => {
        checkAndPlayVideo();
      }, 100);
    };

    return {
      products,
      loading,
      error,
      categories,
      selectedCategory,
      showAllCategories,
      categoryBrands,
      selectedBrand,
      brandsByCategory,
      isMobile,
      isDarkMode,
      searchQuery,
      handleSearch,
      freshlyQcGroups,
      currentImageIndex,
      nextImage,
      maxImagesPerGroup,
      toggleCategoryDropdown,
      toggleCategoriesVisibility,
      selectBrand,
      dropdownPosition,
      qcLoading,
      validQcGroups,
      getCurrentImageForGroup,
      getImageUrl,
      getImageAlt,
      handleImageError,
      setupCategoriesStyle,
      showFilterDropdown,
      activeFilter,
      toggleFilterDropdown,
      setActiveFilter,
      refreshFilter,
      sortDirection,
      setSortDirection,
      creatorVideos,
      scrollCreatorCarousel,
      playCreatorVideo,
      playingVideoId,
      showCategoryMenu,
      activeSidebarItem,
      categoryItems,
      currentCategoryItems,
      toggleCategoryMenu,
      setActiveSidebarItem,
      selectCategoryItem,
      videosLoading,
      videosError,
      // 轮播图相关
      banners,
      bannersLoading,
      bannersError,
      currentBannerIndex,
      nextBanner,
      prevBanner,
      currentBanner,
      getCurrentBannerStyle,
      handleBannerClick,
      isVideoPlaying,
      isPostVideoSlide,
      handleVideoPlayPause,
      handleVideoEnded,
      handleIndicatorClick,
      // 添加商品详情导航方法到返回对象
      goToProductDetail,
      // 商家相关
      sellers,
      isLoadingSellers,
      showSellersModal,
      selectedSeller,
      fetchAllSellers,
      selectSeller,
      closeSellersModal,
      route // 添加route到返回值
    }
  }
}
</script>

<style scoped>
/* ================ 全局页面样式 ================ */
.trending-products-view {
  color: #fff;
  min-height: 100vh;
}

.dark-bg {
  background: linear-gradient(126.54deg, rgba(35, 15, 72, 1) 0%, rgba(23, 10, 48, 1) 31.26%, rgba(7, 12, 31, 1) 70.66%, rgba(37, 15, 74, 1) 100%);
  padding: 40px 20px;
  min-height: 100vh;
  transition: background 0.5s ease;
}

/* 添加浅色主题样式 */
[data-theme="light"] .dark-bg {
  background: linear-gradient(131.67deg, rgba(240, 213, 252, 1) 0%, rgba(252, 247, 255, 1) 31.26%, rgba(249, 245, 252, 1) 70.66%, rgba(249, 245, 252, 1) 100%);
  color: #333;
}

/* 添加light-bg类样式 */
.light-bg {
  background: linear-gradient(131.67deg, rgba(240, 213, 252, 1) 0%, rgba(252, 247, 255, 1) 31.26%, rgba(249, 245, 252, 1) 70.66%, rgba(249, 245, 252, 1) 100%) !important;
  color: #333 !important;
}

/* ================ Beyond The Spreadsheet 顶部区域 ================ */
.hero-section {
  max-width: 1500px;
  margin: 0 auto 60px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 40px;
}

/* --- 左侧内容区域 --- */
.hero-content {
  flex: 1;
  max-width: 500px;
}

.hero-title {
  font-size: 42px;
  font-weight: 700;
  margin-bottom: 8px;
  color: #ffffff;
  line-height: 1.2;
  white-space: nowrap; /* 添加这一行，确保标题不会换行 */
}

.hero-subtitle {
  font-size: 22px;
  color: #ffffff;
  margin-bottom: 30px;
  font-weight: 400;
}

/* --- 搜索框 --- */
.search-container {
  margin-bottom: 24px;
  position: relative;
}

.search-icon {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: rgba(255, 255, 255, 0.5);
  z-index: 2;
}

.search-input {
  background-color: rgba(30, 20, 50, 0.6);
  border: none;
  outline: none;
  color: #ffffff;
  font-size: 16px;
  width: 92%;
  padding: 12px 16px 12px 45px;
  border-radius: 12px;
}

.search-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

/* --- 分类过滤器 --- */
.category-filter {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20px;
  position: relative;
  overflow: visible;
}

.filter-icon {
  margin-right: 16px;
  margin-top: 0;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: rgba(142, 45, 226, 0.3);
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  flex-shrink: 0;
}

.filter-icon:hover {
  background-color: rgba(142, 45, 226, 0.5);
  transform: scale(1.05);
}

.filter-icon svg {
  transition: transform 0.3s ease;
  color: white;
}

.filter-icon svg.rotate {
  transform: rotate(180deg);
}

.filter-chips-container {
  flex: 1;
  max-width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.filter-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  max-width: 100%;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.filter-chip {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 10px;
  white-space: nowrap;
  position: relative;
  display: flex;
  align-items: center;
  gap: 5px;
  border: 1px solid transparent;
}

/* 浅色模式下的分类按钮样式 */
[data-theme="light"] .filter-chip {
  background-color: rgba(240, 213, 252, 0.3);
  color: #333;
  border: 1px solid rgba(142, 45, 226, 0.3);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.filter-chip:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

/* 浅色模式下的分类按钮悬停样式 */
[data-theme="light"] .filter-chip:hover {
  background-color: rgba(240, 213, 252, 0.5);
  border-color: rgba(142, 45, 226, 0.5);
  box-shadow: 0 2px 5px rgba(142, 45, 226, 0.1);
}

.filter-chip.active {
  background-color: rgba(142, 45, 226, 0.6);
  color: white;
}

/* 浅色模式下的分类按钮选中样式 */
[data-theme="light"] .filter-chip.active {
  background-color: rgba(142, 45, 226, 0.2);
  color: #5a33a0;
  border-color: rgba(142, 45, 226, 0.6);
  box-shadow: 0 2px 8px rgba(142, 45, 226, 0.15);
}

.filter-chip.more-button {
  display: flex;
  align-items: center;
  gap: 5px;
  background-color: rgba(255, 255, 255, 0.15);
}

.filter-chip.more-button:hover {
  background-color: rgba(255, 255, 255, 0.25);
}

.filter-chip.more-button svg {
  transition: transform 0.3s ease;
}

.filter-chip.more-button svg.rotate {
  transform: rotate(180deg);
}

.expand-indicator {
  position: absolute;
  width: 6px;
  height: 6px;
  background-color: #8e2de2;
  border-radius: 50%;
  bottom: 1px;
  right: 1px;
  transition: all 0.3s ease;
  opacity: 0;
  transform: scale(0);
}

.expand-indicator.expanded {
  opacity: 1;
  transform: scale(1);
}

/* --- 右侧滑块区域 --- */
.hero-slider {
  flex: 1;
  position: relative;
  background: linear-gradient(135deg, #6b3294 0%, #8540ac 100%);
  border-radius: 12px;
  height: 440px;
  overflow: hidden;
}

.slider-controls {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 100%;
  display: flex;
  justify-content: space-between;
  z-index: 10;
}

.slider-arrow {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: white;
  transition: background 0.2s;
}

.slider-arrow:hover {
  background: rgba(255, 255, 255, 0.3);
}

.slider-content {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  padding: 20px;
  background-size: cover;
  background-position: center;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  cursor: pointer; /* 添加指针样式 */
  transition: transform 0.3s ease; /* 添加过渡效果 */
}

.banner-video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: 1;
}

.slider-content:hover {
  transform: scale(1.01); /* 添加轻微放大效果 */
}

.slider-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.7) 0%, rgba(0, 0, 0, 0.3) 50%, rgba(0, 0, 0, 0) 100%);
  z-index: 1;
}

.slider-title {
  font-size: 22px;
  color: #FFFFFF !important; /* 强制使用白色，覆盖App.vue样式 */
  margin-bottom: 8px;
  font-weight: 500;
  position: relative;
  z-index: 2;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
  text-decoration: none !important; /* 确保没有下划线 */
}

.slider-actions {
  display: flex;
  gap: 24px;
  position: relative;
  z-index: 2;
  margin-top: 8px;
}

.action-button {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 0;
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: center;
  transition: transform 0.2s ease;
  gap: 4px;
  min-width: 40px;
}

.action-button:hover {
  transform: scale(1.05);
}

.action-button svg {
  width: 18px;
  height: 18px;
  stroke-width: 2;
}

.action-button .count {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.95) !important;
  font-weight: 500;
  line-height: 1;
  position: relative;
  top: 1px;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}

/* ================ TRENDING NOW 产品展示区域 ================ */
.trending-products-container {
  max-width: 1400px;
  margin: 0 auto 60px;
  position: relative;
}

.header-section {
  margin-bottom: 40px;
  text-align: center;
}

.trending-title {
  font-size: 32px;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 16px;
  letter-spacing: 1px;
  text-transform: uppercase;
}

.discount-code {
  color: #e0e0e0;
  font-size: 18px;
  margin-bottom: 30px;
  background: rgba(142, 45, 226, 0.2);
  width: fit-content;
  margin: 0 auto;
  padding: 8px 20px;
  border-radius: 20px;
  backdrop-filter: blur(5px);
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 16px;
  padding: 0 5px;
}

/* ================ FRESHLY QC 质检展示区域 ================ */
.freshly-qc-container {
  max-width: 1400px;
  margin: 0 auto 0px;
  position: relative;
  padding: 40px 20px 10px;
}

.freshly-qc-title {
  font-size: 32px;
  font-weight: 600;
  color: #ffffff;
  text-transform: uppercase;
  text-align: center;
  margin-bottom: 30px;
  transition: color 0.3s ease;
}

/* 浅色模式下的标题 */
[data-theme="light"] .freshly-qc-title {
  color: #333333;
}

.qc-groups-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  padding: 0 15px 20px;
  width: 100%;
}

.qc-group {
  width: 100%;
  background-color: rgba(30, 9, 64, 0.8);
  border-radius: 12px;
  overflow: hidden;
  transition: transform 0.3s ease, background-color 0.3s ease;
  position: relative;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

/* 浅色模式下的QC组卡片 */
[data-theme="light"] .qc-group {
  background-color: #f9f5fc;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.qc-group:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.5);
}

/* 浅色模式下的QC组卡片悬停效果 */
[data-theme="light"] .qc-group:hover {
  box-shadow: 0 8px 25px rgba(142, 45, 226, 0.15);
}

.qc-group-header {
  background-color: rgba(77, 48, 105, 1);
  color: #ffffff;
  padding: 12px 15px;
  font-size: 14px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
  transition: background-color 0.3s ease;
}

/* 浅色模式下的QC组标题栏 */
[data-theme="light"] .qc-group-header {
  background-color: #c197d1;
  color: #333333;
}

.group-count {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  padding: 3px 10px;
  font-size: 12px;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* 浅色模式下的计数标签 */
[data-theme="light"] .group-count {
  background: rgba(142, 45, 226, 0.15);
  color: #333333;
}

.qc-group-content {
  height: 320px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(30, 9, 64, 0.8);
  padding: 0;
  position: relative;
  overflow: hidden;
  transition: background-color 0.3s ease;
}

/* 浅色模式下的QC组内容区域 */
[data-theme="light"] .qc-group-content {
  background-color: #f9f5fc;
}

.qc-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.4s ease;
}

.qc-group:hover .qc-image {
  transform: scale(1.02);
}

.nav-button {
  display: none;
}

/* 网格布局的QC组样式 */
/* 优化横向滚动轮播组的样式 */
.qc-groups-grid::-webkit-scrollbar {
  height: 4px;
}

.qc-groups-grid::-webkit-scrollbar-track {
  background: rgba(20, 20, 30, 0.5);
  border-radius: 2px;
}

.qc-groups-grid::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #352458, #5a33a0);
  border-radius: 2px;
}

/* 浅色模式下的滚动条 */
[data-theme="light"] .qc-groups-grid::-webkit-scrollbar-track {
  background: rgba(240, 213, 252, 0.3);
}

[data-theme="light"] .qc-groups-grid::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #c197d1, #8e2de2);
}

.qc-groups-grid::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #42306a, #6a43b0);
}

/* 浅色模式下的滚动条悬停效果 */
[data-theme="light"] .qc-groups-grid::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #d1a7e1, #9e3df2);
}

/* 添加滚动条底部的渐变效果 */
.qc-groups-grid::after {
  content: '';
  display: block;
  width: 100%;
  height: 4px;
  position: absolute;
  bottom: 0;
  left: 0;
  background: linear-gradient(90deg, #352458, #5a33a0, #352458);
  opacity: 0.4;
  border-radius: 2px;
  transition: background 0.3s ease;
}

/* 浅色模式下的滚动条底部渐变 */
[data-theme="light"] .qc-groups-grid::after {
  background: linear-gradient(90deg, #c197d1, #8e2de2, #c197d1);
  opacity: 0.3;
}

/* ================ 响应式设计 ================ */
@media (max-width: 1200px) {
  /* 大屏幕响应式调整 */
  .products-grid {
    grid-template-columns: repeat(4, 1fr);
  }
  
  .hero-section {
    flex-direction: column;
  }
  
  .hero-content {
    max-width: 100%;
  }
}

@media (max-width: 992px) {
  /* 中等屏幕响应式调整 */
  .products-grid {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .qc-groups-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 768px) {
  /* 平板屏幕响应式调整 */
  .hero-section {
    flex-direction: column;
    gap: 0px;
    margin-bottom: 40px;
  }
  
  .hero-content {
    max-width: 100%;
  }
  
  .hero-slider {
    width: 100%;
    margin-top: 20px;
  }
  
  .products-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    padding: 0 10px;
  }
  
  .qc-groups-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .trending-title, .freshly-qc-title {
    font-size: 24px;
  }
  
  .discount-code {
    font-size: 16px;
  }
  
  .hero-title {
    font-size: 36px;
    white-space: nowrap; /* 添加这一行，确保标题在平板设备上也不会换行 */
  }
  
  .hero-subtitle {
    font-size: 18px;
  }
}

@media (max-width: 480px) {
  /* 手机屏幕响应式调整 */
  .dark-bg {
    padding: 20px 0;
  }
  
  /* Hero标题区域调整 */
  .hero-section {
    flex-direction: column;
    padding: 0 20px;
  }
  
  .hero-content {
    max-width: 100%;
    width: 100%;
  }
  
  .hero-title {
    font-size: 28px;
    text-align: center;
    white-space: nowrap; /* 添加这一行，确保标题在移动端也不会换行 */
  }
  
  .hero-subtitle {
    font-size: 16px;
    text-align: center;
  }
  
  /* 搜索框调整 */
  .search-container {
    padding: 0;
    margin-bottom: 15px;
  }
  
  .search-input {
    height: 20px;
    border-radius: 27px;
    margin-left:-11px
  }
  
  /* 过滤器调整 */
  .category-filter {
    padding: 0 10px;
    justify-content: flex-start;
    margin-bottom: 15px;
    flex-wrap: wrap;
  }
  
  .filter-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 36px;
    height: 36px;
    margin-right: 10px;
    margin-bottom: 10px;
    background-color: rgba(142, 45, 226, 0.3);
    border-radius: 50%;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  }
  
  .filter-chips-container {
    width: 100%;
    overflow-x: visible;
    padding-left: 0;
  }
  
  .filter-chips {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    padding-bottom: 5px;
    width: 100%;
  }
  
  .filter-chip {
    margin-bottom: 5px;
    white-space: nowrap;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 13px;
  }
  
  /* 轮播图区域调整 */
  .hero-slider {
    width: 100%;
    height: 200px;
    margin-top: 20px;
    border-radius: 15px;
    overflow: hidden;
    position: relative;
    background: linear-gradient(135deg, #6b3294 0%, #8540ac 100%);
  }
  
  .slider-controls {
    z-index: 10;
  }
  
  .slider-content {
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 15px;
    z-index: 5;
  }
  
  .slider-title {
    font-size: 18px;
    margin-bottom: 10px;
    color: white;
  }
  
  .slider-actions {
    display: flex;
    gap: 12px;
  }
  
  .action-button {
    background: rgba(255, 255, 255, 0.15);
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .slider-arrow {
    width: 32px;
    height: 32px;
  }
  
  /* 品牌下拉菜单调整 */
  .brands-dropdown-container.mobile-dropdown {
    position: fixed;
    top: auto;
    left: 50%;
    transform: translateX(-50%);
    width: 90%;
    max-width: 350px;
    z-index: 1000;
    border-radius: 12px;
    box-shadow: 0 5px 25px rgba(0, 0, 0, 0.5);
  }
  
  .mobile-dropdown .brands-dropdown {
    border-radius: 12px;
    max-height: 60vh;
    overflow-y: auto;
    padding-bottom: 10px;
    background-color: rgba(30, 9, 64, 0.9);
  }
  
  .mobile-dropdown .brands-dropdown::before {
    display: none;
  }
  
  .mobile-dropdown .dropdown-header {
    padding: 15px;
    background-color: rgba(77, 48, 105, 1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    position: sticky;
    top: 0;
    z-index: 2;
  }
  
  .mobile-dropdown .brand-dropdown-item {
    padding: 14px 20px;
    font-size: 15px;
    text-align: left;
  }
  
  /* Trending部分调整 */
  .trending-products-container {
    padding: 0;
    margin-top: -65px;
  }
  
  .header-section {
    margin-bottom: 20px;
  }
  
  .trending-title {
    font-size: 24px;
    text-align: center;
    margin-bottom: 5px;
  }
  
  .discount-code {
    font-size: 14px;
    padding: 6px 16px;
  }
  
  .products-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    margin: 0 auto;
    padding: 0 10px;
    justify-items: center;
  }
  
  /* QC组调整为水平滚动 */
  .qc-groups-grid {
    display: flex;
    grid-template-columns: unset;
    overflow-x: auto;
    overflow-y: hidden;
    scroll-snap-type: x mandatory;
    -webkit-overflow-scrolling: touch;
    padding: 10px 20px 20px;
    gap: 15px;
    max-width: calc(100vw - 40px);
    margin: 0 auto;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
    position: relative;
  }
  
  .qc-groups-grid::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }
  
  .qc-group {
    flex: 0 0 80%;
    min-width: 250px;
    max-width: 300px;
    scroll-snap-align: start;
    margin-right: 5px;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    position: relative;
  }
  
  /* 浅色模式下的QC组卡片 */
  [data-theme="light"] .qc-group {
    box-shadow: 0 5px 15px rgba(142, 45, 226, 0.1);
  }
  
  .qc-group-content {
    height: 280px;
    position: relative;
  }
  
  /* 修改QC轮播控制按钮样式和位置 */
  .qc-carousel-controls {
    display: none !important;
  }
  
  .qc-carousel-btn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 32px;
    height: 32px;
    background: rgba(90, 51, 160, 0.7);
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    cursor: pointer;
    pointer-events: auto;
    z-index: 10;
  }
  
  /* 浅色模式下的按钮 */
  [data-theme="light"] .qc-carousel-btn {
    background: rgba(193, 151, 209, 0.7);
  }
  
  .qc-carousel-btn.prev {
    left: 5px;
  }
  
  .qc-carousel-btn.next {
    right: 5px;
  }
  
  /* 调整移动端商品卡片尺寸为223*302 */
  :deep(.product-card) {
    margin-bottom: 0;
    border-radius: 16px;
    width: 223px;
    height: 302px;
    display: flex;
    flex-direction: column;
  }
  
  :deep(.main-image-container) {
    min-height: 200px;
    height: 200px;
  }
  
  :deep(.product-info-section) {
    padding: 8px 10px;
    height: 102px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
  
  :deep(.current-price) {
    font-size: 22px;
    font-weight: 700;
  }
  
  :deep(.product-id) {
    font-size: 10px;
    padding: 1px 6px;
  }
  
  :deep(.product-name) {
    margin-bottom: 4px;
    font-size: 12px;
    max-height: 36px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
  
  :deep(.social-stats) {
    gap: 10px;
    font-size: 11px;
  }
  
  :deep(.stat-container i) {
    font-size: 12px;
  }
}

/* 品牌下拉菜单样式 */
.brands-dropdown-container {
  position: absolute;
  z-index: 100;
  width: 180px;
}

.brands-dropdown-container.mobile-dropdown {
  border-radius: 12px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.5);
  z-index: 200;
}

.brands-dropdown {
  background-color: rgba(30, 9, 64, 0.9);
  border-radius: 12px;
  padding: 5px 0;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  animation: fadeIn 0.3s ease;
  overflow: hidden;
  position: relative;
}

/* 浅色模式下的品牌下拉框 */
[data-theme="light"] .brands-dropdown {
  background-color: #f9f5fc;
  border: 1px solid rgba(142, 45, 226, 0.2);
  box-shadow: 0 5px 15px rgba(142, 45, 226, 0.15);
}

.mobile-dropdown .brands-dropdown {
  border-radius: 12px;
  padding-top: 0;
  max-height: 300px;
  overflow-y: auto;
  background-color: rgba(30, 9, 64, 0.9);
}

/* 浅色模式下的移动端品牌下拉框 */
[data-theme="light"] .mobile-dropdown .brands-dropdown {
  background-color: #f9f5fc;
}

.dropdown-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background-color: rgba(77, 48, 105, 1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* 浅色模式下的下拉框标题栏 */
[data-theme="light"] .dropdown-header {
  background-color: #e8d9f3;
  border-bottom: 1px solid rgba(142, 45, 226, 0.1);
}

.dropdown-header span {
  font-weight: 500;
  font-size: 14px;
}

/* 浅色模式下的下拉框标题文字 */
[data-theme="light"] .dropdown-header span {
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 16px;
  cursor: pointer;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background 0.2s;
}

/* 浅色模式下的关闭按钮 */
[data-theme="light"] .close-btn {
  color: #333;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

/* 浅色模式下的关闭按钮悬停效果 */
[data-theme="light"] .close-btn:hover {
  background: rgba(142, 45, 226, 0.1);
}

.brands-dropdown::before {
  content: '';
  position: absolute;
  top: -6px;
  left: 20px;
  width: 12px;
  height: 12px;
  background-color: rgba(30, 9, 64, 0.9);
  transform: rotate(45deg);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  border-left: 1px solid rgba(255, 255, 255, 0.1);
}

/* 浅色模式下的小三角 */
[data-theme="light"] .brands-dropdown::before {
  background-color: #f9f5fc;
  border-top: 1px solid rgba(142, 45, 226, 0.2);
  border-left: 1px solid rgba(142, 45, 226, 0.2);
}

.mobile-dropdown .brands-dropdown::before {
  left: 15px;
}

.brand-dropdown-item {
  padding: 10px 20px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: white;
  font-size: 14px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  text-align: center;
}

/* 浅色模式下的品牌选项 */
[data-theme="light"] .brand-dropdown-item {
  color: #333;
  border-bottom: 1px solid rgba(142, 45, 226, 0.05);
}

.mobile-dropdown .brand-dropdown-item {
  padding: 12px 15px;
  font-size: 14px;
  text-align: left;
}

.brand-dropdown-item:hover {
  background-color: rgba(142, 45, 226, 0.2);
}

/* 浅色模式下的品牌选项悬停效果 */
[data-theme="light"] .brand-dropdown-item:hover {
  background-color: rgba(240, 213, 252, 0.5);
}

.brand-dropdown-item.active {
  background-color: rgba(142, 45, 226, 0.4);
  font-weight: 500;
}

/* 浅色模式下的品牌选项选中效果 */
[data-theme="light"] .brand-dropdown-item.active {
  background-color: rgba(142, 45, 226, 0.2);
  color: #5a33a0;
  font-weight: 500;
}

/* 移动端轮播图样式 */
.mobile-hero-slider {
  width: calc(112% - 20px);
  height: 220px;
  margin: 20px -13px 30px;
  border-radius: 15px;
  background: linear-gradient(135deg, #6b3294 0%, #8540ac 100%);
  position: relative;
  overflow: hidden;
}

.mobile-slider-content {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  padding: 15px;
  background-size: cover;
  background-position: center;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  cursor: pointer; /* 添加指针样式 */
}

.mobile-slider-title {
  font-size: 20px;
  color: #FFFFFF !important; /* 强制使用白色，覆盖App.vue样式 */
  margin-bottom: 6px;
  font-weight: 500;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
  position: relative;
  z-index: 2;
  text-decoration: none !important; /* 确保没有下划线 */
}

.mobile-slider-actions {
  display: flex;
  gap: 24px;
  position: relative;
  z-index: 2;
  margin-top: 10px;
}

.mobile-action-button {
  background: none;
  border: none;
  color: white;
  padding: 0;
  cursor: pointer;
  line-height: 0;
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: center;
  transition: transform 0.2s ease;
  gap: 4px;
  min-width: 36px;
}

.mobile-action-button:hover {
  transform: scale(1.05);
}

.mobile-action-button svg {
  width: 22px;
  height: 22px;
  stroke-width: 2.5;
  stroke: #FFFFFF !important; /* 强制使用白色，覆盖App.vue样式 */
}

.mobile-action-button .count {
  font-size: 12px;
  color: #FFFFFF !important; /* 强制使用白色，覆盖App.vue样式 */
  font-weight: 500;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}

.mobile-slider-arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  padding: 0;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  z-index: 10;
  cursor: pointer;
  opacity: 0.8;
}

.mobile-slider-arrow.prev {
  left: 5px;
}

.mobile-slider-arrow.next {
  right: 5px;
}

/* 背景遮罩 */
.dropdown-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 100;
  animation: fadeIn 0.2s ease;
}

/* QC加载状态样式 */
.qc-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 250px;
  width: 100%;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(142, 45, 226, 0.2);
  border-top: 4px solid #8e2de2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.qc-loading p {
  color: #bbb;
  font-size: 16px;
}

/* QC轮播控制按钮 */
.qc-carousel-controls {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 0;
  display: flex;
  justify-content: space-between;
  padding: 0 15px;
  z-index: 30;
  pointer-events: none;
}

.qc-carousel-btn {
  width: 40px;
  height: 40px;
  background: rgba(90, 51, 160, 0.7);
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  top: 150px;
  pointer-events: auto;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3);
}

/* 浅色模式下的QC轮播按钮 */
[data-theme="light"] .qc-carousel-btn {
  background: rgba(193, 151, 209, 0.7);
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.qc-carousel-btn:hover {
  background: rgba(90, 51, 160, 0.9);
  transform: scale(1.05);
}

/* 浅色模式下的QC轮播按钮悬停效果 */
[data-theme="light"] .qc-carousel-btn:hover {
  background: rgba(193, 151, 209, 0.9);
}

.qc-carousel-btn.prev {
  left: -30px;
}

.qc-carousel-btn.next {
  right: 0px;
}

/* 图片过渡效果 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 调整QC组容器和定位 */
.freshly-qc-container {
  position: relative;
  padding-top: 30px;
}

.qc-groups-grid {
  position: relative;
  padding: 10px 30px;
}

.qc-group-content {
  position: relative;
  overflow: hidden;
}

@media (max-width: 768px) {
  .qc-carousel-btn {
    width: 36px;
    height: 36px;
    top: 120px;
  }
  
  .qc-carousel-btn.prev {
    left: -15px;
  }
  
  .qc-carousel-btn.next {
    right: -15px;
  }
  
  .qc-groups-grid {
    padding: 10px 20px;
  }
}

/* QC空数据状态 */
.qc-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  width: 100%;
  background-color: rgba(43, 32, 73, 0.3);
  border-radius: 12px;
  margin-top: 20px;
}

.qc-empty p {
  color: #bbb;
  font-size: 16px;
}

/* 过滤下拉框 */
.filter-dropdown {
  position: absolute;
  top: 46px;
  left: 0;
  width: 100%;
  background: rgba(43, 32, 73, 0.98);
  border-radius: 12px;
  z-index: 100;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
  padding: 14px;
  animation: fadeIn 0.2s ease;
  backdrop-filter: blur(5px);
}

/* 浅色模式下的下拉框样式 */
[data-theme="light"] .filter-dropdown {
  background: rgba(249, 245, 252, 0.98);
  box-shadow: 0 8px 20px rgba(142, 45, 226, 0.15);
  border: 1px solid rgba(142, 45, 226, 0.2);
}

.filter-options {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-gap: 12px;
  padding: 2px;
}

.filter-option {
  height: 52px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(123, 74, 170, 0.5);
  color: white;
  font-size: 16px;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-align: center;
  padding: 0 15px;
  position: relative;
  letter-spacing: 0.5px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 浅色模式下的过滤选项样式 */
[data-theme="light"] .filter-option {
  background-color: rgba(240, 213, 252, 0.4);
  color: #333;
  border: 1px solid rgba(142, 45, 226, 0.2);
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);
}

.filter-option:hover {
  background-color: rgba(123, 74, 170, 0.7);
  box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

/* 浅色模式下的过滤选项悬停样式 */
[data-theme="light"] .filter-option:hover {
  background-color: rgba(240, 213, 252, 0.6);
  border-color: rgba(142, 45, 226, 0.3);
  box-shadow: 0 2px 8px rgba(142, 45, 226, 0.1);
}

.filter-option:active {
  transform: translateY(0);
}

.filter-option.active {
  background-color: rgba(142, 45, 226, 0.9);
  box-shadow: 0 3px 10px rgba(142, 45, 226, 0.3);
}

/* 浅色模式下的过滤选项选中样式 */
[data-theme="light"] .filter-option.active {
  background-color: rgba(142, 45, 226, 0.3);
  color: #5a33a0;
  border-color: rgba(142, 45, 226, 0.4);
  box-shadow: 0 2px 8px rgba(142, 45, 226, 0.15);
}

.seller-option {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  grid-column: 1;
}

.seller-option span {
  padding-left: 0;
  padding-right: 0;
}

.arrow-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
  position: absolute;
  right: 15px;
  top: 0;
  bottom: 0;
}

.arrow-up, .arrow-down {
  cursor: pointer;
  height: 14px;
  color: white;
  opacity: 0.7;
  transition: all 0.2s ease;
  margin: 1px 0;
}

.arrow-up:hover, .arrow-down:hover {
  opacity: 1;
  transform: scale(1.15);
  color: #fff;
}

.active .arrow-up, .active .arrow-down {
  opacity: 0.9;
}

@media (max-width: 480px) {
  .filter-dropdown {
    width: calc(100% - 20px);
    left: 10px;
    top: 42px;
  }
  
  .filter-options {
    grid-gap: 8px;
  }
  
  .filter-option {
    height: 46px;
    font-size: 15px;
    padding: 0 10px;
    border-radius: 8px;
  }
  
  .seller-option {
    grid-column: 1;
    grid-row: 3;
    justify-content: center;
    padding-left: 0;
    padding-right: 0;
  }
  
  .refresh {
    grid-column: 2;
    grid-row: 3;
    box-shadow: 0 2px 8px rgba(103, 58, 183, 0.2);
    background-color: rgba(114, 69, 193, 0.75);
  }
  
  .refresh:hover {
    background-color: rgba(114, 69, 193, 0.85);
  }
  
  .refresh-icon {
    transform: scale(0.9);
  }
  
  .arrow-container {
    right: 12px;
  }
}

/* 过滤下拉框背景遮罩 */
.filter-dropdown-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.2);
  z-index: 90;
  animation: fadeIn 0.2s ease;
}

/* 刷新按钮样式 */
.refresh {
  background-color: rgba(103, 58, 183, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.refresh:after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  background: rgba(255, 255, 255, 0.5);
  opacity: 0;
  border-radius: 100%;
  transform: scale(1, 1) translate(-50%, -50%);
  transform-origin: 50% 50%;
}

.refresh:focus:not(:active)::after {
  animation: ripple 1s ease-out;
}

@keyframes ripple {
  0% {
    transform: scale(0, 0) translate(-50%, -50%);
    opacity: 0;
  }
  20% {
    transform: scale(25, 25) translate(-50%, -50%);
    opacity: 0.2;
  }
  100% {
    opacity: 0;
    transform: scale(40, 40) translate(-50%, -50%);
  }
}

.refresh:hover {
  background-color: rgba(103, 58, 183, 0.85);
  box-shadow: 0 2px 12px 0 rgba(103, 58, 183, 0.2);
}

.refresh:active {
  background-color: rgba(103, 58, 183, 0.9);
  transform: scale(0.98);
}

.refresh-icon {
  transition: all 0.3s;
}

.refresh:hover .refresh-icon {
  transform: rotate(30deg);
}

.refresh:active .refresh-icon {
  transform: rotate(60deg);
}

/* PC端样式 - 确保商品卡片尺寸保持不变 */
@media (min-width: 481px) {
  .products-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 16px;
    padding: 0 5px;
  }
  
  :deep(.product-card) {
    width: 100%;
    height: auto;
    border-radius: 12px;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }
  
  :deep(.main-image-container) {
    width: 100%;
    position: relative;
    overflow: hidden;
    border-radius: 12px 12px 0 0;
  }
}

@media (max-width: 480px) {
  /* Trending部分调整 */
  .products-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 2px;
    margin: 0 auto;
    padding: 0 2px;
    justify-items: center;
  }
  
  /* 调整移动端商品卡片尺寸 */
  :deep(.product-card) {
    margin-bottom: 0;
    border-radius: 12px;
    width: 100%;
    max-width: 223px;
    height: 280px;
    display: flex;
    flex-direction: column;
  }
}

/* ================ CREATOR CHOICE 区域 ================ */
.creator-choice-container {
  max-width: 1400px;
  margin: 0 auto 80px;
  position: relative;
  padding: 40px 20px;
}

.creator-choice-title {
  font-size: 32px;
  font-weight: 600;
  color: #ffffff;
  text-transform: uppercase;
  text-align: center;
  margin-bottom: 30px;
  transition: color 0.3s ease;
}

/* 浅色模式下的标题颜色 */
[data-theme="light"] .creator-choice-title {
  color: #333333;
}

.creator-choice-carousel {
  position: relative;
  padding: 0 40px;
  margin: 0 auto;
  max-width: 1320px; /* Control overall width */
}

.carousel-arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 40px;
  height: 40px;
  background: rgba(90, 51, 160, 0.7);
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
  z-index: 10;
  transition: all 0.3s ease;
}

/* 浅色模式下的箭头按钮 */
[data-theme="light"] .carousel-arrow {
  background: rgba(193, 151, 209, 0.7);
}

.carousel-arrow:hover {
  background: rgba(90, 51, 160, 0.9);
  transform: translateY(-50%) scale(1.05);
}

/* 浅色模式下的箭头按钮悬停效果 */
[data-theme="light"] .carousel-arrow:hover {
  background: rgba(193, 151, 209, 0.9);
}

.carousel-arrow.prev {
  left: 0;
}

.carousel-arrow.next {
  right: 0;
}

.creator-choice-slides {
  display: flex;
  gap: 20px;
  overflow-x: hidden;
  scroll-snap-type: x mandatory;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  padding: 10px 0;
  scroll-behavior: smooth;
  width: 100%;
}

.creator-choice-slides::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.creator-choice-slide {
  flex: 0 0 calc(33.333% - 14px);
  width: calc(33.333% - 14px); /* Ensures exactly 3 videos visible */
  min-width: 0; /* Allows videos to shrink if needed */
  scroll-snap-align: start;
}

.creator-video {
  background-color: rgba(30, 9, 64, 0.8);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  transition: transform 0.3s ease, background-color 0.3s ease;
}

/* 浅色模式下的视频卡片背景 */
[data-theme="light"] .creator-video {
  background-color: #ffffff;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(142, 45, 226, 0.15);
}

.creator-video:hover {
  transform: translateY(-5px);
}

.creator-info {
  padding: 15px;
  position: relative;
  padding-top: 10px;
  padding-left: 65px;
}

.creator-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  position: absolute;
  bottom: -20px;
  left: 15px;
  border: 2px solid rgba(30, 9, 64, 0.8);
  z-index: 10;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  transition: border-color 0.3s ease;
}

/* 浅色模式下的头像边框 */
[data-theme="light"] .creator-avatar {
  border: 2px solid #f9f5fc;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.creator-name {
  color: #b99dff;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 5px;
  transition: color 0.3s ease;
}

/* 浅色模式下的创作者名称 */
[data-theme="light"] .creator-name {
  color: #8e2de2;
}

.creator-description {
  color: #ffffff;
  font-size: 13px;
  line-height: 1.4;
  opacity: 0.9;
  margin-bottom: 10px;
  transition: color 0.3s ease;
}

/* 浅色模式下的描述文字 */
[data-theme="light"] .creator-description {
  color: #333333;
}

@media (max-width: 992px) {
  .creator-choice-slide {
    flex: 0 0 calc(50% - 10px);
    width: calc(50% - 10px); /* 2 videos on medium screens */
  }
  
  .video-placeholder {
    height: 350px;
  }
}

@media (max-width: 768px) {
  .creator-choice-title {
    font-size: 24px;
    margin-bottom: 20px;
  }
  
  .creator-choice-carousel {
    padding: 0 30px;
  }
  
  .carousel-arrow {
    width: 36px;
    height: 36px;
  }
}

@media (max-width: 480px) {
  .creator-choice-container {
    padding: 30px 10px;
    margin-bottom: 60px;
  }
  
  .creator-choice-slide {
    flex: 0 0 calc(100% - 20px);
    width: calc(100% - 20px); /* 1 video on small screens */
  }
  
  .video-placeholder {
    height: 320px;
  }
  
  .creator-choice-carousel {
    padding: 0 20px;
  }
  
  .carousel-arrow {
    width: 32px;
    height: 32px;
  }
  
  .play-button {
    width: 50px;
    height: 50px;
  }
  
  .play-button svg {
    width: 40px;
    height: 40px;
  }
}

.creator-video-element {
  width: 100%;
  height: 100%;
  object-fit: cover;
  position: absolute;
  top: 0;
  left: 0;
  display: none;
  z-index: 5;
  background-color: #000;
  cursor: pointer;
}

@media (max-width: 480px) {
  .video-placeholder {
    height: 420px;
  }
}

.video-container {
  position: relative;
}

/* 视频容器样式 */

.top-buttons {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 20px;
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 5;
}

.category-button {
  background-color: rgba(35, 15, 72, 0.8);
  border: 1px solid #5a33a0;
  border-radius: 4px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 0;
}

.category-button:hover {
  background-color: rgba(90, 51, 160, 0.7);
  border-color: rgba(142, 45, 226, 0.8);
}

/* 浅色模式下的分类按钮 */
[data-theme="light"] .category-button {
  background-color: rgba(240, 213, 252, 0.8);
  border: 1px solid rgba(142, 45, 226, 0.3);
  box-shadow: 0 2px 8px rgba(142, 45, 226, 0.1);
}

.category-button:hover {
  background-color: rgba(90, 51, 160, 0.7);
  border-color: rgba(142, 45, 226, 0.8);
}

/* 浅色模式下的分类按钮悬停效果 */
[data-theme="light"] .category-button:hover {
  background-color: rgba(193, 151, 209, 0.7);
  border-color: rgba(142, 45, 226, 0.5);
}

.category-button:active {
  transform: scale(0.98);
}

.category-button svg {
  color: white;
  width: 20px;
  height: 20px;
}

/* 浅色模式下的分类按钮图标 */
[data-theme="light"] .category-button svg {
  color: #333;
}

.mobile-filter-category .filter-icon {
  width: 48px;
  height: 48px;
  min-width: 48px;
  background-color: #8e2de2;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.mobile-filter-category .filter-icon svg {
  width: 24px;
  height: 24px;
  color: white;
}

/* 浅色模式下的过滤图标 */
[data-theme="light"] .mobile-filter-category .filter-icon {
  background-color: #c197d1;
}

/* 移动端过滤下拉框 */
.mobile-filter-dropdown {
  position: absolute;
  top: 45px;
  left: -10px;
  width: calc(100% + 10px);
  background: rgba(30, 9, 64, 0.9);
  border-radius: 12px;
  z-index: 100;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
  padding: 10px;
  animation: fadeIn 0.2s ease;
}

/* 浅色模式下的移动端过滤下拉框 */
[data-theme="light"] .mobile-filter-dropdown {
  background: #f9f5fc;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(142, 45, 226, 0.2);
}

.mobile-filter-dropdown .filter-options {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-gap: 10px;
  padding: 2px;
}

.mobile-filter-dropdown .filter-option {
  height: 50px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(77, 48, 105, 1);
  color: white;
  font-size: 16px;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-align: center;
  padding: 0 0px;
  position: relative;
  letter-spacing: 0.5px;
}

/* 浅色模式下的过滤选项 */
[data-theme="light"] .mobile-filter-dropdown .filter-option {
  background-color: rgba(240, 213, 252, 0.5);
  color: #333;
  border: 1px solid rgba(142, 45, 226, 0.2);
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);
}

/* 添加移动端过滤选项悬停效果 */
.mobile-filter-dropdown .filter-option:hover {
  background-color: rgba(77, 48, 105, 0.8);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* 浅色模式下的过滤选项悬停效果 */
[data-theme="light"] .mobile-filter-dropdown .filter-option:hover {
  background-color: rgba(240, 213, 252, 0.7);
  border-color: rgba(142, 45, 226, 0.4);
  box-shadow: 0 2px 8px rgba(142, 45, 226, 0.1);
}

.mobile-filter-dropdown .filter-option.active {
  background-color: #8e2de2;
}

/* 浅色模式下的激活过滤选项 */
[data-theme="light"] .mobile-filter-dropdown .filter-option.active {
  background-color: rgba(142, 45, 226, 0.3);
  color: #5a33a0;
  border-color: rgba(142, 45, 226, 0.5);
  box-shadow: 0 2px 8px rgba(142, 45, 226, 0.15);
}

.mobile-filter-dropdown .seller-option {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  grid-column: 1;
}

.mobile-filter-dropdown .refresh {
  grid-column: 2;
  background-color: rgba(77, 48, 105, 1);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 浅色模式下的刷新按钮 */
[data-theme="light"] .mobile-filter-dropdown .refresh {
  background-color: rgba(240, 213, 252, 0.5);
}

/* 新增 CREATOR CHOICE 部分 */
.creator-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 250px;
  width: 100%;
}

.creator-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 250px;
  width: 100%;
}

/* 加载状态和错误状态的样式调整 */
.creator-loading p, .creator-error p {
  color: #bbb;
  font-size: 16px;
  margin-bottom: 10px;
  transition: color 0.3s ease;
}

/* 浅色模式下的加载和错误文字 */
[data-theme="light"] .creator-loading p, 
[data-theme="light"] .creator-error p {
  color: #666;
}

.retry-btn {
  background-color: rgba(103, 58, 183, 0.7);
  border: none;
  border-radius: 5px;
  padding: 10px 20px;
  color: white;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

/* 浅色模式下的重试按钮 */
[data-theme="light"] .retry-btn {
  background-color: rgba(142, 45, 226, 0.5);
}

.retry-btn:hover {
  background-color: rgba(103, 58, 183, 0.9);
}

/* 浅色模式下的重试按钮悬停效果 */
[data-theme="light"] .retry-btn:hover {
  background-color: rgba(142, 45, 226, 0.7);
}

.creator-loading .loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(142, 45, 226, 0.2);
  border-top: 4px solid #8e2de2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

/* 浅色模式下的加载动画 */
[data-theme="light"] .creator-loading .loading-spinner {
  border: 4px solid rgba(240, 213, 252, 0.3);
  border-top: 4px solid #c197d1;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 轮播图加载状态 */
.slider-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 250px;
  width: 100%;
}

/* 轮播图错误状态 */
.slider-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 250px;
  width: 100%;
}

/* 轮播图指示器 */
.slider-indicators {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-top: 10px;
}

.indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.indicator.active {
  background-color: #ffffff;
}

/* 移动端轮播图指示器 */
.mobile-slider-indicators {
  position: absolute;
  bottom: 10px;
  left: 0;
  width: 100%;
  display: flex;
  justify-content: center;
  gap: 8px;
  z-index: 10;
}

.mobile-slider-indicators .indicator {
  width: 8px;
  height: 8px;
}

.video-placeholder {
  height: 529px;
  width: 100%;
  max-width: 374px;
  position: relative;
  overflow: hidden;
  background-color: #000;
  margin: 0 auto;
  cursor: pointer;
  border-radius: 12px 12px 0 0;
}

/* 浅色模式下的视频封面背景 */
[data-theme="light"] .video-placeholder {
  background: #ffffff;
  border: 1px solid rgba(142, 45, 226, 0.2);
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.08);
}

.video-placeholder img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.creator-video:hover .video-placeholder img {
  transform: scale(1.05);
}

.play-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60px;
  height: 60px;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.3s ease;
  z-index: 4;
}

.play-button:hover {
  background-color: rgba(90, 51, 160, 0.8);
}

/* 浅色模式下的播放按钮悬停效果 */
[data-theme="light"] .play-button:hover {
  background-color: rgba(193, 151, 209, 0.8);
}

/* 浅色模式下的播放按钮 */
[data-theme="light"] .play-button {
  background-color: rgba(142, 45, 226, 0.3);
  box-shadow: 0 2px 10px rgba(142, 45, 226, 0.2);
}

.creator-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  position: absolute;
  bottom: -20px;
  left: 15px;
  border: 2px solid rgba(30, 9, 64, 0.8);
  z-index: 10;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  transition: border-color 0.3s ease;
}

.creator-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.category-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(30, 30, 45, 0.8);
  border: 1px solid rgba(90, 51, 160, 0.5);
  border-radius: 30px;
  color: white;
  padding: 12px 20px;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  flex: 1;
  margin-left: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  text-align: center;
  letter-spacing: 0.5px;
  font-weight: 500;
}

/* 浅色模式下的分类按钮 */
[data-theme="light"] .category-btn {
  background-color: rgba(240, 213, 252, 0.8);
  border: 1px solid rgba(142, 45, 226, 0.3);
  color: #333;
  box-shadow: 0 2px 8px rgba(142, 45, 226, 0.1);
}

.category-btn:hover {
  background-color: rgba(90, 51, 160, 0.7);
  border-color: rgba(142, 45, 226, 0.8);
}

/* 浅色模式下的分类按钮悬停效果 */
[data-theme="light"] .category-btn:hover {
  background-color: rgba(193, 151, 209, 0.7);
  border-color: rgba(142, 45, 226, 0.5);
}

.category-btn:active {
  transform: scale(0.98);
}

.category-btn svg {
  margin-right: 10px;
  width: 18px;
  height: 18px;
  display: none; /* 隐藏SVG图标，与截图一致 */
}

/* 移动端分类和过滤按钮 */
.mobile-filter-category {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: -37px;
  width: 100%;
  padding: 0 5px;
  position: relative;

}

/* 浅色模式下的移动端分类和过滤区域 */
[data-theme="light"] .mobile-filter-category {
  background-color: transparent;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dropdown-arrow {
  font-size: 10px;
  transition: transform 0.3s ease;
  opacity: 0.7;
}

.filter-chip.active .dropdown-arrow {
  transform: rotate(180deg);
  opacity: 1;
}

.brand-dropdown-item:last-child {
  border-bottom: none;
}

/* 添加轮播图标题和图标在浅色模式下的独立样式 */
/* 确保在任何模式下轮播图标题和数字都显示为白色 */
.slider-banner-styles {
  color: white !important;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}

/* 轮播图标题样式 - 同时适用于深色和浅色模式 */
.slider-title {
  font-size: 22px;
  color: white;
  margin-bottom: 8px;
  font-weight: 500;
  position: relative;
  z-index: 2;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}

/* 确保在浅色模式下标题保持白色 */
[data-theme="light"] .slider-title {
  color: white;
}

/* 轮播图图标数量样式 */
.action-button .count {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.95);
  font-weight: 500;
  line-height: 1;
  position: relative;
  top: 1px;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}

/* 确保在浅色模式下图标数量保持白色 */
[data-theme="light"] .action-button .count {
  color: rgba(255, 255, 255, 0.95);
}

/* 移动端轮播图标题样式 */
.mobile-slider-title {
  font-size: 20px;
  color: white;
  margin-bottom: 6px;
  font-weight: 500;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
  position: relative;
  z-index: 2;
}

/* 确保在浅色模式下移动端标题保持白色 */
[data-theme="light"] .mobile-slider-title {
  color: white;
}

/* 移动端轮播图图标数量样式 */
.mobile-action-button .count {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.95);
  font-weight: 500;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}

/* 确保在浅色模式下移动端图标数量保持白色 */
[data-theme="light"] .mobile-action-button .count {
  color: rgba(255, 255, 255, 0.95);
}

@media (max-width: 480px) {
  /* QC组调整为水平滚动 */
  .qc-groups-grid {
    display: flex;
    grid-template-columns: unset;
    overflow-x: auto;
    overflow-y: hidden;
    scroll-snap-type: x mandatory;
    -webkit-overflow-scrolling: touch;
    padding: 10px 20px 20px;
    gap: 15px;
    max-width: calc(100vw - 40px);
    margin: 0 auto;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
    position: relative;
  }
  
  .qc-groups-grid::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }
  
  .qc-group {
    flex: 0 0 80%;
    min-width: 250px;
    max-width: 300px;
    scroll-snap-align: start;
    margin-right: 5px;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    position: relative;
  }
  
  /* 浅色模式下的QC组卡片 */
  [data-theme="light"] .qc-group {
    box-shadow: 0 5px 15px rgba(142, 45, 226, 0.1);
  }
  
  .qc-group-content {
    height: 280px;
    position: relative;
  }
  
  /* 修改QC轮播控制按钮样式和位置 */
  .qc-carousel-controls {
    display: none !important;
  }
}

.qc-item-controls {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 10;
}

.qc-item-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 36px;
  height: 36px;
  background: rgba(90, 51, 160, 0.8);
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
  pointer-events: auto;
  z-index: 10;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.qc-item-btn:active {
  transform: translateY(-50%) scale(0.95);
  background: rgba(90, 51, 160, 0.9);
}

/* 浅色模式下的按钮 */
[data-theme="light"] .qc-item-btn {
  background: rgba(193, 151, 209, 0.8);
  box-shadow: 0 2px 8px rgba(142, 45, 226, 0.2);
}

.qc-item-btn.prev {
  left: 5px;
}

.qc-item-btn.next {
  right: 5px;
}

@media (max-width: 480px) {
  /* 隐藏全局QC轮播控制按钮，只在PC端显示 */
  .qc-carousel-controls {
    display: none;
  }
}

/* 商家选择模态框样式 */
.sellers-modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease;
}

.sellers-modal {
  width: 90%;
  max-width: 800px;
  max-height: 80vh;
  background-color: #13101F;
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.4);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 浅色模式下的模态框背景 */
[data-theme="light"] .sellers-modal {
  background-color: #f9f5fc;
  box-shadow: 0 10px 30px rgba(142, 45, 226, 0.2);
}

.sellers-modal-header {
  padding: 16px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* 浅色模式下的模态框头部 */
[data-theme="light"] .sellers-modal-header {
  border-bottom: 1px solid rgba(142, 45, 226, 0.1);
}

.sellers-modal-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
  margin: 0;
}

/* 浅色模式下的模态框标题 */
[data-theme="light"] .sellers-modal-header h3 {
  color: #333;
}

.sellers-modal-content {
  padding: 20px;
  flex: 1;
  overflow-y: auto;
}

.sellers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(230px, 1fr));
  gap: 16px;
}

.seller-item {
  background-color: rgba(30, 20, 50, 0.6);
  border-radius: 12px;
  padding: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  border: 1px solid transparent;
}

/* 浅色模式下的商家项 */
[data-theme="light"] .seller-item {
  background-color: rgba(232, 208, 245, 0.3);
  border: 1px solid rgba(142, 45, 226, 0.1);
}

.seller-item:hover {
  background-color: rgba(90, 51, 160, 0.4);
  transform: translateY(-2px);
}

/* 浅色模式下的商家项悬停效果 */
[data-theme="light"] .seller-item:hover {
  background-color: rgba(232, 208, 245, 0.6);
  border-color: rgba(142, 45, 226, 0.3);
  transform: translateY(-2px);
}

.seller-item.selected {
  background-color: rgba(142, 45, 226, 0.4);
  border: 1px solid rgba(142, 45, 226, 0.8);
}

/* 浅色模式下的选中商家项 */
[data-theme="light"] .seller-item.selected {
  background-color: rgba(142, 45, 226, 0.2);
  border: 1px solid rgba(142, 45, 226, 0.6);
}

.seller-logo {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #2a1a4a;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  flex-shrink: 0;
  overflow: hidden;
}

/* 浅色模式下的商家logo */
[data-theme="light"] .seller-logo {
  background-color: rgba(142, 45, 226, 0.1);
}

.seller-logo.all-sellers {
  background-color: #5a33a0;
  color: white;
  font-size: 22px;
}

/* 浅色模式下的"所有商家"logo */
[data-theme="light"] .seller-logo.all-sellers {
  background-color: rgba(142, 45, 226, 0.3);
}

.seller-logo img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.seller-initials {
  color: #ffffff;
  font-size: 22px;
  font-weight: 600;
}

/* 浅色模式下的商家首字母 */
[data-theme="light"] .seller-initials {
  color: #8e2de2;
}

.seller-info {
  flex: 1;
}

.seller-info h4 {
  margin: 0 0 5px;
  color: #ffffff;
  font-size: 16px;
  font-weight: 500;
}

/* 浅色模式下的商家名称 */
[data-theme="light"] .seller-info h4 {
  color: #333;
}

.seller-info p {
  margin: 0;
  color: #b0b0b0;
  font-size: 13px;
}

/* 浅色模式下的商家描述 */
[data-theme="light"] .seller-info p {
  color: #666;
}

.seller-id {
  margin-top: 5px;
  font-size: 12px;
  color: #777777;
}

/* 浅色模式下的商家ID */
[data-theme="light"] .seller-id {
  color: #999;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
}

.loading-container .loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(142, 45, 226, 0.1);
  border-top: 3px solid #8e2de2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

.loading-container p {
  color: #b0b0b0;
}

/* 浅色模式下的加载文字 */
[data-theme="light"] .loading-container p {
  color: #666;
}

.no-sellers {
  text-align: center;
  padding: 40px 20px;
  color: #b0b0b0;
}

/* 浅色模式下的无商家文字 */
[data-theme="light"] .no-sellers {
  color: #666;
}

@media (max-width: 768px) {
  .sellers-grid {
    grid-template-columns: 1fr;
  }
  
  .sellers-modal {
    width: 95%;
    max-height: 70vh;
  }
}

.more-card {
  background-color: #13101F;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  cursor: pointer;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 1px solid rgba(90, 51, 160, 0.3);
}

/* 浅色模式下的更多卡片 */
[data-theme="light"] .more-card {
  background-color: #ffffff;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(142, 45, 226, 0.15);
}

.more-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(142, 45, 226, 0.3);
}

/* 浅色模式下的更多卡片悬停效果 */
[data-theme="light"] .more-card:hover {
  box-shadow: 0 8px 25px rgba(142, 45, 226, 0.2);
}

.more-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 15px;
  padding: 30px;
}

.more-icon {
  width: 60px;
  height: 60px;
  background-color: rgba(142, 45, 226, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

/* 浅色模式下的图标背景 */
[data-theme="light"] .more-icon {
  background-color: rgba(240, 213, 252, 0.5);
}

.more-card:hover .more-icon {
  background-color: rgba(142, 45, 226, 0.4);
  transform: scale(1.1);
}

/* 浅色模式下的图标悬停效果 */
[data-theme="light"] .more-card:hover .more-icon {
  background-color: rgba(240, 213, 252, 0.8);
}

.more-icon svg {
  color: #9c27b0;
  transition: all 0.3s ease;
}

.more-card:hover .more-icon svg {
  color: #ffffff;
}

/* 浅色模式下的图标颜色 */
[data-theme="light"] .more-icon svg {
  color: #8e2de2;
}

[data-theme="light"] .more-card:hover .more-icon svg {
  color: #5a33a0;
}

.more-text {
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
  transition: color 0.3s ease;
}

/* 浅色模式下的文字颜色 */
[data-theme="light"] .more-text {
  color: #333333;
}

/* 移动端样式调整 */
@media (max-width: 480px) {
  .more-card {
    width: 100%;
    max-width: 223px;
    height: 280px;
  }
  
  .more-content {
    padding: 20px;
  }
  
  .more-icon {
    width: 50px;
    height: 50px;
  }
  
  .more-icon svg {
    width: 30px;
    height: 30px;
  }
  
  .more-text {
    font-size: 16px;
  }
}

/* 添加全屏分类菜单 */
/* 全屏分类菜单 - 深色主题 */
[data-theme="dark"] .category-fullscreen {
  position: fixed;
  top: auto;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(77, 48, 105, 1);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  height: 50vh;
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
  box-shadow: 0 -5px 25px rgba(0, 0, 0, 0.3);
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

/* 全屏分类菜单 - 浅色主题 */
[data-theme="light"] .category-fullscreen {
  position: fixed;
  top: auto;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #f8fafc;
  border: 1px solid rgba(142, 45, 226, 0.2);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  height: 50vh;
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
  box-shadow: 0 -5px 25px rgba(142, 45, 226, 0.15);
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.category-fullscreen.show {
  transform: translateY(0);
}

@media (min-width: 481px) {
  .category-fullscreen {
    display: none;
  }
}

/* 分类菜单头部 - 深色主题 */
[data-theme="dark"] .category-fullscreen-header {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background-color: rgba(77, 48, 105, 1);
  position: relative;
}

/* 分类菜单头部 - 浅色主题 */
[data-theme="light"] .category-fullscreen-header {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid rgba(142, 45, 226, 0.2);
  background-color: rgba(232, 208, 245, 0.9);
  position: relative;
}

/* 分类菜单标题 - 深色主题 */
[data-theme="dark"] .category-fullscreen-header h2 {
  font-size: 20px;
  color: #ffffff;
  font-weight: 500;
  margin: 0;
  text-align: center;
}

/* 分类菜单标题 - 浅色主题 */
[data-theme="light"] .category-fullscreen-header h2 {
  font-size: 20px;
  color: #2d3748;
  font-weight: 500;
  margin: 0;
  text-align: center;
}

/* 关闭按钮样式 */
.close-btn {
  background: none;
  border: none;
  color: white;
  padding: 5px;
  cursor: pointer;
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
}

/* 浅色模式下的关闭按钮 */
[data-theme="light"] .close-btn {
  color: #2d3748;
}

.category-fullscreen-content {
  display: flex;
  height: calc(100% - 50px);
  padding: 0;
  max-width: 100%;
  overflow: hidden;
}

/* 分类侧边栏 - 深色主题 */
[data-theme="dark"] .category-sidebar {
  width: 35%;
  background-color: rgba(35, 15, 72, 0.8);
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 0;
  margin: 0;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

/* 分类侧边栏 - 浅色主题 */
[data-theme="light"] .category-sidebar {
  width: 35%;
  background-color: rgba(232, 208, 245, 0.6);
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 0;
  margin: 0;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

/* 分类内容区域 - 深色主题 */
[data-theme="dark"] .category-content {
  width: 65%;
  padding: 10px;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

/* 分类内容区域 - 浅色主题 */
[data-theme="light"] .category-content {
  width: 65%;
  padding: 10px;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  background-color: #f8fafc;
}

.category-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
  padding: 5px;
}

/* 分类侧边栏项目 - 深色主题 */
[data-theme="dark"] .category-sidebar-item {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 15px;
  transition: background-color 0.3s ease;
  cursor: pointer;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  position: relative;
}

/* 分类侧边栏项目 - 浅色主题 */
[data-theme="light"] .category-sidebar-item {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 15px;
  transition: background-color 0.3s ease;
  cursor: pointer;
  border-bottom: 1px solid rgba(142, 45, 226, 0.1);
  position: relative;
}

/* 激活的侧边栏项目 - 深色主题 */
[data-theme="dark"] .category-sidebar-item.active {
  background-color: rgba(77, 48, 105, 1);
}

/* 激活的侧边栏项目 - 浅色主题 */
[data-theme="light"] .category-sidebar-item.active {
  background-color: rgba(232, 208, 245, 0.9);
}

/* 分类侧边栏文本 - 深色主题 */
[data-theme="dark"] .category-sidebar-item span {
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
}

/* 分类侧边栏文本 - 浅色主题 */
[data-theme="light"] .category-sidebar-item span {
  color: #2d3748;
  font-size: 14px;
  font-weight: 500;
}

/* 选中图标 - 深色主题 */
[data-theme="dark"] .check-icon {
  position: absolute;
  left: 15px;
  color: #ffffff;
  font-size: 16px;
  font-weight: bold;
}

/* 选中图标 - 浅色主题 */
[data-theme="light"] .check-icon {
  position: absolute;
  left: 15px;
  color: #8e2de2;
  font-size: 16px;
  font-weight: bold;
}

.category-sidebar-item.active span,
.category-sidebar-item span.active-text {
  margin-left: 20px;
}

/* 分类卡片 - 深色主题 */
[data-theme="dark"] .category-card {
  width: 100%;
  aspect-ratio: 1;
  background-color: rgba(77, 48, 105, 0.8);
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: transform 0.2s ease;
}

/* 分类卡片 - 浅色主题 */
[data-theme="light"] .category-card {
  width: 100%;
  aspect-ratio: 1;
  background-color: #ffffff;
  border: 1px solid rgba(142, 45, 226, 0.2);
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: transform 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.category-card:hover {
  transform: scale(1.05);
}

.category-image {
  width: 60%;
  height: 60%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.category-image img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  opacity: 0.7;
}

/* 分类名称 - 深色主题 */
[data-theme="dark"] .category-name {
  padding: 10px 5px;
  color: #ffffff;
  font-size: 14px;
  text-align: center;
  font-weight: 500;
}

/* 分类名称 - 浅色主题 */
[data-theme="light"] .category-name {
  padding: 10px 5px;
  color: #2d3748;
  font-size: 14px;
  text-align: center;
  font-weight: 500;
}

.mobile-slider-content .banner-video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: 1;
}

/* 确保视频播放占位文字正确显示 */
.mobile-slider-content:has(.banner-video) {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAwIiBoZWlnaHQ9IjQwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iODAwIiBoZWlnaHQ9IjQwMCIgZmlsbD0iIzMzMyIvPjx0ZXh0IHg9IjQwMCIgeT0iMjAwIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMzAiIGZpbGw9IiNmZmYiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGFsaWdubWVudC1iYXNlbGluZT0ibWlkZGxlIj5WaWRlbyBMb2FkaW5nLi4uPC90ZXh0Pjwvc3ZnPg==');
  background-size: cover;
  background-position: center;
}

/* 删除以下样式 */
/* 
.virtual-views-badge {
  display: flex;
  align-items: center;
  gap: 5px;
  color: white;
  font-size: 12px;
  margin-top: 10px;
}

.virtual-views-badge svg {
  width: 16px;
  height: 16px;
}

.product-wrapper {
  position: relative;
  width: 100%;
}

.virtual-views-badge {
  position: absolute;
  bottom: 10px;
  right: 10px;
  display: flex;
  align-items: center;
  gap: 5px;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 12px;
  z-index: 10;
}

.virtual-views-badge svg {
  width: 16px;
  height: 16px;
  color: white;
}
*/
</style>
