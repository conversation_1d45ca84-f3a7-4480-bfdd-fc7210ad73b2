import api from '../services/api'

/**
 * 邀请码相关API接口
 */
export default {
  /**
   * 验证邀请码有效性
   * @param {string} inviteCode 邀请码
   * @returns {Promise} API响应
   */
  validateInviteCode(inviteCode) {
    return api.get(`/api/invite/validate/${inviteCode}`)
  },

  /**
   * 记录邀请码访问
   * @param {string} inviteCode 邀请码
   * @returns {Promise} API响应
   */
  recordVisit(inviteCode) {
    return api.post(`/api/invite/visit`, null, {
      params: { inviteCode }
    })
  },

  /**
   * 记录邀请码注册
   * @param {string} inviteCode 邀请码
   * @param {number} userId 用户ID
   * @returns {Promise} API响应
   */
  recordRegistration(inviteCode, userId) {
    return api.post('/api/invite/register', {
      inviteCode,
      userId
    })
  },

  /**
   * 获取邀请码统计信息（如果后端支持）
   * @param {string} inviteCode 邀请码
   * @returns {Promise} API响应
   */
  getInviteCodeStats(inviteCode) {
    return api.get(`/api/invite/stats/${inviteCode}`)
  }
}
