<template>
  <div class="products-two">
    <!-- 使用封装的星空背景组件 -->
    <StarryBackground />
    
    <!-- 世界地图组件 -->
    <div class="world-map-section">
      <WorldMap :statsData="statsData" />
    </div>
    
    <!-- 搜索框组件 -->
    <div class="search-container" ref="searchContainerRef">
      <div class="search-icon" @click="handleSearch">
        <i class="fas fa-search"></i>
      </div>
      <input 
        type="text" 
        class="search-input" 
        placeholder="Search" 
        v-model="searchQuery"
        @keyup.enter="handleSearch"
      />
      <div class="camera-icon">
        <i class="fas fa-camera"></i>
      </div>
    </div>
    
    <!-- 移动端排序筛选布局 -->
    <div v-if="isMobile">
      <!-- 移动端背景遮罩 -->
      <div v-if="showFilterDropdown && isMobile" class="mobile-filter-backdrop" @click="showFilterDropdown = false"></div>

      <!-- 第一行：Sort by、Category、筛选器图标 -->
      <div class="mobile-sorting-layout">
        <!-- Sort by 按钮（继承原筛选器功能） -->
        <div class="mobile-sort-by" @click="toggleFilterDropdown">
          <span class="sort-text">Sort by</span>
          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" :class="{ 'rotate': showFilterDropdown }">
            <polyline points="6 9 12 15 18 9"></polyline>
          </svg>

          <!-- 移动端弹出层已移到页面最外层 -->
        </div>

        <!-- Category 按钮 -->
        <button class="mobile-category-btn" @click="toggleCategoryMenu">
          CATEGORY
        </button>

        <!-- 筛选器图标（控制新按钮折叠） -->
        <div class="mobile-filter-toggle" @click="toggleMobileFilters">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <polygon points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"></polygon>
          </svg>
        </div>
      </div>

      <!-- 第二行：可折叠的新筛选按钮 -->
      <div v-if="isMobileFiltersOpen" class="mobile-new-filters">
        <div class="mobile-filter-btn" @click="handlePriceFilter">
          <span>Price</span>
          <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <polyline points="6 9 12 15 18 9"></polyline>
          </svg>
        </div>
        <div class="mobile-filter-btn" @click="handleSellerFilter">
          <span>Seller</span>
          <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <polyline points="6 9 12 15 18 9"></polyline>
          </svg>
        </div>
        <div class="mobile-filter-btn" @click="handlePlatformFilter">
          <span>Platform</span>
          <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <polyline points="6 9 12 15 18 9"></polyline>
          </svg>
        </div>
      </div>
    </div>

    <!-- PC端产品分类行 -->
    <div class="pc-categories-section" v-if="!isMobile">
      <div class="filter-chips-container">
        <div class="filter-chips">
          <div
            v-for="(category, index) in categories"
            :key="`cat-${index}`"
            class="category-dropdown-wrapper"
            @mouseleave="handleCategoryAreaLeave"
          >
            <div
              class="filter-chip"
              :class="{
                'active': selectedCategory === category.id,
                'hover': hoveredCategory === category.id && !selectedCategory
              }"
              @mouseenter="handleCategoryHover(category.id)"
              @click="selectCategory(category.id)"
            >
              <i :class="getCategoryIcon(category.name)" class="category-icon"></i>
              {{ category.name }}
              <span v-if="hoveredCategory === category.id || selectedCategory === category.id" class="dropdown-arrow">▼</span>
              <span v-else class="dropdown-arrow">▼</span>
            </div>

            <!-- 品牌下拉菜单 - 移到分类内部 -->
            <div
              v-if="showBrandsDropdown && (
                (!selectedCategory && hoveredCategory === category.id) ||
                (selectedCategory === category.id)
              )"
              class="brands-dropdown-container category-dropdown"
              :class="{ 'mobile-dropdown': isMobile }"
            >
              <div class="brands-dropdown">
                <div v-if="categoryBrands.length > 0">
                  <div
                    v-for="(brand, bIndex) in categoryBrands"
                    :key="`brand-${bIndex}`"
                    class="brand-dropdown-item"
                    :class="{ 'active': selectedBrand === brand.brandId }"
                    @click="selectBrand(brand.brandId)"
                  >
                    {{ brand.name }}
                  </div>
                </div>
                <div v-else class="empty-brands">
                  <p>No brands available</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- PC端排序筛选行 -->
    <div class="pc-sorting-section" v-if="!isMobile">
      <!-- 添加背景遮罩 -->
      <div v-if="showFilterDropdown" class="filter-dropdown-backdrop" @click="showFilterDropdown = false"></div>

      <div class="sorting-controls">
        <!-- Sort by 文字（替换原筛选器图标） -->
        <div class="sort-by-text" :class="{ 'active': activeFilter !== 'recommend' || showFilterDropdown }" @click="toggleFilterDropdown">
          <span>Sort by</span>
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" :class="{ 'rotate': showFilterDropdown }">
            <polyline points="6 9 12 15 18 9"></polyline>
          </svg>

          <!-- PC端过滤下拉框（保持原有功能） - 移到Sort by按钮内 -->
          <div v-if="showFilterDropdown" class="filter-dropdown">
            <div class="filter-options">
              <button class="filter-option" :class="{ 'active': activeFilter === 'recommend' }" @click="setActiveFilter('recommend')">
                <span>Recommend</span>
              </button>
              <button class="filter-option" :class="{ 'active': activeFilter === 'favorite' }" @click="setActiveFilter('favorite')">
                <span>Favorite</span>
              </button>
              <button class="filter-option" :class="{ 'active': activeFilter === 'amountSold' }" @click="setActiveFilter('amountSold')">
                <span>Amount Sold</span>
              </button>
              <button class="filter-option" :class="{ 'active': activeFilter === 'price' }" @click="setActiveFilter('price')">
                <span>Price</span>
              </button>
              <div class="filter-option seller-option" :class="{ 'active': activeFilter === 'seller' }" @click="setActiveFilter('seller')">
                <span>Seller</span>
                <div class="arrow-container">
                  <svg class="arrow-up" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" @click.stop="setSortDirection('asc')">
                    <polyline points="18 15 12 9 6 15"></polyline>
                  </svg>
                  <svg class="arrow-down" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" @click.stop="setSortDirection('desc')">
                    <polyline points="6 9 12 15 18 9"></polyline>
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 新增的筛选按钮 -->
        <div class="new-filter-controls">
          <div class="filter-text-btn" :class="{ 'active': selectedPriceRange || showPriceDropdown }" @click="handlePriceFilter">
            <span>Price</span>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" :class="{ 'rotate': showPriceDropdown }">
              <polyline points="6 9 12 15 18 9"></polyline>
            </svg>
          </div>
          <div class="filter-text-btn" :class="{ 'active': selectedSeller || showSellersModal }" @click="handleSellerFilter">
            <span>Seller</span>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <polyline points="6 9 12 15 18 9"></polyline>
            </svg>
          </div>
          <div class="filter-text-btn" :class="{ 'active': selectedPlatform || showPlatformDropdown }" @click="handlePlatformFilter">
            <span>Platform</span>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" :class="{ 'rotate': showPlatformDropdown }">
              <polyline points="6 9 12 15 18 9"></polyline>
            </svg>
          </div>
        </div>
      </div>
    </div>


    
    <!-- 产品卡片网格 -->
    <div class="products-grid-container">
      <!-- 加载中状态 -->
      <div v-if="isLoading" class="loading-spinner-container">
        <div class="loading-spinner"></div>
        <p>Loading products...</p>
      </div>
      
      <!-- 错误状态 -->
      <div v-else-if="error" class="error-message">
        {{ error }}
        <button @click="fetchProducts">Retry</button>
      </div>
      
      <!-- 产品网格 -->
      <div v-else class="products-grid">
        <ProductCard 
          v-for="product in products" 
          :key="product.id" 
          :product="product"
          @collect-updated="handleCollectUpdated"
          @like-updated="handleLikeUpdated"
        />
      </div>
      
      <!-- 加载更多时的宇宙风格加载器 -->
      <div v-if="isLoadingMore" class="load-more-container">
        <div class="cosmic-loader">
          <div class="cosmic-loader-ring"></div>
          <div class="cosmic-loader-stars">
            <div class="cosmic-star" v-for="n in 8" :key="n"></div>
          </div>
        </div>
      </div>
      
      <!-- 没有更多产品提示 -->
      <div v-if="!isLoading && noMoreProducts && products.length > 0" class="no-more-products">
        <div class="no-more-icon">
          <i class="fas fa-satellite"></i>
        </div>
        <p>No more products available</p>
        <div class="cosmic-line"></div>
      </div>
    </div>

    <!-- 添加全屏分类菜单 -->
    <div class="category-fullscreen" v-if="showCategoryMenu">
      <div class="category-fullscreen-header">
        <h2>CATEGORY</h2>
        <button class="close-btn" @click="toggleCategoryMenu">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>

      <div class="category-fullscreen-content">
        <div class="category-sidebar">
          <!-- 动态分类列表 -->
          <div
            v-for="category in categories"
            :key="category.id"
            class="category-sidebar-item"
            :class="{ 'active': activeSidebarItem === category.id }"
            @click="setActiveSidebarItem(category.id)"
          >
            <div class="category-item-content">
              <i :class="getCategoryIcon(category.name)" class="category-sidebar-icon"></i>
              <span :class="{ 'active-text': activeSidebarItem === category.id }">{{ category.name }}</span>
            </div>
          </div>
        </div>

        <div class="category-content">
          <div class="category-grid">
            <div class="category-card" v-for="(item, index) in currentCategoryItems" :key="item.id || index" @click="selectCategoryItem(item)">
              <div class="category-image">
                <img :src="item.logoUrl || item.image || 'https://cdn.findqc.com/bt/20250607/mbme4p4h_lete.jpg'" :alt="item.name">
              </div>
              <div class="category-name">{{ item.name }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 商家选择弹窗 -->
    <div v-if="showSellersModal" class="sellers-modal-backdrop" @click="closeSellersModal">
      <div class="sellers-modal" @click.stop>
        <div class="sellers-modal-header">
          <button class="close-btn close-btn-left" @click="closeSellersModal">×</button>
          <h3>Select Merchant</h3>
        </div>

        <div class="sellers-modal-content">
          <div v-if="isLoadingSellers" class="loading-container">
            <div class="loading-spinner"></div>
            <p>Loading merchant data...</p>
          </div>

          <div v-else-if="sellers.length === 0" class="no-sellers">
            <p>No merchant data available</p>
          </div>

          <div v-else class="sellers-grid">
            <!-- All merchants option -->
            <div class="seller-item"
                 :class="{ 'selected': !selectedSeller }"
                 @click="selectSeller(null)">
              <div class="seller-logo all-sellers">
                <i class="fas fa-store"></i>
              </div>
              <div class="seller-info">
                <h4>All Merchants</h4>
                <p>Show all products</p>
              </div>
            </div>

            <!-- Merchant list -->
            <div v-for="seller in sellers"
                 :key="seller.id"
                 class="seller-item"
                 :class="{ 'selected': selectedSeller && selectedSeller.id === seller.id }"
                 @click="selectSeller(seller)">
              <div class="seller-logo">
                <img v-if="seller.logoUrl"
                     :src="seller.logoUrl"
                     :alt="seller.name"
                     @error="$event.target.style.display='none'">
                <div v-else class="seller-initials">
                  {{ (seller.name || 'N').charAt(0).toUpperCase() }}
                </div>
              </div>
              <div class="seller-info">
                <h4>{{ seller.name || 'Unknown Merchant' }}</h4>
                <p v-if="seller.description">{{ seller.description }}</p>
                <p class="seller-id">Merchant ID: {{ seller.originalIndex }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 移动端筛选弹出层 - 放在最外层确保不被遮挡 -->
  <div v-if="showFilterDropdown && isMobile" class="mobile-filter-dropdown">
    <div class="filter-options">
      <button class="filter-option" :class="{ 'active': activeFilter === 'recommend' }" @click="setActiveFilter('recommend')">
        <span>Recommend</span>
      </button>
      <button class="filter-option" :class="{ 'active': activeFilter === 'favorite' }" @click="setActiveFilter('favorite')">
        <span>Favorite</span>
      </button>
      <button class="filter-option" :class="{ 'active': activeFilter === 'amountSold' }" @click="setActiveFilter('amountSold')">
        <span>Amount Sold</span>
      </button>
      <button class="filter-option" :class="{ 'active': activeFilter === 'price' }" @click="setActiveFilter('price')">
        <span>Price</span>
      </button>
      <div class="filter-option seller-option" :class="{ 'active': activeFilter === 'seller' }" @click="setActiveFilter('seller')">
        <span>Seller</span>
        <div class="arrow-container">
          <svg class="arrow-up" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" @click.stop="setSortDirection('asc')">
            <polyline points="18 15 12 9 6 15"></polyline>
          </svg>
          <svg class="arrow-down" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" @click.stop="setSortDirection('desc')">
            <polyline points="6 9 12 15 18 9"></polyline>
          </svg>
        </div>
      </div>
    </div>
  </div>

  <!-- 价格筛选弹出层 - 放在最外层确保不被遮挡 -->
  <div v-if="showPriceDropdown" class="price-filter-dropdown">
    <div class="price-filter-options">
      <!-- 价格区间选项 -->
      <div class="price-ranges">
        <button
          v-for="range in priceRanges"
          :key="range.id"
          class="price-range-btn"
          :class="{ 'active': selectedPriceRange?.id === range.id }"
          @click="selectPriceRange(range)"
        >
          {{ range.label }}
        </button>
      </div>

      <!-- 自定义价格输入 -->
      <div class="custom-price-inputs">
        <div class="price-input-group">
          <input
            v-model="customMinPrice"
            type="number"
            placeholder="$Min."
            class="price-input"
            @keyup.enter="applyPriceFilter"
            @input="clearSelectedRange"
          />
          <input
            v-model="customMaxPrice"
            type="number"
            placeholder="$Max."
            class="price-input"
            @keyup.enter="applyPriceFilter"
            @input="clearSelectedRange"
          />
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="price-actions">
        <button class="apply-price-btn" @click="applyPriceFilter">
          Apply Filter
        </button>
        <button class="reset-price-btn" @click="resetPriceRange">
          Reset
        </button>
      </div>
    </div>
  </div>

  <!-- 平台筛选弹出层 - 放在最外层确保不被遮挡 -->
  <div v-if="showPlatformDropdown" class="platform-filter-dropdown">
    <div class="platform-filter-options">
      <!-- 平台选项 -->
      <div class="platform-options">
        <button
          v-for="platform in platformOptions"
          :key="platform.id"
          class="platform-option-btn"
          :class="{ 'active': selectedPlatform?.id === platform.id }"
          @click="selectPlatform(platform)"
        >
          <div class="platform-icon">
            <i v-if="platform.id === 'all'" class="fas fa-globe"></i>
            <i v-else-if="platform.id === 'taobao'" class="fab fa-shopify"></i>
            <i v-else-if="platform.id === 'weidian'" class="fas fa-store"></i>
            <i v-else-if="platform.id === '1688'" class="fas fa-industry"></i>
          </div>
          <span class="platform-name">{{ platform.name }}</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import StarryBackground from '@/components/StarryBackground.vue'
import WorldMap from '@/components/WorldMap.vue'
import ProductCard from '@/components/ProductCard.vue'
import productsApi from '@/api/products'
import apiClient from '@/services/api'
import { ref, onMounted, onUnmounted, computed, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'

export default {
  name: 'ProductsTwo',
  components: {
    StarryBackground,
    WorldMap,
    ProductCard
  },
  setup() {
    const router = useRouter()
    const route = useRoute()
    const searchQuery = ref('')
    const isDarkMode = ref(false)
    const categories = ref([])
    const selectedCategory = ref(null)
    const hoveredCategory = ref(null) // 新增：当前悬停的分类
    const isHoveringDropdown = ref(false) // 新增：是否正在悬停下拉菜单
    const categoryBrands = ref([])
    const selectedBrand = ref(null)
    const brandsByCategory = ref({}) // 存储每个分类对应的品牌列表
    const isMobile = ref(window.innerWidth <= 480)
    const showFilterDropdown = ref(false)
    const showCategoryMenu = ref(false)
    const activeSidebarItem = ref(null)
    const activeFilter = ref('recommend')
    const sortDirection = ref('asc')
    const searchContainerRef = ref(null)
    const themeObserver = ref(null)
    // 添加一个新的状态，控制是否显示品牌下拉框
    const showBrandsDropdown = ref(false)
    // 新增移动端筛选器折叠状态
    const isMobileFiltersOpen = ref(false)

    // 价格筛选相关状态
    const showPriceDropdown = ref(false)
    const selectedPriceRange = ref(null)
    const customMinPrice = ref('')
    const customMaxPrice = ref('')

    // 汇率相关（与ProductCard组件保持一致）
    const exchangeRate = ref(6.5); // 人民币到美元的汇率，默认6.5（与ProductCard组件一致）

    // 价格区间选项（美元显示）
    const priceRanges = ref([
      { id: 'under10', label: '<10$', min: 0, max: 10 },
      { id: '10to100', label: '10$-100$', min: 10, max: 100 },
      { id: '100to1000', label: '100$-1000$', min: 100, max: 1000 },
      { id: 'over1000', label: '>1000$', min: 1000, max: null }
    ])

    // 货币转换函数
    const convertUSDToRMB = (usdPrice) => {
      // 用户输入的美元价格 × 汇率 = 人民币价格（用于与后端数据比较）
      return usdPrice * exchangeRate.value;
    };

    const convertRMBToUSD = (rmbPrice) => {
      // 后端返回的人民币价格 ÷ 汇率 = 美元价格（用于显示）
      return rmbPrice / exchangeRate.value;
    };
    
    // 从路由参数中获取分类ID和品牌ID
    const routeCategoryId = route.query.categoryId || null
    const routeBrandId = route.query.brandId || null
    const routeSortType = route.query.sort || 'recommend'
    const routeSortDirection = route.query.direction || 'asc'
    const routeMerchant = route.query.merchant || null // 获取merchant参数
    const routeKeyword = route.query.keyword || null // 获取keyword参数
    const routeRecommended = route.query.recommended === 'true' // 获取recommended参数
    
    // 如果路由中有关键词，设置到搜索框
    if (routeKeyword) {
      searchQuery.value = routeKeyword
    }
    
    // 如果路由中有分类ID，设置为当前选中的分类
    if (routeCategoryId) {
      selectedCategory.value = routeCategoryId
      activeSidebarItem.value = routeCategoryId
      
      // 如果路由中有品牌ID，设置为当前选中的品牌
      if (routeBrandId) {
        selectedBrand.value = routeBrandId
      }
      
      // 注意：我们不设置showBrandsDropdown为true，这样通过路由跳转不会自动显示下拉框
    }
    
    // 如果路由中有排序类型和方向，设置为当前排序状态
    if (routeSortType) {
      activeFilter.value = routeSortType
    }
    
    if (routeSortDirection) {
      sortDirection.value = routeSortDirection
    }
    
    // 下拉菜单位置
    const dropdownPosition = ref({
      position: 'absolute',
      top: '0px',
      left: '0px',
      bottom: 'auto',
      width: 'auto'
    });
    
    // 添加API相关数据
    const products = ref([]);
    const filteredProducts = ref([]);
    const displayedProducts = ref([]);
    const isLoading = ref(false);
    const isLoadingMore = ref(false);
    const error = ref(null);
    const noMoreProducts = ref(false);
    const totalProducts = ref(0); // 服务器返回的总条数

    // 添加统计数据
    const statsData = ref({
      spreadsheets: 264,
      rows: 21367,
      products: 1254 // 默认值，将从API获取真实数据
    });
    
    // 添加分页相关数据
    const currentPage = ref(1);
    const itemsPerPage = ref(16);
    const apiCurrentPage = ref(1);
    const apiPageSize = ref(20);
    const hasMorePages = ref(true);
    
    // 添加过滤相关数据
    const priceRange = ref({
      min: null,
      max: null
    });
    const isRequestPending = ref(false);

    // 商家相关数据
    const sellers = ref([]);
    const isLoadingSellers = ref(false);
    const showSellersModal = ref(false);
    const selectedSeller = ref(null);

    // 添加滚动监听相关数据
    const scrollThreshold = ref(window.innerWidth <= 768 ? 300 : 500); // 根据设备类型设置不同的阈值
    const isScrollListenerActive = ref(false); // 是否已添加滚动监听
    const lastScrollTop = ref(0); // 记录上次滚动位置

    // 分类菜单数据 - 使用API数据
    const categoryItems = ref({});

    // 计算当前显示的分类项目
    const currentCategoryItems = computed(() => {
      // 显示选中分类对应的品牌数据
      const categoryId = activeSidebarItem.value;
      return brandsByCategory.value[categoryId] || [];
    });
    
    /**
     * 获取分类列表
     */
    async function fetchCategories() {
      try {
        const response = await productsApi.getAllCategories();
        if (response.code === 200 && response.data) {
          categories.value = response.data.map(category => ({
            id: category.categoryId || category.id,
            name: category.name
          }));

          // 如果路由中有分类ID，确保它在分类列表中
          if (routeCategoryId && !activeSidebarItem.value) {
            activeSidebarItem.value = routeCategoryId;
          }
          // 如果没有从路由中获取分类ID，设置第一个分类为默认选中
          else if (categories.value.length > 0 && !activeSidebarItem.value) {
            activeSidebarItem.value = categories.value[0].id;
          }

          // 如果有选中的分类，获取该分类的品牌
          if (selectedCategory.value) {
            await fetchBrandsByCategory(selectedCategory.value);
          }

          // 预加载前几个分类的品牌数据
          const preloadCategories = categories.value.slice(0, 3);
          for (const category of preloadCategories) {
            try {
              await fetchBrandsByCategory(category.id);
            } catch (error) {
              console.warn(`预加载分类 ${category.name} 的品牌数据失败:`, error);
            }
          }
        } else {
          console.error('Error getting categories:', response);
          categories.value = [];
        }
      } catch (error) {
        console.error('Error fetching categories:', error);
        categories.value = [];
      }
    }

    /**
     * 获取产品总数
     */
    async function fetchProductsCount() {
      try {
        console.log('开始获取产品总数...');
        const response = await apiClient.get('/omg/products/allCount');
        console.log('API响应:', response);
        if (response.code === 200 && response.data !== undefined) {
          console.log('更新前的产品数量:', statsData.value.products);
          statsData.value.products = response.data;
          console.log('更新后的产品数量:', statsData.value.products);
          console.log('获取产品总数成功:', response.data);
        } else {
          console.warn('API响应格式不正确:', response);
        }
      } catch (error) {
        console.error('获取产品总数失败:', error);
      }
    }

    // 添加视频适配模式选项
    const videoFitModes = {
      COVER: 'cover',    // 填充整个容器，可能裁剪视频
      CONTAIN: 'contain', // 确保整个视频可见，可能有黑边
      FILL: 'fill'       // 拉伸视频填充容器
    };
    
    // 默认视频适配模式
    const defaultVideoFitMode = ref(videoFitModes.CONTAIN);
    
    /**
     * 获取商品列表
     */
    async function fetchProducts(isLoadMore = false) {
      let newProducts = []; // 将变量声明移到方法开头，确保整个方法都能访问
      
      try {
        if (!isLoadMore) {
          isLoading.value = true;
          apiCurrentPage.value = 1; // 重置API页码
          hasMorePages.value = true; // 重置分页状态
          // 重置错误状态
          error.value = null;
        } else {
          isLoadingMore.value = true;
        }
        
        noMoreProducts.value = false; // 重置状态
        let response;
        
        // 使用本地变量存储当前选中分类，避免请求过程中被修改
        const selectedCat = selectedCategory.value;
        const selectedBrd = selectedBrand.value;
        
        // 构建分页参数
        const paginationParams = {
          page: apiCurrentPage.value,
          pageSize: apiPageSize.value
        };
        
        // 添加商家参数（如果有）
        if (selectedSeller.value && selectedSeller.value.name) {
          paginationParams.merchant = selectedSeller.value.name;
        }
        
        // 如果是推荐商品请求，使用推荐API
        if (routeRecommended) {
          
          response = await productsApi.getRecommendedProducts(paginationParams);
        }
        // 判断是否有选中的分类
        else if (selectedCat || selectedBrd) {
          
          const categoryParams = {
            minPrice: priceRange.value.min,
            maxPrice: priceRange.value.max,
            ...paginationParams
          };
          
          // 如果有商家筛选，添加商家参数
          if (selectedSeller.value && selectedSeller.value.name) {
            categoryParams.merchant = selectedSeller.value.name;
          }
          
          response = await productsApi.getProductsByCategory(selectedCat, selectedBrd, categoryParams);
        } else {
          // 搜索商品或获取所有商品，使用相同的API
          const params = {
            ...paginationParams
          };
          
          // 只在有搜索关键词时添加keyword参数
          if (searchQuery.value && searchQuery.value.trim()) {
            params.keyword = searchQuery.value.trim();
          }
          
          // 添加价格范围参数(如果有)
          if (priceRange.value.min !== null) {
            params.minPrice = priceRange.value.min;
          }
          if (priceRange.value.max !== null) {
            params.maxPrice = priceRange.value.max;
          }
          
          // 如果有商家筛选，添加商家参数
          if (selectedSeller.value && selectedSeller.value.name) {
            params.merchant = selectedSeller.value.name;
          }
          
          response = await productsApi.searchProducts(params);
        }
        
        if (response.code === 200 && response.data) {
          // 处理新的数据格式，数据在 data.list 中
          const dataList = response.data.list || response.data; // 兼容两种数据格式
          const totalCount = response.data.total || 0; // 获取总条数
          
          newProducts = dataList.map((item) => {
            // 获取视频URL，优先使用video字段，然后是videoUrl字段
            const videoUrl = item.video || item.videoUrl || null;

            // 调试日志：显示每个商品的视频信息
            console.log(`商品 ${item.name} (ID: ${item.productId || item.id}) 的视频URL:`, videoUrl);

            return {
              ...item,
              id: item.productId || item.id,
              name: item.name,
              price: item.price,
              imageUrl: item.mainImage || item.image,
              platform: item.platform || 'Unknown',
              likes: item.likes || 0,
              views: item.views || 0,
              comments: Array.isArray(item.comments) ? item.comments : [],
              productStatus: item.productStatus || 0,
              isCollected: false,
              // 使用接口返回的video字段，如果没有则使用null
              videoUrl: videoUrl,
              videoFitMode: item.videoFitMode || defaultVideoFitMode.value // 添加视频适配模式
            };
          });

          // 调试日志：显示处理后的商品数据
          console.log('ProductsTwo - 处理后的商品数据:', newProducts);

          if (!isLoadMore) {
            // 首次加载，替换产品列表
            products.value = newProducts;
            totalProducts.value = totalCount; // 使用服务器返回的总条数

            // 🔧 修复：应用当前的商家筛选条件
            applyMerchantFilter();

            // 应用当前的排序状态（如果有）
            applySortingIfNeeded();

            // 重置分页并加载第一页
            currentPage.value = 1;
            displayedProducts.value = filteredProducts.value.slice(0, itemsPerPage.value);
          } else {
            // 加载更多，追加到现有产品列表
            products.value = [...products.value, ...newProducts];

            // 🔧 修复：应用当前的商家筛选条件，而不是直接复制所有产品
            applyMerchantFilter();

            // 应用当前的排序状态（如果有）
            applySortingIfNeeded();

            // 重新计算需要显示的产品数量（保持之前显示的数量 + 新的一页）
            const newDisplayCount = displayedProducts.value.length + itemsPerPage.value;
            const actualDisplayCount = Math.min(newDisplayCount, filteredProducts.value.length);
            displayedProducts.value = filteredProducts.value.slice(0, actualDisplayCount);
          }
          
          // 根据总条数和当前已加载的数据量判断是否还有更多页
          const currentLoadedCount = products.value.length;
          const hasMoreData = currentLoadedCount < totalCount;
          
          if (!hasMoreData || newProducts.length < apiPageSize.value) {
            hasMorePages.value = false;
            noMoreProducts.value = true;
          } else {
            hasMorePages.value = true;
            apiCurrentPage.value++; // 为下次加载更多准备
          }
        } else {
          console.error('获取产品错误:', response.msg);
          if (!isLoadMore) {
            products.value = [];
            filteredProducts.value = [];
            displayedProducts.value = [];
            totalProducts.value = 0; // 重置总条数
            // 设置错误消息
            error.value = `Failed to load products: ${response.msg || 'Unknown error'}`;
          }
          hasMorePages.value = false;
          noMoreProducts.value = true;
        }

        // 检查每个商品的收藏状态
        if (localStorage.getItem('isLoggedIn')) {
          // 只检查新加载的商品
          const productsToCheck = isLoadMore ? newProducts : products.value;
          await checkCollectionStatus(productsToCheck);
        }

        // 检查每个商品的点赞状态
        if (localStorage.getItem('isLoggedIn')) {
          // 只检查新加载的商品
          const productsToCheck = isLoadMore ? newProducts : products.value;
          await checkLikeStatus(productsToCheck);
        }

      } catch (err) {
        console.error('获取产品错误:', err);
        if (!isLoadMore) {
          products.value = [];
          filteredProducts.value = [];
          displayedProducts.value = [];
          totalProducts.value = 0; // 重置总条数
          // 设置错误消息
          error.value = `Failed to load products: ${err.message || 'Unknown error'}`;
        }
        hasMorePages.value = false;
        noMoreProducts.value = true;
      } finally {
        if (!isLoadMore) {
          isLoading.value = false;
        } else {
          isLoadingMore.value = false;
        }
        // 如果是通过按钮触发的请求，重置请求标志
        if (!isLoadMore) {
          isRequestPending.value = false;
        }
      }
    }

    /**
     * 获取所有商家数据
     */
    async function fetchAllSellers() {
      isLoadingSellers.value = true;

      try {
        const response = await apiClient.get('/omg/products/getAllMerchant');

        if (response.code === 200 && response.data) {
          // Process merchant data - adapt object format {0: "OMG", 1: "Crazy Koala", ...}
          const sellersData = response.data;
          sellers.value = Object.entries(sellersData).map(([index, sellerName]) => ({
            id: `seller_${index}`,
            name: sellerName || `Merchant ${parseInt(index) + 1}`,
            platform: sellerName, // Use merchant name as platform identifier
            logoUrl: null, // API doesn't provide logo information
            description: `Products from ${sellerName}`,
            productCount: 0, // API doesn't provide product count
            rating: 0, // API doesn't provide rating
            isVerified: false, // API doesn't provide verification status
            originalIndex: index // Save original index, might be useful for filtering
          }));

          showSellersModal.value = true; // Show merchant selection modal
        } else {
          console.error('Failed to fetch merchant data:', response.msg);
          ElMessage.error(`Failed to fetch merchant data: ${response.msg || 'Unknown error'}`);
        }
      } catch (err) {
        console.error('Error fetching merchant data:', err);
        ElMessage.error(`Failed to fetch merchant data: ${err.message || 'Network error'}`);
      } finally {
        isLoadingSellers.value = false;
      }
    }

    /**
     * Select merchant for filtering
     */
    function selectSeller(seller) {
      selectedSeller.value = seller;

      // Apply merchant filter using the new function
      applyMerchantFilter();

      // Apply current sorting state
      applySortingIfNeeded();

      // Reload displayed products
      displayedProducts.value = filteredProducts.value.slice(0, itemsPerPage.value);

      // Close merchant selection modal
      showSellersModal.value = false;

      ElMessage.success(`Filtered products from ${seller ? seller.name : 'All merchants'}`);
    }

    /**
     * Close merchant selection modal
     */
    function closeSellersModal() {
      showSellersModal.value = false;
    }

    /**
     * Apply current merchant filter to products
     */
    function applyMerchantFilter() {
      if (selectedSeller.value && selectedSeller.value.name) {
        // Apply merchant filter
        filteredProducts.value = products.value.filter(product => {
          // Match multiple possible merchant fields (merchant is the primary field)
          const productSeller = product.merchant ||      // 主要字段：商家名称
                               product.platform ||
                               product.seller ||
                               product.merchantName ||
                               product.sellerName ||
                               product.source ||
                               '';

          // Enhanced matching logic
          const sellerName = selectedSeller.value.name.toLowerCase();
          const productSellerLower = productSeller.toLowerCase();

          // 1. Direct match
          if (productSellerLower === sellerName) return true;

          // 2. Contains match (both directions)
          if (productSellerLower.includes(sellerName) || sellerName.includes(productSellerLower)) return true;

          // 3. Special mapping for known platforms
          const platformMapping = {
            'omg': ['omg', '微店', 'weidian'],
            'crazy koala': ['crazy koala', 'crazykoala'],
            'flying pig fashion': ['flying pig', 'flyingpig', 'flying pig fashion'],
            'slpbuy': ['slpbuy', 'slp'],
            'polarbearbuy': ['polarbearbuy', 'polarbear', 'polar bear']
          };

          // Check if current seller has mapping
          const mappedPlatforms = platformMapping[sellerName];
          if (mappedPlatforms) {
            return mappedPlatforms.some(mapped =>
              productSellerLower.includes(mapped) || mapped.includes(productSellerLower)
            );
          }

          return false;
        });
      } else {
        // No merchant filter, show all products
        filteredProducts.value = [...products.value];
      }
    }

    // 检查商品是否被收藏
    async function checkCollectionStatus(products) {
      if (!localStorage.getItem('isLoggedIn')) return;
      
      try {
        // 获取所有商品ID
        const productIds = products.map(p => p.id);
        
        // 批量检查收藏状态
        const response = await apiClient.post(`/omg/collect/isCollected`, {
          userId: JSON.parse(localStorage.getItem('userInfo')).userId,
          productIds: productIds
        });
        
        if (response.code === 200 && response.data) {
          // 更新每个商品的收藏状态
          products.forEach(product => {
            product.isCollected = response.data[product.id] || false;
          });
        }
      } catch (error) {
        console.error('批量检查收藏状态失败:', error);
      }
    }
    
    // 检查商品是否被点赞
    async function checkLikeStatus(products) {
      if (!localStorage.getItem('isLoggedIn')) return;
      
      try {
        // 获取所有产品ID
        const productIds = products.map(p => p.id);
        
        // 使用修改后的API批量检查
        const response = await productsApi.checkLikeToday(productIds);
        
        if (response && response.code === 200 && response.data) {
          // 更新每个产品的点赞状态
          products.forEach(product => {
            product.isLiked = response.data[product.id] || false;
          });
        } else {
          console.warn('批量检查未返回有效数据，回退到单个检查');
        }
      } catch (error) {
        console.error('批量检查点赞状态失败:', error);
      }
    }
    
    // 根据当前排序状态应用排序
    function applySortingIfNeeded() {
      if (activeFilter.value === 'amountSold') {
        // 按销量排序
        filteredProducts.value.sort((a, b) => b.amountSold - a.amountSold);
      } else if (activeFilter.value === 'price') {
        // 按价格排序
        if (sortDirection.value === 'asc') {
          filteredProducts.value.sort((a, b) => a.price - b.price);
        } else {
          filteredProducts.value.sort((a, b) => b.price - a.price);
        }
      } else if (activeFilter.value === 'seller') {
        // 按卖家排序
        filteredProducts.value.sort((a, b) => {
          if (sortDirection.value === 'asc') {
            return a.platform.localeCompare(b.platform);
          } else {
            return b.platform.localeCompare(a.platform);
          }
        });
      } else if (activeFilter.value === 'favorite') {
        // 按收藏量排序（假设有likes或类似字段）
        filteredProducts.value.sort((a, b) => b.likes - a.likes);
      }
    }

    // 检测当前主题
    const checkCurrentTheme = () => {
      // 检查HTML元素的data-theme属性
      const htmlElement = document.documentElement;
      const theme = htmlElement.getAttribute('data-theme');
      isDarkMode.value = theme === 'dark';
      
      // 应用主题样式到搜索框
      applySearchTheme();
    };
    
    // 应用搜索框主题样式
    const applySearchTheme = () => {
      if (searchContainerRef.value) {
        if (isDarkMode.value) {
          searchContainerRef.value.classList.add('dark-theme');
          searchContainerRef.value.classList.remove('light-theme');
        } else {
          searchContainerRef.value.classList.add('light-theme');
          searchContainerRef.value.classList.remove('dark-theme');
        }
      }
    };
    
    // 处理主题变化
    const handleThemeChange = (data) => {
      if (data && typeof data.isDarkMode !== 'undefined') {
        // 获取当前主题
        isDarkMode.value = data.isDarkMode;
        
        // 手动应用主题到关键元素
        const productsElement = document.querySelector('.products-two');
        if (productsElement) {
          productsElement.style.background = isDarkMode.value ? 
            'linear-gradient(126.54deg, rgba(35, 15, 72, 1) 0%, rgba(23, 10, 48, 1) 31.26%, rgba(7, 12, 31, 1) 70.66%, rgba(37, 15, 74, 1) 100%)' : 
            'linear-gradient(131.67deg, rgba(240, 213, 252, 1) 0%, rgba(252, 247, 255, 1) 31.26%, rgba(249, 245, 252, 1) 70.66%, rgba(240, 212, 252, 1) 100%)';
        }
        
        // 应用主题到搜索框
        applySearchTheme();
      }
    };
    
    // 处理搜索
    const handleSearch = () => {
      if (searchQuery.value.trim()) {
        // 如果已有请求在进行中，忽略
        if (isRequestPending.value) {
          return;
        }
        
        // 设置请求标志
        isRequestPending.value = true;
        
        // 重置分类和品牌选择
        selectedCategory.value = null;
        selectedBrand.value = null;
        
        // 重置API分页状态
        apiCurrentPage.value = 1;
        hasMorePages.value = true;
        
        // 获取产品
        fetchProducts().finally(() => {
          isRequestPending.value = false;
        });
      }
    };

    // 处理分类悬停 - 展开下拉菜单
    const handleCategoryHover = async (categoryId) => {
      // 如果已有请求在进行中，忽略
      if (isRequestPending.value) {
        return;
      }

      // 检查categoryId是否有效
      if (categoryId === undefined || categoryId === null) {
        console.error('无效的分类ID:', categoryId);
        return;
      }

      // 如果有分类被选中，只有悬停在选中的分类上才显示下拉菜单
      if (selectedCategory.value) {
        if (selectedCategory.value === categoryId) {
          // 如果已经缓存了该分类的品牌数据，直接使用缓存
          if (brandsByCategory.value[categoryId]) {
            categoryBrands.value = brandsByCategory.value[categoryId];
          } else {
            // 否则，获取该分类下的品牌
            await fetchBrandsByCategory(categoryId);
          }
          // 显示品牌下拉框
          showBrandsDropdown.value = true;
        }
        return; // 如果有选中分类但悬停的不是选中分类，直接返回
      }

      // 没有分类被选中时，设置悬停状态
      hoveredCategory.value = categoryId;

      // 如果已经缓存了该分类的品牌数据，直接使用缓存
      if (brandsByCategory.value[categoryId]) {
        categoryBrands.value = brandsByCategory.value[categoryId];
      } else {
        // 否则，获取该分类下的品牌
        await fetchBrandsByCategory(categoryId);
      }

      // 显示品牌下拉框
      showBrandsDropdown.value = true;
    };

    // 处理分类区域离开 - 立即关闭下拉菜单
    const handleCategoryAreaLeave = () => {
      hoveredCategory.value = null;
      showBrandsDropdown.value = false;
      isHoveringDropdown.value = false;
    };

    // 选择分类 - 点击时请求商品
    const selectCategory = async (categoryId) => {
      // 如果已有请求在进行中，忽略
      if (isRequestPending.value) {
        return;
      }

      // 检查categoryId是否有效
      if (categoryId === undefined || categoryId === null) {
        console.error('无效的分类ID:', categoryId);
        return;
      }

      if (selectedCategory.value === categoryId) {
        // 如果点击的是当前选中的分类，则取消选择
        selectedCategory.value = null;
        selectedBrand.value = null;

        // 更新URL，移除分类和品牌参数
        router.replace({
          query: { ...route.query, categoryId: undefined, brandId: undefined },
          scrollBehavior: () => false
        });
      } else {
        // 设置请求标志
        isRequestPending.value = true;

        try {
          // 设置选中的分类ID
          selectedCategory.value = categoryId;
          selectedBrand.value = null;

          // 更新URL，添加分类参数，移除品牌参数
          router.replace({
            query: { ...route.query, categoryId: categoryId, brandId: undefined },
            scrollBehavior: () => false
          });

          // 使用选中的分类获取产品
          await fetchProducts();
        } finally {
          // 完成请求，重置标志
          isRequestPending.value = false;
        }
      }
    };

    // 选择分类（保留原有方法名以兼容其他地方的调用）
    const toggleCategoryDropdown = async (categoryId) => {
      // 如果已有请求在进行中，忽略
      if (isRequestPending.value) {
        return;
      }

      // 设置请求标志
      isRequestPending.value = true;

      // 重置分类和品牌选择
      selectedCategory.value = null;
      selectedBrand.value = null;

      // 重置API分页状态
      apiCurrentPage.value = 1;
      hasMorePages.value = true;

      // 更新URL参数
      const newQuery = { ...route.query };

      // 如果有搜索关键词，添加到URL；如果没有，移除keyword参数
      if (searchQuery.value && searchQuery.value.trim()) {
        newQuery.keyword = searchQuery.value.trim();
      } else {
        delete newQuery.keyword;
      }

      // 移除分类和品牌参数，因为搜索时会重置这些选择
      delete newQuery.categoryId;
      delete newQuery.brandId;

      // 更新URL
      router.replace({
        query: newQuery,
        scrollBehavior: () => false
      });

      // 获取产品（无论搜索框是否为空都执行搜索）
      fetchProducts().finally(() => {
        isRequestPending.value = false;
      });
    };

    // 直接导航到分类页面（点击分类名称）
    const navigateToCategory = (categoryId) => {
      if (!categoryId) {
        console.error('无效的分类ID:', categoryId);
        return;
      }

      // 关闭所有下拉框
      activeDropdownCategory.value = null;
      showBrandsDropdown.value = false;
      categoryBrands.value = [];

      // 导航到分类页面
      router.push({
        query: {
          ...route.query,
          categoryId: categoryId,
          brandId: undefined
        }
      });
    };

    // 处理下拉箭头点击（仅控制UI状态，不影响URL）
    const handleDropdownToggle = async (categoryId) => {
      if (!categoryId || isRequestPending.value) {
        return;
      }

      // 如果点击的是当前打开的下拉框，则关闭它
      if (activeDropdownCategory.value === categoryId && showBrandsDropdown.value) {
        activeDropdownCategory.value = null;
        showBrandsDropdown.value = false;
        categoryBrands.value = [];
        return;
      }

      // 否则，打开新的下拉框
      try {
        isRequestPending.value = true;

        // 设置UI状态（不影响URL）
        activeDropdownCategory.value = categoryId;
        selectedBrand.value = null;

        // 获取品牌数据
        if (brandsByCategory.value[categoryId]) {
          categoryBrands.value = brandsByCategory.value[categoryId];
        } else {
          await fetchBrandsByCategory(categoryId);
        }

        // 计算位置并显示下拉框
        calculateDropdownPosition();
        showBrandsDropdown.value = true;

      } finally {
        isRequestPending.value = false;
      }
    };
    
    // 计算下拉菜单位置
    const calculateDropdownPosition = () => {
      // 延迟执行，确保DOM已更新
      setTimeout(() => {
        // 优先查找悬停的分类，如果没有则查找选中的分类
        const hoveredChip = document.querySelector(`.filter-chip.hover`);
        const selectedChip = document.querySelector(`.filter-chip.active`);
        const targetChip = hoveredChip || selectedChip;

        if (targetChip) {
          const rect = targetChip.getBoundingClientRect();
          
          // 根据设备类型设置下拉菜单位置
          if (isMobile.value) {
            // 移动端显示在分类正下方，完全贴合
            dropdownPosition.value = {
              position: 'fixed',
              top: `${rect.bottom}px`, // 完全贴合，不留间隙
              left: '50%',
              transform: 'translateX(-50%)',
              width: `${Math.max(rect.width, 120)}px`, // 至少与按钮一样宽
              maxWidth: '350px',
              bottom: 'auto',
              '--arrow-left': '50%' // 箭头位置居中
            };

            // 添加滚动监听器，在滚动时关闭品牌下拉菜单
            const handleScroll = () => {
              showBrandsDropdown.value = false;
              window.removeEventListener('scroll', handleScroll);
            };
            window.addEventListener('scroll', handleScroll);
          } else {
            // PC端显示在分类下方，完全贴合
            dropdownPosition.value = {
              position: 'absolute',
              top: `${rect.bottom + window.scrollY}px`, // 完全贴合，不留间隙
              left: `${rect.left}px`,
              bottom: 'auto',
              width: `${Math.max(rect.width, 180)}px`, // 至少与按钮一样宽
              transform: 'none'
            };
          }
        } else {
          console.error('未找到选中的分类标签元素，使用默认位置');
          
          // 如果找不到选中的标签，设置一个默认位置
          if (isMobile.value) {
            dropdownPosition.value = {
              position: 'fixed',
              top: '150px',
              left: '50%',
              transform: 'translateX(-50%)',
              width: '90%',
              maxWidth: '350px',
              bottom: 'auto'
            };
          } else {
            dropdownPosition.value = {
              position: 'absolute',
              top: '150px',
              left: '50%',
              transform: 'translateX(-50%)',
              bottom: 'auto',
              width: '180px'
            };
          }
        }
      }, 10);
    };
    
    // 选择品牌
    const selectBrand = (brandId) => {
      // 如果已有请求在进行中，忽略
      if (isRequestPending.value) {
        return;
      }

      // 设置请求标志
      isRequestPending.value = true;

      try {
        if (selectedBrand.value === brandId) {
          selectedBrand.value = null;

          // 更新URL，移除品牌参数，保留分类参数
          router.replace({
            query: { ...route.query, brandId: undefined },
            // 添加选项阻止滚动到顶部
            scrollBehavior: () => false
          });
        } else {
          selectedBrand.value = brandId;

          // 确保对应的分类被设置为选中状态
          if (hoveredCategory.value) {
            selectedCategory.value = hoveredCategory.value;
          }

          // 更新URL，添加品牌参数和分类参数
          router.replace({
            query: { ...route.query, brandId: brandId, categoryId: selectedCategory.value },
            // 添加选项阻止滚动到顶部
            scrollBehavior: () => false
          });
        }

        // 清除悬停状态，关闭品牌下拉框
        hoveredCategory.value = null;
        showBrandsDropdown.value = false;

        // 使用选中的品牌获取产品
        fetchProducts();
      } finally {
        // 延迟重置请求标志，避免重复点击
        setTimeout(() => {
          isRequestPending.value = false;
        }, 300);
      }
    };
    
    // 获取分类下的品牌
    const fetchBrandsByCategory = async (categoryId) => {
      try {
        // 检查categoryId是否有效
        if (categoryId === undefined || categoryId === null) {
          console.error('无效的分类ID:', categoryId);
          return;
        }
        
        // 确保categoryId是字符串类型
        const catId = categoryId.toString();
        
        // 调用API获取品牌，确保传递categoryId参数
        try {
          const response = await productsApi.getBrandsByCategory(catId);
          
          if (response && response.data && Array.isArray(response.data)) {
            categoryBrands.value = response.data.map(brand => ({
              brandId: brand.brandId || brand.id,
              name: brand.name,
              logoUrl: brand.logoUrl || null,
              image: brand.image || null,
              parentCategoryId: categoryId
            }));
            brandsByCategory.value[categoryId] = categoryBrands.value;
          } else if (Array.isArray(response)) {
            categoryBrands.value = response.map(brand => ({
              brandId: brand.brandId || brand.id,
              name: brand.name,
              logoUrl: brand.logoUrl || null,
              image: brand.image || null,
              parentCategoryId: categoryId
            }));
            brandsByCategory.value[categoryId] = categoryBrands.value;
          } else {
            // 如果API返回的数据无效，使用模拟数据
            console.warn('API返回的品牌数据无效，使用模拟数据');
            const mockData = [
              { brandId: 1, name: 'Hoodies', parentCategoryId: categoryId, logoUrl: null, image: null },
              { brandId: 2, name: 'T-Shirt', parentCategoryId: categoryId, logoUrl: null, image: null },
              { brandId: 3, name: 'Pants', parentCategoryId: categoryId, logoUrl: null, image: null },
              { brandId: 4, name: 'Sets', parentCategoryId: categoryId, logoUrl: null, image: null }
            ];
            categoryBrands.value = mockData;
            brandsByCategory.value[categoryId] = mockData;
          }
        } catch (apiErr) {
          console.error('API调用失败:', apiErr);
          // 使用模拟数据
          const mockData = [
            { brandId: 1, name: 'Hoodies', parentCategoryId: categoryId, logoUrl: null, image: null },
            { brandId: 2, name: 'T-Shirt', parentCategoryId: categoryId, logoUrl: null, image: null },
            { brandId: 3, name: 'Pants', parentCategoryId: categoryId, logoUrl: null, image: null },
            { brandId: 4, name: 'Sets', parentCategoryId: categoryId, logoUrl: null, image: null }
          ];
          categoryBrands.value = mockData;
          brandsByCategory.value[categoryId] = mockData;
        }
      } catch (err) {
        console.error('获取品牌数据失败:', err, '分类ID:', categoryId);
        // 发生错误时使用模拟数据
        const mockData = [
          { brandId: 1, name: 'Hoodies', parentCategoryId: categoryId, logoUrl: null, image: null },
          { brandId: 2, name: 'T-Shirt', parentCategoryId: categoryId, logoUrl: null, image: null },
          { brandId: 3, name: 'Pants', parentCategoryId: categoryId, logoUrl: null, image: null },
          { brandId: 4, name: 'Sets', parentCategoryId: categoryId, logoUrl: null, image: null }
        ];
        categoryBrands.value = mockData;
        brandsByCategory.value[categoryId] = mockData;
      }
    };
    
    // 添加点击外部关闭下拉菜单
    const handleClickOutside = (event) => {
      // 如果点击的不是分类或其子元素，也不是品牌下拉菜单
      const filterChips = document.querySelectorAll('.filter-chip');
      const brandsDropdown = document.querySelector('.brands-dropdown-container');
      let clickedInside = false;
      
      // 检查是否点击在分类上
      filterChips.forEach(chip => {
        if (chip.contains(event.target)) {
          clickedInside = true;
        }
      });
      
      // 检查是否点击在品牌下拉菜单上
      if (brandsDropdown && brandsDropdown.contains(event.target)) {
        clickedInside = true;
      }
      
      // 如果点击在外部，关闭下拉菜单
      if (!clickedInside && showBrandsDropdown.value) {
        showBrandsDropdown.value = false;
        selectedCategory.value = null;
        categoryBrands.value = [];
      }
    };
    
    // 处理窗口尺寸变化
    const handleResize = () => {
      isMobile.value = window.innerWidth <= 480;
      // 更新滚动阈值
      scrollThreshold.value = window.innerWidth <= 768 ? 300 : 500;
      // 重新设置分类样式
      setupCategoriesStyle();
      if (selectedCategory.value !== null) {
        // 重新计算下拉菜单位置
        calculateDropdownPosition();
      }
    };
    
    // 设置分类样式
    const setupCategoriesStyle = () => {
      if (typeof window === 'undefined') return;
      
      setTimeout(() => {
        const filterChips = document.querySelector('.filter-chips');
        if (filterChips) {
          // 根据设备类型设置样式
          if (isMobile.value) {
            filterChips.style.flexWrap = 'wrap';
            filterChips.style.width = '100%';
          } else {
            // PC端可以根据需要设置不同样式
            filterChips.style.flexWrap = 'wrap';
          }
        }
      }, 100);
    };
    
    // 切换过滤下拉框显示状态
    const toggleFilterDropdown = (event) => {
      showFilterDropdown.value = !showFilterDropdown.value;

      // 动态计算移动端弹出层位置
      if (showFilterDropdown.value && isMobile.value) {
        nextTick(() => {
          const dropdown = document.querySelector('.mobile-filter-dropdown');
          const sortByButton = event?.currentTarget || document.querySelector('.mobile-sort-by');

          if (dropdown && sortByButton) {
            const rect = sortByButton.getBoundingClientRect();
            // 移动端使用 fixed 定位，计算相对于视口的固定位置
            dropdown.style.position = 'fixed';
            dropdown.style.top = `${rect.bottom + 8}px`;
            dropdown.style.left = `${rect.left}px`;
            dropdown.style.width = '300px';
            dropdown.style.maxWidth = 'calc(100vw - 2rem)';

            // 确保弹出层不会超出屏幕右边界
            const dropdownWidth = 300;
            if (rect.left + dropdownWidth > window.innerWidth) {
              dropdown.style.left = `${window.innerWidth - dropdownWidth - 16}px`;
            }

            // 确保弹出层不会超出屏幕左边界
            if (rect.left < 16) {
              dropdown.style.left = '16px';
            }

            // 添加滚动监听器，在滚动时关闭弹出层
            const handleScroll = () => {
              showFilterDropdown.value = false;
              window.removeEventListener('scroll', handleScroll);
            };
            window.addEventListener('scroll', handleScroll);
          }
        });
      }

      // 点击外部区域关闭下拉框
      if (showFilterDropdown.value) {
        setTimeout(() => {
          document.addEventListener('click', handleOutsideClick);
        }, 10);
      } else {
        document.removeEventListener('click', handleOutsideClick);
      }
    };
    
    // 设置激活的过滤选项
    const setActiveFilter = (filter) => {
      activeFilter.value = filter;
      
      // 如果切换到价格排序，默认为升序
      if (filter === 'price' && sortDirection.value !== 'asc' && sortDirection.value !== 'desc') {
        sortDirection.value = 'asc';
      }
      
      // 更新URL参数，添加排序类型和方向
      router.replace({
        query: { 
          ...route.query, 
          sort: filter,
          direction: sortDirection.value
        }
      });
      

      // 如果选择的是seller，获取商家列表
      if (filter === 'seller') {
        fetchAllSellers();
        // 关闭Sort by下拉框，显示商家选择弹窗
        showFilterDropdown.value = false;
        return;
      }

      // 应用过滤选项
      applySortingIfNeeded();

      // 重新加载排序后的产品数据
      displayedProducts.value = filteredProducts.value.slice(0, itemsPerPage.value);
      
      // 关闭下拉框
      showFilterDropdown.value = false;
    };
    


    // 切换分类菜单显示状态
    const toggleCategoryMenu = () => {
      showCategoryMenu.value = !showCategoryMenu.value;

      // 如果打开菜单，添加body的overflow:hidden防止滚动
      if (showCategoryMenu.value) {
        document.body.style.overflow = 'hidden';
        // 添加show类来触发动画
        setTimeout(() => {
          const menuElement = document.querySelector('.category-fullscreen');
          if (menuElement) {
            menuElement.classList.add('show');
          }
        }, 10);

        // 添加滚动监听器，在滚动时关闭分类菜单（虽然已经禁止了滚动，但作为额外保护）
        const handleScroll = () => {
          showCategoryMenu.value = false;
          const menuElement = document.querySelector('.category-fullscreen');
          if (menuElement) {
            menuElement.classList.remove('show');
            setTimeout(() => {
              document.body.style.overflow = '';
            }, 300);
          } else {
            document.body.style.overflow = '';
          }
          window.removeEventListener('scroll', handleScroll);
        };
        window.addEventListener('scroll', handleScroll);
      } else {
        // 先移除show类，等动画结束后再恢复滚动
        const menuElement = document.querySelector('.category-fullscreen');
        if (menuElement) {
          menuElement.classList.remove('show');
          setTimeout(() => {
            document.body.style.overflow = '';
          }, 300); // 等待动画结束
        } else {
          document.body.style.overflow = '';
        }
      }
    };

    // 设置活动侧边栏项目
    const setActiveSidebarItem = async (item) => {
      activeSidebarItem.value = item;
      
      // 更新URL，添加分类参数，移除品牌参数
      router.replace({
        query: { ...route.query, categoryId: item, brandId: undefined },
        // 添加选项阻止滚动到顶部
        scrollBehavior: () => false
      });

      // 获取对应分类的品牌数据
      if (!brandsByCategory.value[item]) {
        try {
          await fetchBrandsByCategory(item);
        } catch (error) {
          console.error('获取分类品牌数据失败:', error);
        }
      }
    };

    // 选择分类项目
    const selectCategoryItem = async (item) => {
      // 选择的是品牌
      selectedCategory.value = activeSidebarItem.value; // 设置当前选中的分类
      selectedBrand.value = item.brandId; // 设置选中的品牌

      // 更新URL，添加分类和品牌参数
      router.replace({
        query: { 
          ...route.query, 
          categoryId: activeSidebarItem.value, 
          brandId: item.brandId 
        },
        // 添加选项阻止滚动到顶部
        scrollBehavior: () => false
      });

      // 获取该品牌的产品
      await fetchProducts();

      toggleCategoryMenu(); // 选择后关闭菜单
    };
    
    // 处理点击外部区域
    const handleOutsideClick = (event) => {
      const filterIcon = document.querySelector('.filter-icon');
      const filterDropdown = document.querySelector('.filter-dropdown');
      
      if (filterIcon && filterDropdown) {
        // 如果点击的不是过滤图标或下拉框内的元素，则关闭下拉框
        if (!filterIcon.contains(event.target) && !filterDropdown.contains(event.target)) {
          showFilterDropdown.value = false;
          document.removeEventListener('click', handleOutsideClick);
        }
      }
    };
    
    // 设置排序方向
    const setSortDirection = (direction) => {
      sortDirection.value = direction;
      activeFilter.value = 'seller'; // 自动选择seller作为过滤条件
      
      // 更新URL参数
      router.replace({
        query: { 
          ...route.query, 
          sort: activeFilter.value,
          direction: direction
        }
      });
      
      // 应用排序
      applySortingIfNeeded();
      
      // 重新加载排序后的产品数据
      displayedProducts.value = filteredProducts.value.slice(0, itemsPerPage.value);
    };
    
    // 获取分类名称
    const getCategoryName = (categoryId) => {
      const category = categories.value.find(c => c.id === categoryId);
      return category ? category.name : 'Unknown Category';
    };

    // 获取分类对应的图标
    const getCategoryIcon = (categoryName) => {
      const iconMap = {
        'OMG': 'far fa-star',
        'Clothing': 'fas fa-tshirt',
        'Shoes': 'mdi mdi-shoe-sneaker', // 其他选项: 'fas fa-person-running', 'fas fa-walking', 'mdi mdi-shoe-formal'
        '3C Products': 'fas fa-mobile-alt',
        'Accessories': 'far fa-gem',
        'Jewelry&Watches': 'far fa-clock',
        'Other Products': 'fas fa-th-large'
      };

      // 如果找不到对应的图标，返回默认图标
      return iconMap[categoryName] || 'far fa-folder';
    };
    
    // 加载更多产品
    const loadMoreProducts = () => {
      if (isLoadingMore.value || noMoreProducts.value || isRequestPending.value) return;
      
      // 立即设置加载状态
      isLoadingMore.value = true;
      
      // 检查是否还有更多本地数据可显示
      const currentDisplayedCount = displayedProducts.value.length;
      
      // 如果本地还有更多数据未显示
      if (currentDisplayedCount < filteredProducts.value.length) {
        // 从本地数据加载更多
        const nextBatch = filteredProducts.value.slice(
          currentDisplayedCount, 
          currentDisplayedCount + itemsPerPage.value
        );
        
        // 使用setTimeout模拟加载过程，提供更好的视觉反馈
        setTimeout(() => {
          displayedProducts.value = [...displayedProducts.value, ...nextBatch];
          
          // 检查是否显示了所有本地数据
          if (displayedProducts.value.length >= filteredProducts.value.length && hasMorePages.value) {
            // 需要从服务器加载更多数据
            fetchProducts(true);
          } else if (displayedProducts.value.length >= filteredProducts.value.length) {
            // 没有更多数据了
            noMoreProducts.value = true;
            isLoadingMore.value = false;
          } else {
            // 还有本地数据，重置加载状态
            isLoadingMore.value = false;
          }
        }, 500); // 800ms的加载动画展示时间
      } else if (hasMorePages.value) {
        // 从服务器加载更多数据
        fetchProducts(true);
      } else {
        // 没有更多数据了
        noMoreProducts.value = true;
        isLoadingMore.value = false;
      }
    };
    
    // 添加滚动监听函数
    const handleScroll = () => {
      if (isLoading.value || isLoadingMore.value || noMoreProducts.value || isRequestPending.value) return;
      
      // 获取当前滚动位置
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      
      // 判断是否向下滚动
      const isScrollingDown = scrollTop > lastScrollTop.value;
      
      // 更新上次滚动位置
      lastScrollTop.value = scrollTop;
      
      // 只有向下滚动时才触发加载
      if (isScrollingDown) {
        // 计算滚动位置
        const scrollPosition = window.innerHeight + window.pageYOffset;
        const documentHeight = document.documentElement.scrollHeight;
        const distanceFromBottom = documentHeight - scrollPosition;
        
        // 当距离底部小于阈值时加载更多
        if (distanceFromBottom < scrollThreshold.value) {
          loadMoreProducts();
        }
      }
    };
    
    // 添加防抖函数
    const debounce = (fn, delay) => {
      let timer = null;
      return function() {
        const context = this;
        const args = arguments;
        clearTimeout(timer);
        timer = setTimeout(() => {
          fn.apply(context, args);
        }, delay);
      };
    };
    
    // 使用防抖处理滚动事件
    const debouncedHandleScroll = debounce(handleScroll, 200);
    
    // 处理收藏状态更新
    const handleCollectUpdated = (product) => {
      // 更新产品列表中对应产品的收藏状态
      const index = products.value.findIndex(p => p.id === product.id);
      if (index !== -1) {
        products.value[index].isCollected = product.isCollected;
      }
    };
    
    // 处理点赞状态更新
    const handleLikeUpdated = (product) => {
      // 更新产品列表中对应产品的点赞状态和点赞数
      const index = products.value.findIndex(p => p.id === product.id);
      if (index !== -1) {
        products.value[index].isLiked = product.isLiked;
        products.value[index].likes = product.likes;
      }
    };
    
    // 处理商品API响应
    function handleProductsResponse(response, isLoadMore = false) {
      if (response.code === 200 && response.data) {
        // 处理新的数据格式，数据在 data.list 中
        const dataList = response.data.list || response.data; // 兼容两种数据格式
        const totalCount = response.data.total || 0; // 获取总条数
        
        const newProducts = dataList.map(item => {
          // 获取视频URL，优先使用video字段，然后是videoUrl字段
          const videoUrl = item.video || item.videoUrl || null;

          // 调试日志：显示每个商品的视频信息
          console.log(`商品 ${item.name} (ID: ${item.productId || item.id}) 的视频URL:`, videoUrl);

          return {
            ...item,
            id: item.productId || item.id,
            name: item.name,
            price: item.price,
            imageUrl: item.mainImage || item.image,
            platform: item.platform || 'Unknown',
            likes: item.likes || 0,
            views: item.views || 0,
            comments: Array.isArray(item.comments) ? item.comments : [],
            productStatus: item.productStatus || 0, // 使用API返回的productStatus字段
            isCollected: false,
            // 使用接口返回的video字段，如果没有则使用null
            videoUrl: videoUrl,
            videoFitMode: item.videoFitMode || defaultVideoFitMode.value // 添加视频适配模式
          };
        });

        // 调试日志：显示处理后的商品数据
        console.log('ProductsTwo - handleProductsResponse 处理后的商品数据:', newProducts);

        if (!isLoadMore) {
          // 首次加载，替换产品列表
          products.value = newProducts;
          totalProducts.value = totalCount; // 使用服务器返回的总条数

          // 应用当前的商家筛选条件
          applyMerchantFilter();

          // 应用当前的排序状态（如果有）
          applySortingIfNeeded();

          // 重置分页并加载第一页
          currentPage.value = 1;
          displayedProducts.value = filteredProducts.value.slice(0, itemsPerPage.value);
        } else {
          // 加载更多，追加到现有产品列表
          products.value = [...products.value, ...newProducts];

          // 应用当前的商家筛选条件，而不是直接复制所有产品
          applyMerchantFilter();

          // 应用当前的排序状态（如果有）
          applySortingIfNeeded();

          // 重新计算需要显示的产品数量（保持之前显示的数量 + 新的一页）
          const newDisplayCount = displayedProducts.value.length + itemsPerPage.value;
          const actualDisplayCount = Math.min(newDisplayCount, filteredProducts.value.length);
          displayedProducts.value = filteredProducts.value.slice(0, actualDisplayCount);
        }
        
        // 根据总条数和当前已加载的数据量判断是否还有更多页
        const currentLoadedCount = products.value.length;
        const hasMoreData = currentLoadedCount < totalCount;
        
        if (!hasMoreData || newProducts.length < apiPageSize.value) {
          hasMorePages.value = false;
          noMoreProducts.value = true;
        } else {
          hasMorePages.value = true;
          apiCurrentPage.value++; // 为下次加载更多准备
        }
        
        return true;
      } else {
        console.error('获取产品错误:', response.msg);
        if (!isLoadMore) {
          products.value = [];
          filteredProducts.value = [];
          displayedProducts.value = [];
          totalProducts.value = 0; // 重置总条数
          // 设置错误消息
          error.value = `Failed to load products: ${response.msg || 'Unknown error'}`;
        }
        hasMorePages.value = false;
        noMoreProducts.value = true;
        return false;
      }
    }
    
    onMounted(async () => {
      checkCurrentTheme();

      // 获取分类数据
      await fetchCategories();

      // 获取产品总数
      await fetchProductsCount();
      
      // 如果有搜索关键词参数，优先处理搜索
      if (routeKeyword) {
        // 不需要调用handleSearch()，因为我们已经设置了searchQuery
        // 并且fetchProducts()会使用这个值
        isLoading.value = true;
        try {
          // 获取产品
          await fetchProducts();
        } finally {
          isLoading.value = false;
        }
      }
      // 如果有推荐参数，处理推荐商品请求
      else if (routeRecommended) {
        isLoading.value = true;
        try {
          // 获取推荐产品
          await fetchProducts();
        } finally {
          isLoading.value = false;
        }
      }
      // 直接测试API调用 - 手动构建请求
      else if (routeMerchant) {
        selectedSeller.value = {
          name: routeMerchant,
          id: `seller_${routeMerchant.toLowerCase().replace(/\s+/g, '_')}`
        };
        
        try {
          // 直接使用apiClient进行调试调用
          await apiClient.get('/omg/products/getAllMerchant', {
            params: {
              page: 1,
              pageSize: 20,
              merchant: routeMerchant
            }
          });
          
          // 手动构建请求参数
          const manualParams = {
            page: 1,
            pageSize: 20,
            merchant: routeMerchant
          };
          
          if (routeCategoryId) {
            manualParams.categoryId = routeCategoryId;
          }
          
          if (routeBrandId) {
            manualParams.brandId = routeBrandId;
          }
          
          // 正常发送API请求
          isLoading.value = true;
          
          try {
            const response = await productsApi.getProductsByCategory(
              routeCategoryId || null, 
              routeBrandId || null, 
              manualParams
            );
            
            handleProductsResponse(response, false);
          } catch (err) {
            console.error('❌ 商家商品请求失败:', err);
            error.value = `获取商家商品失败: ${err.message || '未知错误'}`;
          } finally {
            isLoading.value = false;
          }
        } catch (testErr) {
          console.error('🧪 测试直接API调用 - 错误:', testErr);
        }
      } else {
        // 没有商家参数时，正常获取商品
        fetchProducts();
      }
      
      // 使用MutationObserver监听主题变化
      const htmlElement = document.documentElement;
      themeObserver.value = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.attributeName === 'data-theme') {
            checkCurrentTheme();
          }
        });
      });
      
      themeObserver.value.observe(htmlElement, { attributes: true, attributeFilter: ['data-theme'] });
      
      // 添加主题变化监听
      window.addEventListener('themechange', checkCurrentTheme);
      
      // 如果存在emitter，也添加监听
      if (window.emitter) {
        window.emitter.on('theme-changed', handleThemeChange);
      }
      
      // 监听系统主题变化
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      mediaQuery.addEventListener('change', (e) => {
        isDarkMode.value = e.matches;
        applySearchTheme(); // 应用搜索框主题
      });
      
      // 设置分类样式
      setupCategoriesStyle();
      
      // 添加全局点击事件
      document.addEventListener('click', handleClickOutside);
      
      // 添加窗口尺寸变化事件
      window.addEventListener('resize', handleResize);
      
      // 添加滚动监听
      window.addEventListener('scroll', debouncedHandleScroll);
      isScrollListenerActive.value = true;
      
      // 组件卸载时清理
      onUnmounted(() => {
        // 停止观察DOM变化
        if (themeObserver.value) {
          themeObserver.value.disconnect();
        }
        
        // 移除主题变化监听
        window.removeEventListener('themechange', checkCurrentTheme);
        
        if (window.emitter) {
          window.emitter.off('theme-changed', handleThemeChange);
        }
        
        // 移除系统主题变化监听
        mediaQuery.removeEventListener('change', () => {});
        
        // 移除全局点击事件
        document.removeEventListener('click', handleClickOutside);
        document.removeEventListener('click', handleOutsideClick);
        
        // 移除窗口尺寸变化事件
        window.removeEventListener('resize', handleResize);
        
        // 移除滚动监听
        if (isScrollListenerActive.value) {
          window.removeEventListener('scroll', debouncedHandleScroll);
          isScrollListenerActive.value = false;
        }
      });
    });
    
    // 初始化时，检查URL参数中是否有商家参数
    if (routeMerchant) {
      // 设置选中的商家
      selectedSeller.value = {
        name: routeMerchant,
        id: `seller_${routeMerchant.toLowerCase().replace(/\s+/g, '_')}`
      };
    }

    // 新增方法
    const toggleMobileFilters = () => {
      isMobileFiltersOpen.value = !isMobileFiltersOpen.value;
    };

    const handlePriceFilter = (event) => {
      showPriceDropdown.value = !showPriceDropdown.value;

      // 动态计算弹出层位置
      if (showPriceDropdown.value) {
        nextTick(() => {
          const dropdown = document.querySelector('.price-filter-dropdown');

          if (dropdown) {
            if (isMobile.value) {
              // 移动端：相对于移动端 Price 按钮定位
              const priceButton = event?.currentTarget || document.querySelector('.mobile-filter-btn');
              if (priceButton) {
                const rect = priceButton.getBoundingClientRect();
                // 移动端使用 fixed 定位
                dropdown.style.position = 'fixed';
                dropdown.style.top = `${rect.bottom + 8}px`;
                dropdown.style.left = `${rect.left}px`;
                dropdown.style.width = `${Math.min(340, window.innerWidth - rect.left - 16)}px`;

                // 添加滚动监听器，在滚动时关闭弹出层
                const handleScroll = () => {
                  showPriceDropdown.value = false;
                  window.removeEventListener('scroll', handleScroll);
                };
                window.addEventListener('scroll', handleScroll);
              }
            } else {
              // PC端：相对于PC端 Price 按钮定位
              const pcPriceButton = event?.currentTarget || document.querySelector('.filter-text-btn');
              if (pcPriceButton) {
                const rect = pcPriceButton.getBoundingClientRect();
                // PC端使用 absolute 定位，需要加上滚动距离
                dropdown.style.position = 'absolute';
                dropdown.style.top = `${rect.bottom + window.scrollY + 8}px`;
                dropdown.style.left = `${rect.left + window.scrollX}px`;
                dropdown.style.width = '340px'; // PC端固定宽度
              }
            }
          }
        });
      }

      // 点击外部区域关闭下拉框
      if (showPriceDropdown.value) {
        setTimeout(() => {
          document.addEventListener('click', handlePriceOutsideClick);
        }, 10);
      } else {
        document.removeEventListener('click', handlePriceOutsideClick);
      }
    };

    const handleSellerFilter = () => {
      // 调用获取商家列表的函数，这会显示商家选择弹窗
      fetchAllSellers();
    };

    const handlePlatformFilter = (event) => {
      showPlatformDropdown.value = !showPlatformDropdown.value;

      // 动态计算弹出层位置
      if (showPlatformDropdown.value) {
        nextTick(() => {
          const dropdown = document.querySelector('.platform-filter-dropdown');

          if (dropdown) {
            if (isMobile.value) {
              // 移动端：相对于移动端 Platform 按钮定位
              const platformButton = event?.currentTarget || document.querySelector('.mobile-filter-btn');
              if (platformButton) {
                const rect = platformButton.getBoundingClientRect();
                // 移动端使用 fixed 定位，往左移动一点点
                dropdown.style.position = 'fixed';
                dropdown.style.top = `${rect.bottom + 8}px`;
                dropdown.style.left = `${rect.left - 20}px`; // 往左移动20px
                dropdown.style.width = `${Math.min(280, window.innerWidth - rect.left - 16)}px`;

                // 添加滚动监听器，在滚动时关闭弹出层
                const handleScroll = () => {
                  showPlatformDropdown.value = false;
                  window.removeEventListener('scroll', handleScroll);
                };
                window.addEventListener('scroll', handleScroll);
              }
            } else {
              // PC端：相对于PC端 Platform 按钮定位
              const pcPlatformButton = event?.currentTarget || document.querySelector('.filter-text-btn');
              if (pcPlatformButton) {
                const rect = pcPlatformButton.getBoundingClientRect();
                // PC端使用 absolute 定位，需要加上滚动距离
                dropdown.style.position = 'absolute';
                dropdown.style.top = `${rect.bottom + window.scrollY + 8}px`;
                dropdown.style.left = `${rect.left + window.scrollX}px`;
                dropdown.style.width = '280px'; // PC端固定宽度
              }
            }
          }
        });
      }

      // 点击外部区域关闭下拉框
      if (showPlatformDropdown.value) {
        setTimeout(() => {
          document.addEventListener('click', handlePlatformOutsideClick);
        }, 10);
      } else {
        document.removeEventListener('click', handlePlatformOutsideClick);
      }
    };

    // 价格筛选相关方法
    const handlePriceOutsideClick = (event) => {
      const dropdown = document.querySelector('.price-filter-dropdown');
      const mobilePriceButton = document.querySelector('.mobile-filter-btn');
      const pcPriceButton = document.querySelector('.filter-text-btn');

      // 检查是否点击在弹出层或任何价格按钮上
      const clickedOnDropdown = dropdown && dropdown.contains(event.target);
      const clickedOnMobileButton = mobilePriceButton && mobilePriceButton.contains(event.target);
      const clickedOnPcButton = pcPriceButton && pcPriceButton.contains(event.target);

      if (!clickedOnDropdown && !clickedOnMobileButton && !clickedOnPcButton) {
        showPriceDropdown.value = false;
        document.removeEventListener('click', handlePriceOutsideClick);
      }
    };

    const selectPriceRange = (range) => {
      selectedPriceRange.value = range;
      customMinPrice.value = range.min?.toString() || '';
      customMaxPrice.value = range.max?.toString() || '';

      // 立即应用筛选
      applyPriceFilter();
    };

    const resetPriceRange = () => {
      selectedPriceRange.value = null;
      customMinPrice.value = '';
      customMaxPrice.value = '';

      // 重置全局价格范围状态
      priceRange.value = {
        min: null,
        max: null
      };

      // 恢复所有产品显示
      filteredProducts.value = [...products.value];

      // 应用当前的排序（如果有）
      applySortingIfNeeded();

      // 重置分页并更新显示的产品
      currentPage.value = 1;
      displayedProducts.value = filteredProducts.value.slice(0, itemsPerPage.value);

      ElMessage({
        message: 'Price filter reset successfully',
        type: 'success',
        duration: 2000
      });

      // 关闭弹出层
      showPriceDropdown.value = false;
      document.removeEventListener('click', handlePriceOutsideClick);
    };

    const applyPriceFilter = () => {
      // 获取价格范围
      let minPrice = null;
      let maxPrice = null;

      // 如果有自定义输入，使用自定义值
      if (customMinPrice.value && !isNaN(parseFloat(customMinPrice.value))) {
        minPrice = parseFloat(customMinPrice.value);
      }
      if (customMaxPrice.value && !isNaN(parseFloat(customMaxPrice.value))) {
        maxPrice = parseFloat(customMaxPrice.value);
      }

      // 如果没有自定义输入但有选中的区间，使用区间值
      if (selectedPriceRange.value && minPrice === null && maxPrice === null) {
        minPrice = selectedPriceRange.value.min;
        maxPrice = selectedPriceRange.value.max;
      }

      // 验证价格范围
      if (minPrice !== null && maxPrice !== null && minPrice > maxPrice) {
        ElMessage({
          message: 'Minimum price cannot be higher than maximum price',
          type: 'error',
          duration: 2000
        });
        return;
      }

      // 更新全局价格范围状态
      priceRange.value = {
        min: minPrice,
        max: maxPrice
      };

      // 应用价格筛选到当前产品列表
      if (minPrice !== null || maxPrice !== null) {
        // 将用户输入的美元价格转换为人民币进行比较
        const minPriceRMB = minPrice !== null ? convertUSDToRMB(minPrice) : null;
        const maxPriceRMB = maxPrice !== null ? convertUSDToRMB(maxPrice) : null;

        console.log('Price filter (USD):', { min: minPrice, max: maxPrice });
        console.log('Price filter (RMB for comparison):', { min: minPriceRMB, max: maxPriceRMB });

        filteredProducts.value = products.value.filter(product => {
          const productPriceRMB = parseFloat(product.price) || 0; // 后端返回的人民币价格

          // 如果只有最小价格
          if (minPriceRMB !== null && maxPriceRMB === null) {
            return productPriceRMB >= minPriceRMB;
          }
          // 如果只有最大价格
          if (minPriceRMB === null && maxPriceRMB !== null) {
            return productPriceRMB <= maxPriceRMB;
          }
          // 如果有价格范围
          if (minPriceRMB !== null && maxPriceRMB !== null) {
            return productPriceRMB >= minPriceRMB && productPriceRMB <= maxPriceRMB;
          }

          return true;
        });

        // 显示筛选结果消息
        ElMessage({
          message: `Found ${filteredProducts.value.length} products in price range $${minPrice || 0} - $${maxPrice || '∞'}`,
          type: 'success',
          duration: 3000
        });
      } else {
        // 如果没有价格筛选，显示所有产品
        filteredProducts.value = [...products.value];
        ElMessage({
          message: 'Price filter cleared',
          type: 'info',
          duration: 2000
        });
      }

      // 应用当前的排序（如果有）
      applySortingIfNeeded();

      // 重置分页并更新显示的产品
      currentPage.value = 1;
      displayedProducts.value = filteredProducts.value.slice(0, itemsPerPage.value);

      console.log('Applied price filter:', {
        range: selectedPriceRange.value,
        min: minPrice,
        max: maxPrice,
        filteredCount: filteredProducts.value.length,
        totalCount: products.value.length
      });

      // 关闭弹出层
      showPriceDropdown.value = false;
      document.removeEventListener('click', handlePriceOutsideClick);
    };

    // 清除选中的价格区间（当用户手动输入时）
    const clearSelectedRange = () => {
      selectedPriceRange.value = null;
    };

    // 平台筛选相关状态
    const showPlatformDropdown = ref(false)
    const selectedPlatform = ref(null)

    // 平台选项
    const platformOptions = ref([
      { id: 'all', name: 'All Platforms', value: null },
      { id: 'taobao', name: 'Taobao', value: 'taobao' },
      { id: 'weidian', name: 'Weidian', value: 'weidian' },
      { id: '1688', name: '1688', value: '1688' }
    ])

    // 平台筛选相关方法
    const handlePlatformOutsideClick = (event) => {
      const dropdown = document.querySelector('.platform-filter-dropdown');
      const mobilePlatformButton = document.querySelector('.mobile-filter-btn');
      const pcPlatformButton = document.querySelector('.filter-text-btn');

      // 检查是否点击在弹出层或任何平台按钮上
      const clickedOnDropdown = dropdown && dropdown.contains(event.target);
      const clickedOnMobileButton = mobilePlatformButton && mobilePlatformButton.contains(event.target);
      const clickedOnPcButton = pcPlatformButton && pcPlatformButton.contains(event.target);

      if (!clickedOnDropdown && !clickedOnMobileButton && !clickedOnPcButton) {
        showPlatformDropdown.value = false;
        document.removeEventListener('click', handlePlatformOutsideClick);
      }
    };

    const selectPlatform = (platform) => {
      selectedPlatform.value = platform;
      applyPlatformFilter();
    };

    const applyPlatformFilter = () => {
      // 定义平台映射关系（移到函数顶部，确保在整个函数中都可访问）
      const platformMapping = {
        'taobao': ['taobao', '淘宝', 'tb'],
        'weidian': ['weidian', '微店', 'wd'],
        '1688': ['1688', 'alibaba']
      };

      if (selectedPlatform.value && selectedPlatform.value.value) {
        // 根据选中的平台筛选产品
        filteredProducts.value = products.value.filter(product => {
          const productPlatform = (product.platform || product.source || '').toLowerCase();
          const selectedPlatformValue = selectedPlatform.value.value.toLowerCase();

          // 获取当前选择平台的所有可能匹配项
          const matchingTerms = platformMapping[selectedPlatformValue] || [selectedPlatformValue];

          // 检查产品平台是否匹配任何一个可能的术语
          return matchingTerms.some(term => {
            const termLower = term.toLowerCase();
            return productPlatform.includes(termLower) || termLower.includes(productPlatform);
          });
        });

        ElMessage({
          message: `Found ${filteredProducts.value.length} products from ${selectedPlatform.value.name}`,
          type: 'success',
          duration: 3000
        });
      } else {
        // 显示所有平台的产品
        filteredProducts.value = [...products.value];
        ElMessage({
          message: 'Platform filter cleared - showing all platforms',
          type: 'info',
          duration: 2000
        });
      }

      // 应用当前的排序（如果有）
      applySortingIfNeeded();

      // 重置分页并更新显示的产品
      currentPage.value = 1;
      displayedProducts.value = filteredProducts.value.slice(0, itemsPerPage.value);

      console.log('Applied platform filter:', {
        platform: selectedPlatform.value,
        filteredCount: filteredProducts.value.length,
        totalCount: products.value.length,
        platformMapping: selectedPlatform.value ? platformMapping[selectedPlatform.value.value] : null
      });

      // 关闭弹出层
      showPlatformDropdown.value = false;
      document.removeEventListener('click', handlePlatformOutsideClick);
    };

    return {
      searchQuery,
      isDarkMode,
      handleSearch,
      categories,
      selectedCategory,
      hoveredCategory,
      isHoveringDropdown,
      toggleCategoryDropdown,
      handleCategoryHover,
      handleCategoryAreaLeave,
      selectCategory,
      categoryBrands,
      selectedBrand,
      selectBrand,
      dropdownPosition,
      isMobile,
      showFilterDropdown,
      showCategoryMenu,
      showBrandsDropdown,  // 添加showBrandsDropdown到返回值
      activeSidebarItem,
      categoryItems,
      currentCategoryItems,
      activeFilter,
      toggleFilterDropdown,
      toggleCategoryMenu,
      setActiveSidebarItem,
      selectCategoryItem,
      setActiveFilter,
      sortDirection,
      setSortDirection,
      getCategoryName,
      getCategoryIcon,
      searchContainerRef,
      // 替换模拟数据为API获取的数据
      products: displayedProducts,
      isLoading,
      isLoadingMore,
      noMoreProducts,
      loadMoreProducts,
      error,
      handleCollectUpdated,
      handleLikeUpdated,
      // Merchant related exports
      sellers,
      isLoadingSellers,
      showSellersModal,
      selectedSeller,
      fetchAllSellers,
      selectSeller,
      closeSellersModal,
      applyMerchantFilter,
      // 新增的方法和状态
      isMobileFiltersOpen,
      toggleMobileFilters,
      handlePriceFilter,
      handleSellerFilter,
      handlePlatformFilter,
      // 价格筛选相关
      showPriceDropdown,
      selectedPriceRange,
      customMinPrice,
      customMaxPrice,
      priceRanges,
      selectPriceRange,
      resetPriceRange,
      applyPriceFilter,
      clearSelectedRange,
      // 货币转换相关
      exchangeRate,
      convertUSDToRMB,
      convertRMBToUSD,
      // 平台筛选相关
      showPlatformDropdown,
      selectedPlatform,
      platformOptions,
      selectPlatform,
      applyPlatformFilter,
      // 统计数据
      statsData
    };
  }
}
</script>

<style scoped>
.products-two {
  padding: 2rem 5rem;
  min-height: 100vh;
  color: #e0e0e0;
  /* 移除背景色和渐变设置，让StarryBackground生效 */
  background: transparent;
  background-image: none;
  position: relative;
  z-index: 1; /* 确保内容在星空背景之上 */
}

/* 世界地图部分的样式 */
.world-map-section {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 1rem;
  margin-bottom: 2rem;
}

/* 主题响应样式 */
[data-theme="dark"] .world-map-section {
  background: var(--card-bg) !important;
}

[data-theme="light"] .world-map-section {
  background: var(--card-bg) !important;
}

/* 搜索框样式 */
.search-container {
  display: flex;
  align-items: center;
  max-width: 1400px;
  height: 56px;
  border-radius: 8px;
  margin: 0 auto 2rem;
  padding: 0 16px;
  transition: all 0.3s ease;
  background-color: transparent;
  border: none;
  box-shadow: none;
  overflow: hidden;
}

/* 深色主题搜索框 */
.search-container.dark-theme {
  background-color: #1e1e2f;
  border: 1px solid #2a2a3a;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* 浅色主题搜索框 */
.search-container.light-theme {
  background-color: #e8d0f5;
  border: none;
  box-shadow: 0 2px 8px rgba(142, 45, 226, 0.2);
}

.search-icon, .camera-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  padding: 0 10px;
  transition: color 0.3s ease;
}

.search-icon {
  cursor: pointer;
}

.search-icon:hover {
  opacity: 0.7;
}

.dark-theme .search-icon,
.dark-theme .camera-icon {
  color: #e0e0e0;
}

.light-theme .search-icon,
.light-theme .camera-icon {
  color: #8e2de2;
}

.search-input {
  flex: 1;
  height: 100%;
  background: transparent;
  border: none;
  font-size: 16px;
  padding: 0 12px;
  outline: none;
  transition: color 0.3s ease;
}

.dark-theme .search-input {
  color: #e0e0e0;
}

.dark-theme .search-input::placeholder {
  color: #a0a0a0;
}

.light-theme .search-input {
  color: #333;
}

.light-theme .search-input::placeholder {
  color: #666;
}

/* 添加移动端世界地图容器样式 */
@media (max-width: 768px) {
  .world-map-section {
    width: 100%;
    max-width: 100%;
    padding: 0.5rem;
    margin: 0 auto 0.5rem;
    display: flex;
    justify-content: center;
    box-sizing: border-box;
  }
  
  .products-two {
    padding: 0.3rem;
  }
  
  .search-container {
    width: calc(100% - 0.6rem);
    margin: 0 auto 0.5rem;
    height: 48px; /* 移动端稍微降低高度 */
    border-radius: 6px; /* 移动端稍微减小圆角 */
  }
}

/* ================ 分类过滤器样式 ================ */
.navbar-filter {
  display: flex;
  align-items: center;
  justify-content: center;
  max-width: 1400px;
  margin: 0 auto 2rem;
  padding: 0 1rem;
  position: relative;
}

.filter-icon {
  margin-right: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  flex-shrink: 0;
}

/* 深色主题下的过滤器图标 */
[data-theme="dark"] .filter-icon {
  background-color: #8e2de2;
  box-shadow: 0 2px 8px rgba(142, 45, 226, 0.3);
}

/* 浅色主题下的过滤器图标 */
[data-theme="light"] .filter-icon {
  background-color: #e8d0f5;
  border: none;
  box-shadow: 0 2px 8px rgba(142, 45, 226, 0.2);
}

/* 深色主题下的图标悬停效果 */
[data-theme="dark"] .filter-icon:hover {
  background-color: #9d4de8;
  transform: scale(1.05);
}

/* 浅色主题下的图标悬停效果 */
[data-theme="light"] .filter-icon:hover {
  background-color: #d9b5ef;
  transform: scale(1.05);
}

.filter-icon svg {
  transition: transform 0.3s ease;
}

/* 深色主题下的SVG图标颜色 */
[data-theme="dark"] .filter-icon svg {
  color: #ffffff;
}

/* 浅色主题下的SVG图标颜色 */
[data-theme="light"] .filter-icon svg {
  color: #8e2de2;
}

.filter-icon svg.rotate {
  transform: rotate(180deg);
}

.category-tabs {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
  padding-bottom: 4px;
  max-width: 80%;
}

.category-tabs::-webkit-scrollbar {
  display: none;
}

.filter-chip {
  padding: 8px 20px;
  border-radius: 20px;
  font-size: 14px;
  cursor: pointer;
  white-space: nowrap;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 5px;
  border: none;
}

/* 深色主题下的筛选器按钮 */
[data-theme="dark"] .filter-chip {
  background-color: #2a1a4a;
  color: #ffffff;
  border: none;
}

/* 浅色主题下的筛选器按钮 */
[data-theme="light"] .filter-chip {
  background-color: #e8d0f5;
  color: #333;
  border: none;
}

.dropdown-arrow {
  font-size: 10px;
  opacity: 0.7;
  transition: transform 0.3s ease;
}

.filter-chip.active .dropdown-arrow {
  transform: rotate(180deg);
  opacity: 1;
}

/* 深色主题下悬停效果 */
[data-theme="dark"] .filter-chip:hover {
  background-color: #3a2a5a;
}

/* 浅色主题下悬停效果 */
[data-theme="light"] .filter-chip:hover {
  background-color: #d9b5ef;
}

/* 深色主题下激活状态 */
[data-theme="dark"] .filter-chip.active {
  background-color: #8e2de2;
  box-shadow: 0 2px 8px rgba(142, 45, 226, 0.3);
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
  position: relative;
  z-index: 1001;
  border-bottom: none;
  border-left: none;
  border-right: none;
  border-top: none;
  color: #ffffff;
}

/* 浅色主题下激活状态 */
[data-theme="light"] .filter-chip.active {
  background-color: #9d4de8;
  box-shadow: 0 2px 8px rgba(142, 45, 226, 0.2);
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
  position: relative;
  z-index: 1001;
  border-bottom: none;
  border-left: none;
  border-right: none;
  border-top: none;
  color: #ffffff;
}

/* 过滤下拉框 */
.filter-dropdown {
  position: absolute;
  top: 100%; /* 相对于Sort by按钮的底部 */
  left: 0; /* 左对齐到Sort by按钮 */
  transform: none; /* 移除居中变换 */
  width: 400px; /* 固定宽度 */
  max-width: 500px;
  border-radius: 12px;
  z-index: 1100; /* 提高z-index确保在遮罩层之上 */
  padding: 14px;
  animation: fadeIn 0.2s ease;
  backdrop-filter: blur(5px);
  margin-top: 8px; /* 添加一点间距 */
}

/* 深色主题下的过滤下拉框 */
[data-theme="dark"] .filter-dropdown {
  background: rgba(43, 32, 73, 0.98);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
}

/* 浅色主题下的过滤下拉框 */
[data-theme="light"] .filter-dropdown {
  background: rgba(232, 208, 245, 0.98);
  box-shadow: 0 8px 20px rgba(142, 45, 226, 0.15);
  border: none;
}

.filter-options {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-gap: 12px;
  padding: 2px;
}

.filter-option {
  height: 52px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-align: center;
  padding: 0 15px;
  position: relative;
  letter-spacing: 0.5px;
}

/* 深色主题下的过滤选项 */
[data-theme="dark"] .filter-option {
  background-color: rgba(90, 51, 160, 0.7);
  color: #ffffff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  border: none;
}

/* 浅色主题下的过滤选项 */
[data-theme="light"] .filter-option {
  background-color: rgba(232, 208, 245, 0.7);
  color: #333;
  box-shadow: 0 1px 3px rgba(142, 45, 226, 0.2);
  border: none;
}

/* 深色主题下的悬停效果 */
[data-theme="dark"] .filter-option:hover {
  background-color: rgba(114, 69, 193, 0.9);
  box-shadow: 0 2px 10px 0 rgba(142, 45, 226, 0.2);
  transform: translateY(-1px);
}

/* 浅色主题下的悬停效果 */
[data-theme="light"] .filter-option:hover {
  background-color: rgba(217, 181, 239, 0.9);
  box-shadow: 0 2px 10px 0 rgba(142, 45, 226, 0.1);
  transform: translateY(-1px);
}

.filter-option:active {
  transform: translateY(0);
}

/* 深色主题下的激活状态 */
[data-theme="dark"] .filter-option.active {
  background-color: #8e2de2;
  box-shadow: 0 3px 10px rgba(142, 45, 226, 0.3);
}

/* 浅色主题下的激活状态 */
[data-theme="light"] .filter-option.active {
  background-color: #9d4de8;
  box-shadow: 0 3px 10px rgba(142, 45, 226, 0.2);
  color: #ffffff;
}

.seller-option {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  grid-column: 1;
}

.seller-option span {
  padding-left: 0;
  padding-right: 0;
}

.arrow-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
  position: absolute;
  right: 15px;
  top: 0;
  bottom: 0;
}

.arrow-up, .arrow-down {
  cursor: pointer;
  height: 14px;
  opacity: 0.7;
  transition: all 0.2s ease;
  margin: 1px 0;
}

/* 深色主题下的箭头颜色 */
[data-theme="dark"] .arrow-up, 
[data-theme="dark"] .arrow-down {
  color: #ffffff;
}

/* 浅色主题下的箭头颜色 */
[data-theme="light"] .arrow-up, 
[data-theme="light"] .arrow-down {
  color: #8e2de2;
}

.arrow-up:hover, .arrow-down:hover {
  opacity: 1;
  transform: scale(1.15);
}

.active .arrow-up, .active .arrow-down {
  opacity: 1;
}



/* 背景遮罩 */
.filter-dropdown-backdrop, .dropdown-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 1090; /* 提高z-index，但略低于下拉菜单 */
  animation: backdropFadeIn 0.2s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes backdropFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 移动端适配 */
@media (max-width: 768px) {
  .navbar-filter {
    padding: 0 10px;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
    justify-content: center;
  }
  
  .navbar-filter::-webkit-scrollbar {
    display: none;
  }
  
  .filter-icon {
    margin-right: 10px;
  }
  
  .category-tabs {
    gap: 8px;
    max-width: 75%;
    justify-content: flex-start;
  }
  
  .filter-chip {
    padding: 6px 16px;
    font-size: 13px;
  }
  
  .filter-dropdown {
    width: 95%;
    max-width: 450px;
    left: 50%;
    transform: translateX(-50%);
  }
}

@media (max-width: 480px) {
  .navbar-filter {
    justify-content: center;
    padding: 0 0px;
    transform: none;
    flex-direction: column;
    width: 100%;
    margin-bottom: 2.5rem;
    position: relative; /* 添加相对定位，作为筛选器弹出框的定位参考 */
  }
  
  .category-tabs {
    max-width: 100%;
    margin-top: 10px;
    flex-wrap: wrap;
    justify-content: center;
    gap: 8px;
    padding: 0 5px;
  }
  
  .filter-chip {
    margin-bottom: 5px;
    min-width: 80px;
    text-align: center;
    justify-content: center;
  }
  
  /* 移动端旧的筛选器样式已移动到新的mobile-filter-dropdown类 */
  
  .filter-options {
    grid-gap: 10px; /* 略微增加间距，确保按钮之间不会太挤 */
    padding: 0; /* 移除内边距 */
  }
  
  .filter-option {
    height: 42px; /* 稍微减小高度 */
    font-size: 14px; /* 减小字体大小 */
    padding: 0 8px; /* 减小水平内边距 */
  }
  
  .seller-option {
    grid-column: 1;
    grid-row: 3;
    justify-content: center;
    padding-left: 0;
    padding-right: 0;
  }
  

  
  .arrow-container {
    right: 12px;
  }
  
  .brands-dropdown-container.mobile-dropdown {
    position: fixed;
    top: auto;
    left: 50%;
    transform: translateX(-50%);
    width: 90%;
    max-width: 350px;
    z-index: 1000;
    margin-top: 10px; /* 添加顶部外边距，适应换行布局 */
  }
}

/* 品牌下拉菜单样式 */
.brands-dropdown-container {
  position: absolute;
  z-index: 1000;
  width: 180px;
  margin-top: -75px; /* 移除顶部外边距，确保完全贴合 */
}

.brands-dropdown-container.mobile-dropdown {
  position: fixed;
  border-radius: 12px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.5);
  z-index: 1000;
  left: 50% !important;
  transform: translateX(-50%) !important;
  width: 90% !important;
  max-width: 350px !important;
  margin-top: 0; /* 移除顶部外边距，确保完全贴合 */
}

.brands-dropdown {
  border-radius: 12px;
  padding-top: 0;
  padding-bottom: 5px;
  animation: fadeIn 0.3s ease;
  overflow: hidden;
  position: relative;
  width: 100%;
  margin-top: 0;
  border-top-left-radius: 0; /* 使顶部边角与按钮贴合 */
  border-top-right-radius: 0; /* 使顶部边角与按钮贴合 */
}

/* 深色主题下的品牌下拉菜单 */
[data-theme="dark"] .brands-dropdown {
  background-color: #2a1a4a;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
  border: none;
  margin-top: 20px;
}

/* 浅色主题下的品牌下拉菜单 */
[data-theme="light"] .brands-dropdown {
  background-color: #e8d0f5;
  box-shadow: 0 8px 20px rgba(142, 45, 226, 0.15);
  border: none;
  margin-top: 20px;
}

/* 移除箭头，使下拉框直接与按钮贴合 */
.brands-dropdown::before {
  display: none;
}

.mobile-dropdown .brands-dropdown::before {
  display: none;
}

/* 调整下拉框顶部样式 */
.dropdown-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 15px;
  position: sticky;
  top: 0;
  z-index: 2;
}

/* 深色主题下的下拉框头部 */
[data-theme="dark"] .dropdown-header {
  background-color: #3a2a5a;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* 浅色主题下的下拉框头部 */
[data-theme="light"] .dropdown-header {
  background-color: #d9b5ef;
  border-bottom: 1px solid rgba(142, 45, 226, 0.1);
}

.dropdown-header span {
  font-weight: 500;
  font-size: 14px;
}

/* 深色主题下的标题文字 */
[data-theme="dark"] .dropdown-header span {
  color: #e0e0e0;
}

/* 浅色主题下的标题文字 */
[data-theme="light"] .dropdown-header span {
  color: #333;
}

.brand-dropdown-item {
  padding: 8px 15px; /* 减少内边距使其更紧凑 */
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  text-align: center;
}

/* 深色主题下的品牌项 */
[data-theme="dark"] .brand-dropdown-item {
  color: #ffffff;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

/* 浅色主题下的品牌项 */
[data-theme="light"] .brand-dropdown-item {
  color: #333;
  border-bottom: 1px solid rgba(142, 45, 226, 0.05);
}

.brand-dropdown-item:last-child {
  border-bottom: none;
}

/* 深色主题下的品牌项悬停效果 */
[data-theme="dark"] .brand-dropdown-item:hover {
  background-color: rgba(90, 51, 160, 0.6);
}

/* 浅色主题下的品牌项悬停效果 */
[data-theme="light"] .brand-dropdown-item:hover {
  background-color: rgba(217, 181, 239, 0.6);
}

/* 深色主题下的激活品牌项 */
[data-theme="dark"] .brand-dropdown-item.active {
  background-color: #8e2de2;
  font-weight: 500;
  color: #ffffff;
}

/* 浅色主题下的激活品牌项 */
[data-theme="light"] .brand-dropdown-item.active {
  background-color: #9d4de8;
  font-weight: 500;
  color: #ffffff;
}

/* 背景遮罩 */
.dropdown-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 999;
  animation: backdropFadeIn 0.2s ease;
  cursor: pointer; /* 添加指针样式，提示用户可点击 */
}

.empty-brands {
  padding: 20px;
  text-align: center;
  color: rgba(255, 255, 255, 0.6);
  font-size: 14px;
}

.close-btn {
  background: none;
  border: none;
  font-size: 16px;
  cursor: pointer;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background 0.2s;
}

/* 深色主题下的关闭按钮 */
[data-theme="dark"] .close-btn {
  color: #e0e0e0;
}

/* 浅色主题下的关闭按钮 */
[data-theme="light"] .close-btn {
  color: #333;
}

/* 深色主题下的关闭按钮悬停效果 */
[data-theme="dark"] .close-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

/* 浅色主题下的关闭按钮悬停效果 */
[data-theme="light"] .close-btn:hover {
  background: rgba(0, 0, 0, 0.05);
}

/* 产品网格样式 */
.products-grid-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0rem 1rem;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 20px;
  margin-bottom: 20px;
}

/* 移动端适配 */
@media (max-width: 1200px) {
  .products-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (max-width: 992px) {
  .products-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 768px) {
  .products-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  }

  .products-grid-container {
    padding: 1.5rem 0.5rem;
  }
}

@media (max-width: 480px) {
  .products-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 4px;
    padding: 0;
  }

  .products-grid-container {
    padding: 0rem 0.2rem;
    max-width: 100%;
  }

  .products-two {
    padding: 0.2rem;
  }
}

/* 针对更小屏幕的优化 */
@media (max-width: 360px) {
  .products-grid {
    gap: 3px;
  }

  .products-grid-container {
    padding: 0rem 0.1rem;
  }

  .products-two {
    padding: 0.1rem;
  }
}

/* 加载状态样式 */
.loading-spinner-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  height: 300px;
  border-radius: 16px;
  margin-bottom: 2rem;
  transition: all 0.3s ease;
}

/* 深色主题下的加载容器 */
[data-theme="dark"] .loading-spinner-container {
  background: rgba(20, 15, 40, 0.7);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3), 0 0 20px rgba(120, 70, 200, 0.2);
  border: 1px solid rgba(80, 80, 100, 0.3);
}

/* 浅色主题下的加载容器 */
[data-theme="light"] .loading-spinner-container {
  background: rgba(232, 208, 245, 0.7);
  box-shadow: 0 5px 15px rgba(142, 45, 226, 0.15);
  border: 1px solid rgba(142, 45, 226, 0.1);
}

.loading-spinner {
  width: 60px;
  height: 60px;
  border: 4px solid rgba(120, 70, 200, 0.2);
  border-radius: 50%;
  border-top-color: #7b88ff;
  animation: spin 1.5s linear infinite;
  margin-bottom: 1rem;
}

/* 深色主题下的加载文字 */
[data-theme="dark"] .loading-spinner-container p {
  color: #c3a3ff;
  font-size: 1.2rem;
}

/* 浅色主题下的加载文字 */
[data-theme="light"] .loading-spinner-container p {
  color: #8e2de2;
  font-size: 1.2rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 加载更多按钮 */
.load-more-container {
  display: flex;
  justify-content: center;
  margin: 1.5rem 0 2.5rem;
  min-height: 100px;
  width: 100%;
}

.load-more-btn {
  background: linear-gradient(135deg, #352458, #2d1e4a);
  color: #ffffff;
  border: 2px solid rgba(180, 120, 255, 0.5);
  padding: 1rem 2.5rem;
  border-radius: 50px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3), 0 0 20px rgba(120, 70, 200, 0.3);
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 200px;
}

.load-more-btn:hover {
  background: linear-gradient(135deg, #42306a, #352458);
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4), 0 0 30px rgba(120, 70, 200, 0.5);
}

.load-more-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.small-spinner {
  width: 24px;
  height: 24px;
  border: 3px solid rgba(255, 255, 255, 0.2);
  border-top-color: #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 10px;
}

/* 没有更多产品提示 */
.no-more-products {
  text-align: center;
  padding: 1.5rem;
  font-style: italic;
  border-radius: 15px;
  margin: 1rem auto 2rem;
  max-width: 400px;
  transition: all 0.3s ease;
}

/* 深色主题下的无更多产品提示 */
[data-theme="dark"] .no-more-products {
  color: #c3a3ff;
  background: rgba(30, 30, 35, 0.7);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3), 0 0 20px rgba(120, 70, 200, 0.2);
  border: 1px solid rgba(80, 80, 100, 0.3);
}

/* 浅色主题下的无更多产品提示 */
[data-theme="light"] .no-more-products {
  color: #8e2de2;
  background: rgba(232, 208, 245, 0.7);
  box-shadow: 0 5px 15px rgba(142, 45, 226, 0.15);
  border: 1px solid rgba(142, 45, 226, 0.1);
}

.no-more-icon {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  transition: color 0.3s ease;
}

/* 深色主题下的图标 */
[data-theme="dark"] .no-more-icon {
  color: #8a2be2;
}

/* 浅色主题下的图标 */
[data-theme="light"] .no-more-icon {
  color: #6a1b9a;
}

.cosmic-line {
  width: 80%;
  height: 1px;
  margin: 1rem auto 0;
  transition: background 0.3s ease;
}

/* 深色主题下的分隔线 */
[data-theme="dark"] .cosmic-line {
  background: linear-gradient(90deg, transparent, rgba(138, 43, 226, 0.6), transparent);
}

/* 浅色主题下的分隔线 */
[data-theme="light"] .cosmic-line {
  background: linear-gradient(90deg, transparent, rgba(106, 27, 154, 0.4), transparent);
}

/* 宇宙风格加载器样式 */
.cosmic-loader {
  position: relative;
  width: 80px;
  height: 80px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.cosmic-loader-ring {
  position: absolute;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  border: 3px solid transparent;
  animation: cosmicSpin 1.5s linear infinite;
}

/* 深色主题下的宇宙加载环 */
[data-theme="dark"] .cosmic-loader-ring {
  border-top-color: #9e49ff;
  border-left-color: #4cebff;
  border-right-color: #ff9be7;
  box-shadow: 
    0 0 15px rgba(156, 73, 255, 0.6),
    0 0 30px rgba(76, 235, 255, 0.4),
    0 0 45px rgba(255, 155, 231, 0.3);
}

/* 浅色主题下的宇宙加载环 */
[data-theme="light"] .cosmic-loader-ring {
  border-top-color: #8e2de2;
  border-left-color: #4a00e0;
  border-right-color: #c837ab;
  box-shadow: 
    0 0 15px rgba(142, 45, 226, 0.6),
    0 0 30px rgba(74, 0, 224, 0.4),
    0 0 45px rgba(200, 55, 171, 0.3);
}

.cosmic-loader-stars {
  position: absolute;
  width: 100%;
  height: 100%;
  animation: cosmicRotate 7s linear infinite;
}

.cosmic-star {
  position: absolute;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  opacity: 0.7;
  filter: blur(1px);
  animation: cosmicTwinkle 2s infinite alternate;
}

/* 深色主题下的星星 */
[data-theme="dark"] .cosmic-star {
  background-color: #ffffff;
}

/* 浅色主题下的星星 */
[data-theme="light"] .cosmic-star {
  background-color: #8e2de2;
}

@keyframes cosmicSpin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes cosmicRotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes cosmicTwinkle {
  0% { opacity: 0.2; transform: scale(0.8); }
  100% { opacity: 1; transform: scale(1.2); }
}

/* 错误消息样式 */
.error-message {
  padding: 1.5rem;
  margin: 1rem auto;
  border-radius: 15px;
  text-align: center;
  max-width: 600px;
  transition: all 0.3s ease;
}

/* 深色主题下的错误消息 */
[data-theme="dark"] .error-message {
  background: rgba(150, 0, 0, 0.2);
  color: #ff6b6b;
  border: 1px solid rgba(150, 0, 0, 0.3);
  box-shadow: 0 0 20px rgba(150, 0, 0, 0.2);
}

/* 浅色主题下的错误消息 */
[data-theme="light"] .error-message {
  background: rgba(255, 107, 107, 0.1);
  color: #d32f2f;
  border: 1px solid rgba(211, 47, 47, 0.2);
  box-shadow: 0 0 20px rgba(211, 47, 47, 0.1);
}

.error-message button {
  padding: 0.5rem 1.5rem;
  border-radius: 25px;
  margin-left: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

/* 深色主题下的错误按钮 */
[data-theme="dark"] .error-message button {
  background: linear-gradient(135deg, #352458, #2d1e4a);
  color: #e0e0e0;
  border: 1px solid rgba(120, 70, 200, 0.3);
}

/* 浅色主题下的错误按钮 */
[data-theme="light"] .error-message button {
  background: linear-gradient(135deg, #f5f5f5, #e0e0e0);
  color: #d32f2f;
  border: 1px solid rgba(211, 47, 47, 0.3);
}

/* 深色主题下的错误按钮悬停效果 */
[data-theme="dark"] .error-message button:hover {
  background: linear-gradient(135deg, #42306a, #352458);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(120, 70, 200, 0.4);
  border-color: rgba(120, 70, 200, 0.6);
}

/* 浅色主题下的错误按钮悬停效果 */
[data-theme="light"] .error-message button:hover {
  background: linear-gradient(135deg, #ffffff, #f5f5f5);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(211, 47, 47, 0.2);
  border-color: rgba(211, 47, 47, 0.5);
}

@media (max-width: 480px) {
  .filter-options {
    grid-gap: 10px; /* 略微增加间距，确保按钮之间不会太挤 */
    padding: 0; /* 移除内边距 */
  }

  .filter-option {
    height: 42px; /* 稍微减小高度 */
    font-size: 14px; /* 减小字体大小 */
    padding: 0 8px; /* 减小水平内边距 */
    border-radius: 8px;
  }
}

/* ================ PC端分类过滤器样式 ================ */
.category-filter {
  display: flex;
  align-items: center;
  justify-content: center;
  max-width: 1400px;
  margin: 0 auto 2rem;
  padding: 0 1rem;
  position: relative;
}

.filter-chips-container {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
}

.filter-chips {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
}

.category-dropdown-wrapper {
  position: relative;
  display: inline-block;
}

.category-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  min-width: 200px;
  max-width: 300px;
  margin-top: 0;
}

.category-dropdown .brands-dropdown {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  margin-top: 0;
  padding-top: 8px; /* 添加顶部内边距，因为移除了头部 */
}

.filter-chip {
  padding: 8px 12px 8px 20px;
  border-radius: 25px;
  transition: all 0.3s ease;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
  position: relative;
  justify-content: space-between;
}

/* 分类主要内容区域（可点击进入分类） */
.category-main {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  flex: 1;
  padding: 0;
}

/* 下拉箭头区域（可点击显示下拉框） */
.dropdown-arrow {
  font-size: 12px;
  transition: transform 0.3s ease;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 24px;
  height: 24px;
}

.dropdown-arrow:hover {
  background: rgba(255, 255, 255, 0.1);
}

.dropdown-arrow.rotated {
  transform: rotate(180deg);
  background: rgba(255, 255, 255, 0.15);
}

/* 深色主题下的分类标签 */
[data-theme="dark"] .filter-chip {
  background: rgba(60, 40, 90, 0.6);
  border: 1px solid rgba(120, 70, 200, 0.3);
  color: #e0e0e0;
}

[data-theme="dark"] .filter-chip:hover {
  background: rgba(90, 51, 160, 0.7);
  border-color: rgba(142, 45, 226, 0.8);
}

[data-theme="dark"] .filter-chip.active {
  background: rgba(142, 45, 226, 0.8);
  border-color: rgba(142, 45, 226, 1);
  color: white;
}

/* 浅色主题下的分类标签 */
[data-theme="light"] .filter-chip {
  background: rgba(232, 208, 245, 0.6);
  border: 1px solid rgba(142, 45, 226, 0.3);
  color: #2d3748;
}

[data-theme="light"] .filter-chip:hover {
  background: rgba(232, 208, 245, 0.8);
  border-color: rgba(142, 45, 226, 0.6);
}

[data-theme="light"] .filter-chip.active {
  background: rgba(142, 45, 226, 0.8);
  border-color: rgba(142, 45, 226, 1);
  color: white;
}

/* 悬停状态样式 - 深色主题 */
[data-theme="dark"] .filter-chip.hover {
  background: rgba(120, 70, 200, 0.6);
  border-color: rgba(142, 45, 226, 0.6);
  color: #ffffff;
}

/* 悬停状态样式 - 浅色主题 */
[data-theme="light"] .filter-chip.hover {
  background: rgba(142, 45, 226, 0.6);
  border-color: rgba(142, 45, 226, 0.8);
  color: white;
}

/* 悬停状态下的箭头旋转 */
.filter-chip.hover .dropdown-arrow {
  transform: rotate(180deg);
  opacity: 1;
}

.dropdown-arrow {
  font-size: 12px;
  transition: transform 0.3s ease;
}

.filter-chip.active .dropdown-arrow {
  transform: rotate(180deg);
}

/* 分类图标样式 */
.category-icon {
  margin-right: 8px;
  font-size: 16px;
  opacity: 0.8;
  transition: all 0.3s ease;
}

.filter-chip:hover .category-icon {
  opacity: 1;
  transform: scale(1.1);
}

.filter-chip.active .category-icon {
  opacity: 1;
  color: #ffffff;
}

/* ================ TrendingProducts 移动端样式 ================ */
/* 移动端分类和过滤按钮 */
.mobile-filter-category {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 0 5px;
  position: relative;
  z-index: 10;
}

/* 移动端CATEGORY按钮 - 深色主题 */
[data-theme="dark"] .category-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(30, 30, 45, 0.8);
  border: 1px solid rgba(90, 51, 160, 0.5);
  border-radius: 30px;
  color: white;
  padding: 12px 20px;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  flex: 1;
  margin-left: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  text-align: center;
  letter-spacing: 0.5px;
  font-weight: 500;
}

/* 移动端CATEGORY按钮 - 浅色主题 */
[data-theme="light"] .category-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(232, 208, 245, 0.8);
  border: 1px solid rgba(142, 45, 226, 0.5);
  border-radius: 30px;
  color: #2d3748;
  padding: 12px 20px;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  flex: 1;
  margin-left: 10px;
  box-shadow: 0 2px 8px rgba(142, 45, 226, 0.15);
  text-align: center;
  letter-spacing: 0.5px;
  font-weight: 500;
}

/* 移动端CATEGORY按钮悬停效果 - 深色主题 */
[data-theme="dark"] .category-btn:hover {
  background-color: rgba(90, 51, 160, 0.7);
  border-color: rgba(142, 45, 226, 0.8);
}

/* 移动端CATEGORY按钮悬停效果 - 浅色主题 */
[data-theme="light"] .category-btn:hover {
  background-color: rgba(232, 208, 245, 1);
  border-color: rgba(142, 45, 226, 0.8);
  color: #1a202c;
}

.category-btn:active {
  transform: scale(0.98);
}

.category-btn svg {
  margin-right: 10px;
  width: 18px;
  height: 18px;
  display: none; /* 隐藏SVG图标，与截图一致 */
}

/* 移动端筛选图标 - 深色主题 */
[data-theme="dark"] .mobile-filter-category .filter-icon {
  width: 48px;
  height: 48px;
  min-width: 48px;
  background-color: #8e2de2;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(142, 45, 226, 0.3);
}

/* 移动端筛选图标 - 浅色主题 */
[data-theme="light"] .mobile-filter-category .filter-icon {
  width: 48px;
  height: 48px;
  min-width: 48px;
  background-color: rgba(232, 208, 245, 0.9);
  border: 1px solid rgba(142, 45, 226, 0.4);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(142, 45, 226, 0.2);
}

/* 移动端筛选图标SVG - 深色主题 */
[data-theme="dark"] .mobile-filter-category .filter-icon svg {
  width: 24px;
  height: 24px;
  color: white;
}

/* 移动端筛选图标SVG - 浅色主题 */
[data-theme="light"] .mobile-filter-category .filter-icon svg {
  width: 24px;
  height: 24px;
  color: #8e2de2;
}

/* 移动端过滤背景遮罩 */
.mobile-filter-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  animation: backdropFadeIn 0.2s ease;
}

/* 移动端过滤下拉框 - 深色主题（更新为新的定位方式） */
[data-theme="dark"] .mobile-filter-dropdown {
  background: rgba(43, 32, 73, 0.98);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
}

/* 移动端过滤下拉框 - 浅色主题（更新为新的定位方式） */
[data-theme="light"] .mobile-filter-dropdown {
  background: rgba(232, 208, 245, 0.98);
  box-shadow: 0 8px 20px rgba(142, 45, 226, 0.15);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

/* 移动端过滤选项网格 */
.mobile-filter-dropdown .filter-options {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: repeat(3, 1fr);
  gap: 8px;
  padding: 0;
}

/* 移动端筛选选项 - 深色主题 */
[data-theme="dark"] .mobile-filter-dropdown .filter-option {
  background: rgba(60, 40, 90, 0.6);
  border: 1px solid rgba(120, 70, 200, 0.3);
  border-radius: 30px;
  color: #e0e0e0;
  padding: 0;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  min-height: 36px;
  pointer-events: auto;
  position: relative;
  z-index: 1;
}

/* 移动端筛选选项 - 浅色主题 */
[data-theme="light"] .mobile-filter-dropdown .filter-option {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(142, 45, 226, 0.3);
  border-radius: 8px;
  color: #2d3748;
  padding: 0px 12px;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  min-height: 36px;
  pointer-events: auto;
  position: relative;
  z-index: 1;
}

/* 移动端筛选选项悬停效果 - 深色主题 */
[data-theme="dark"] .mobile-filter-dropdown .filter-option:hover {
  background: rgba(90, 51, 160, 0.7);
  border-color: rgba(142, 45, 226, 0.8);
}

/* 移动端筛选选项悬停效果 - 浅色主题 */
[data-theme="light"] .mobile-filter-dropdown .filter-option:hover {
  background: rgba(232, 208, 245, 0.8);
  border-color: rgba(142, 45, 226, 0.6);
  color: #1a202c;
}

/* 移动端筛选选项激活状态 - 深色主题 */
[data-theme="dark"] .mobile-filter-dropdown .filter-option.active {
  background: rgba(142, 45, 226, 0.8);
  border-color: rgba(142, 45, 226, 1);
  color: white;
}

/* 移动端筛选选项激活状态 - 浅色主题 */
[data-theme="light"] .mobile-filter-dropdown .filter-option.active {
  background: rgba(142, 45, 226, 0.9);
  border-color: rgba(142, 45, 226, 1);
  color: white;
}

.mobile-filter-dropdown .seller-option {
  grid-column: 1;
  grid-row: 3;
  justify-content: center;
  padding-left: 0;
  padding-right: 0;
}



/* ================ 全屏分类菜单样式 ================ */
/* 全屏分类菜单 - 深色主题 */
[data-theme="dark"] .category-fullscreen {
  position: fixed;
  top: auto;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #462b66;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  height: 50vh;
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
  box-shadow: 0 -5px 25px rgba(0, 0, 0, 0.3);
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

/* 全屏分类菜单 - 浅色主题 */
[data-theme="light"] .category-fullscreen {
  position: fixed;
  top: auto;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #f8fafc;
  border: 1px solid rgba(142, 45, 226, 0.2);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  height: 50vh;
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
  box-shadow: 0 -5px 25px rgba(142, 45, 226, 0.15);
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.category-fullscreen.show {
  transform: translateY(0);
}

@media (min-width: 481px) {
  .category-fullscreen {
    display: none;
  }
}

/* 分类菜单头部 - 深色主题 */
[data-theme="dark"] .category-fullscreen-header {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background-color: #462b66;
  position: relative;
}

/* 分类菜单头部 - 浅色主题 */
[data-theme="light"] .category-fullscreen-header {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid rgba(142, 45, 226, 0.2);
  background-color: rgba(232, 208, 245, 0.9);
  position: relative;
}

/* 分类菜单标题 - 深色主题 */
[data-theme="dark"] .category-fullscreen-header h2 {
  font-size: 20px;
  color: #ffffff;
  font-weight: 500;
  margin: 0;
  text-align: center;
}

/* 分类菜单标题 - 浅色主题 */
[data-theme="light"] .category-fullscreen-header h2 {
  font-size: 20px;
  color: #2d3748;
  font-weight: 500;
  margin: 0;
  text-align: center;
}

/* 关闭按钮 - 深色主题 */
[data-theme="dark"] .close-btn {
  background: none;
  border: none;
  color: white;
  padding: 5px;
  cursor: pointer;
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
}

/* 关闭按钮 - 浅色主题 */
[data-theme="light"] .close-btn {
  background: none;
  border: none;
  color: #2d3748;
  padding: 5px;
  cursor: pointer;
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
}

.category-fullscreen-content {
  display: flex;
  height: calc(100% - 50px);
  padding: 0;
  max-width: 100%;
  overflow: hidden;
}

/* 分类侧边栏 - 深色主题 */
[data-theme="dark"] .category-sidebar {
  width: 35%;
  background-color: #3b2556;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 0;
  margin: 0;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

/* 分类侧边栏 - 浅色主题 */
[data-theme="light"] .category-sidebar {
  width: 35%;
  background-color: rgba(232, 208, 245, 0.6);
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 0;
  margin: 0;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

/* 分类内容区域 - 深色主题 */
[data-theme="dark"] .category-content {
  width: 65%;
  padding: 10px;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  background-color: #462b66;
}

/* 分类内容区域 - 浅色主题 */
[data-theme="light"] .category-content {
  width: 65%;
  padding: 10px;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  background-color: #f8fafc;
}

.category-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
  padding: 5px;
}

/* 分类侧边栏项目 - 深色主题 */
[data-theme="dark"] .category-sidebar-item {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 15px;
  transition: background-color 0.3s ease;
  cursor: pointer;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  position: relative;
}

/* 分类侧边栏项目 - 浅色主题 */
[data-theme="light"] .category-sidebar-item {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 15px;
  transition: background-color 0.3s ease;
  cursor: pointer;
  border-bottom: 1px solid rgba(142, 45, 226, 0.1);
  position: relative;
}

/* 分类侧边栏激活项目 - 深色主题 */
[data-theme="dark"] .category-sidebar-item.active {
  background-color: #462b66;
}

/* 分类侧边栏激活项目 - 浅色主题 */
[data-theme="light"] .category-sidebar-item.active {
  background-color: rgba(142, 45, 226, 0.2);
}

/* 分类侧边栏文本 - 深色主题 */
[data-theme="dark"] .category-sidebar-item span {
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
}

/* 分类侧边栏文本 - 浅色主题 */
[data-theme="light"] .category-sidebar-item span {
  color: #2d3748;
  font-size: 14px;
  font-weight: 500;
}

/* 选中图标 - 深色主题 */
[data-theme="dark"] .check-icon {
  position: absolute;
  left: 15px;
  color: #ffffff;
  font-size: 16px;
  font-weight: bold;
}

/* 选中图标 - 浅色主题 */
[data-theme="light"] .check-icon {
  position: absolute;
  left: 15px;
  color: #8e2de2;
  font-size: 16px;
  font-weight: bold;
}

.category-sidebar-item.active span,
.category-sidebar-item span.active-text {
  margin-left: 3px;
}

/* 分类侧边栏内容布局 */
.category-item-content {
  display: flex;
  align-items: center;
  flex: 1;
}

/* 分类侧边栏图标样式 */
.category-sidebar-icon {
  margin-right: 12px;
  font-size: 18px;
  opacity: 0.8;
  transition: all 0.3s ease;
  min-width: 20px;
  text-align: center;
}

/* 深色主题下的侧边栏图标 */
[data-theme="dark"] .category-sidebar-icon {
  color: #e0e0e0;
}

/* 浅色主题下的侧边栏图标 */
[data-theme="light"] .category-sidebar-icon {
  color: #2d3748;
}

/* 激活状态下的图标 */
.category-sidebar-item.active .category-sidebar-icon {
  opacity: 1;
  color: #8e2de2;
  transform: scale(1.1);
}

/* 分类卡片 - 深色主题 */
[data-theme="dark"] .category-card {
  width: 100%;
  aspect-ratio: 1;
  background-color: #5d3c85;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: transform 0.2s ease;
}

/* 分类卡片 - 浅色主题 */
[data-theme="light"] .category-card {
  width: 100%;
  aspect-ratio: 1;
  background-color: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(142, 45, 226, 0.3);
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: transform 0.2s ease;
  box-shadow: 0 2px 8px rgba(142, 45, 226, 0.1);
}

.category-card:hover {
  transform: scale(1.05);
}

.category-image {
  width: 60%;
  height: 60%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.category-image img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  opacity: 0.7;
}

/* 分类卡片名称 - 深色主题 */
[data-theme="dark"] .category-name {
  padding: 10px 5px;
  color: #ffffff;
  font-size: 14px;
  text-align: center;
  font-weight: 500;
}

/* 分类卡片名称 - 浅色主题 */
[data-theme="light"] .category-name {
  padding: 10px 5px;
  color: #2d3748;
  font-size: 14px;
  text-align: center;
  font-weight: 500;
}

/* ================ Merchant Selection Modal Styles ================ */
.sellers-modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease;
}

.sellers-modal {
  width: 90%;
  max-width: 800px;
  max-height: 80vh;
  border-radius: 16px;
  overflow: hidden;
  animation: slideUp 0.3s ease;
  /* 默认浅色主题 */
  background: linear-gradient(135deg, #ffffff, #f8f9fa);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* Dark theme modal */
[data-theme="dark"] .sellers-modal {
  background: linear-gradient(135deg, #2a1f3b, #1e1a2e);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
}

.sellers-modal-header {
  padding: 1.5rem 2rem;
  border-bottom: 1px solid rgba(139, 92, 246, 0.2);
  display: flex;
  align-items: center;
  position: relative;
  /* 默认浅色主题 */
  background: linear-gradient(135deg, #8b5cf6, #a855f7);
}

/* Dark theme header */
[data-theme="dark"] .sellers-modal-header {
  background: linear-gradient(135deg, #352458, #2d1e4a);
  border-bottom: 1px solid rgba(120, 70, 200, 0.3);
}

.sellers-modal-header h3 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

.close-btn {
  background: none;
  border: none;
  font-size: 2rem;
  color: white;
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

/* 左上角关闭按钮特定样式 */
.close-btn-left {
  position: relative;
  margin-right: auto;
}

.sellers-modal-content {
  padding: 2rem;
  max-height: 60vh;
  overflow-y: auto;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  /* 默认浅色主题 */
  color: #6366f1;
}

/* Dark theme loading container */
[data-theme="dark"] .loading-container {
  color: #c3a3ff;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(139, 92, 246, 0.2);
  border-top: 4px solid #8b5cf6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

/* Dark theme loading spinner */
[data-theme="dark"] .loading-spinner {
  border: 4px solid rgba(195, 163, 255, 0.2);
  border-top: 4px solid #c3a3ff;
}

.no-sellers {
  text-align: center;
  padding: 3rem;
  /* 默认浅色主题 */
  color: #6b7280;
}

/* Dark theme no data message */
[data-theme="dark"] .no-sellers {
  color: #9ca3af;
}

.sellers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1rem;
}

.seller-item {
  display: flex;
  align-items: center;
  padding: 1rem;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  /* 默认浅色主题 */
  background: rgba(248, 250, 252, 0.8);
  border-color: rgba(139, 92, 246, 0.2);
}

/* Dark theme seller item */
[data-theme="dark"] .seller-item {
  background: rgba(30, 30, 40, 0.6);
  border-color: rgba(120, 70, 200, 0.3);
}

.seller-item:hover {
  transform: translateY(-2px);
  /* 默认浅色主题悬停 */
  background: rgba(139, 92, 246, 0.1);
  border-color: rgba(139, 92, 246, 0.4);
  box-shadow: 0 4px 15px rgba(139, 92, 246, 0.2);
}

/* Dark theme hover */
[data-theme="dark"] .seller-item:hover {
  background: rgba(45, 35, 65, 0.8);
  border-color: rgba(120, 70, 200, 0.6);
  box-shadow: 0 4px 15px rgba(120, 70, 200, 0.3);
}

.seller-item.selected {
  /* 默认浅色主题选中 */
  background: rgba(139, 92, 246, 0.2);
  border-color: #8b5cf6;
  box-shadow: 0 4px 15px rgba(139, 92, 246, 0.3);
}

/* Dark theme selected */
[data-theme="dark"] .seller-item.selected {
  background: rgba(120, 70, 200, 0.3);
  border-color: #c3a3ff;
  box-shadow: 0 4px 15px rgba(195, 163, 255, 0.4);
}

.seller-logo {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  overflow: hidden;
  margin-right: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  /* 默认浅色主题 */
  background: linear-gradient(135deg, #8b5cf6, #a855f7);
}

/* Dark theme merchant logo */
[data-theme="dark"] .seller-logo {
  background: linear-gradient(135deg, #352458, #2d1e4a);
}

.seller-logo.all-sellers {
  /* 默认浅色主题全部商家图标 */
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
}

/* Dark theme all merchants icon */
[data-theme="dark"] .seller-logo.all-sellers {
  background: linear-gradient(135deg, #4c1d95, #5b21b6);
}

.seller-logo img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.seller-logo i {
  font-size: 1.5rem;
  color: white;
}

.seller-initials {
  font-size: 1.2rem;
  font-weight: bold;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.seller-info {
  flex: 1;
}

.seller-info h4 {
  margin: 0 0 0.25rem 0;
  font-size: 1rem;
  font-weight: 600;
  /* 默认浅色主题 */
  color: #1f2937;
}

/* Dark theme merchant name */
[data-theme="dark"] .seller-info h4 {
  color: #ffffff;
}

.seller-info p {
  margin: 0;
  font-size: 0.875rem;
  /* 默认浅色主题 */
  color: #6b7280;
}

/* Dark theme merchant description */
[data-theme="dark"] .seller-info p {
  color: #9ca3af;
}

.seller-id {
  font-size: 0.75rem !important;
  opacity: 0.8;
  font-style: italic;
}

/* Animation effects */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Mobile responsive */
@media (max-width: 768px) {
  .sellers-modal {
    width: 95%;
    max-height: 85vh;
  }

  .sellers-modal-header {
    padding: 1rem 1.5rem;
  }

  .sellers-modal-header h3 {
    font-size: 1.25rem;
  }

  .sellers-modal-content {
    padding: 1rem;
  }

  .sellers-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .seller-item {
    padding: 0.75rem;
  }

  .seller-logo {
    width: 40px;
    height: 40px;
    margin-right: 0.75rem;
  }

  .seller-logo i {
    font-size: 1.25rem;
  }

  .seller-initials {
    font-size: 1rem;
  }
}

/* 关闭按钮样式 - PC端专用 */
:not(.mobile-dropdown) .close-btn {
  background: rgba(142, 45, 226, 0.1);
  border: 1px solid rgba(142, 45, 226, 0.2);
  font-size: 14px;
  cursor: pointer;
  width: 26px;
  height: 26px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: none !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transform: none !important;
}

/* 深色主题下的PC端关闭按钮 */
[data-theme="dark"] :not(.mobile-dropdown) .close-btn {
  color: #ffffff;
  background: rgba(90, 51, 160, 0.7);
  border: 1px solid rgba(142, 45, 226, 0.5);
}

/* 浅色主题下的PC端关闭按钮 */
[data-theme="light"] :not(.mobile-dropdown) .close-btn {
  color: #8e2de2;
  background: rgba(232, 208, 245, 0.7);
  border: 1px solid rgba(142, 45, 226, 0.3);
}

/* 移除PC端关闭按钮悬停效果 */
:not(.mobile-dropdown) .close-btn:hover,
:not(.mobile-dropdown) .close-btn:active,
:not(.mobile-dropdown) .close-btn:focus {
  background: rgba(142, 45, 226, 0.1);
  transform: none !important;
  scale: none !important;
  translate: none !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

/* 深色主题下移除PC端关闭按钮悬停效果 */
[data-theme="dark"] :not(.mobile-dropdown) .close-btn:hover,
[data-theme="dark"] :not(.mobile-dropdown) .close-btn:active,
[data-theme="dark"] :not(.mobile-dropdown) .close-btn:focus {
  background: rgba(90, 51, 160, 0.7);
  transform: none !important;
  scale: none !important;
  translate: none !important;
}

/* 浅色主题下移除PC端关闭按钮悬停效果 */
[data-theme="light"] :not(.mobile-dropdown) .close-btn:hover,
[data-theme="light"] :not(.mobile-dropdown) .close-btn:active,
[data-theme="light"] :not(.mobile-dropdown) .close-btn:focus {
  background: rgba(232, 208, 245, 0.7);
  transform: none !important;
  scale: none !important;
  translate: none !important;
}

/* 覆盖所有按钮变换效果 */
.brands-dropdown .close-btn:active,
.brands-dropdown:not(.mobile-dropdown) .close-btn:active {
  transform: none !important;
  scale: none !important;
  translate: none !important;
}

/* 新增样式 - PC端产品分类区域 */
.pc-categories-section {
  margin-bottom: 0.8rem;
  padding: 0.8rem 1rem;
  max-width: 1400px;
  margin-left: auto;
  margin-right: auto;
}

/* PC端排序筛选区域 */
.pc-sorting-section {
  margin-bottom: 1.5rem;
  padding: 0.5rem 0;
  max-width: 1400px;
  margin-left: auto;
  margin-right: auto;
}

.sorting-controls {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  padding: 0 1rem;
  justify-content: flex-start;
}

/* Sort by 文字样式（简单样式，不是按钮） */
.sort-by-text {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  color: #e0e0e0;
  font-size: 0.95rem;
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative; /* 添加相对定位，让弹出层相对于此元素定位 */
  padding: 0.5rem 0.8rem;
  border-radius: 8px;
  border: 1px solid transparent;
}

.sort-by-text:hover {
  color: #c3a3ff;
  background: rgba(195, 163, 255, 0.1);
}

/* Sort by 按钮选中状态 */
.sort-by-text.active {
  color: #ffffff;
  background: linear-gradient(135deg, #8b5cf6, #a855f7);
  border: 1px solid rgba(139, 92, 246, 0.5);
  box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3);
}

.sort-by-text.active:hover {
  background: linear-gradient(135deg, #7c3aed, #9333ea);
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.4);
}

.sort-by-text svg {
  transition: transform 0.3s ease;
}

.sort-by-text svg.rotate {
  transform: rotate(180deg);
}

/* 新增筛选控制区域 */
.new-filter-controls {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

/* 筛选文字按钮样式（简单样式） */
.filter-text-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  color: #c3a3ff;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.3s ease;
  padding: 0.5rem 0.8rem;
  border-radius: 8px;
  border: 1px solid transparent;
}

.filter-text-btn:hover {
  color: #e0d0ff;
  background: rgba(195, 163, 255, 0.1);
}

/* 筛选按钮选中状态 */
.filter-text-btn.active {
  color: #ffffff;
  background: linear-gradient(135deg, #8b5cf6, #a855f7);
  border: 1px solid rgba(139, 92, 246, 0.5);
  box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3);
}

.filter-text-btn.active:hover {
  background: linear-gradient(135deg, #7c3aed, #9333ea);
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.4);
}

/* 筛选按钮箭头旋转效果 */
.filter-text-btn svg {
  transition: transform 0.3s ease;
}

.filter-text-btn svg.rotate {
  transform: rotate(180deg);
}

/* 移动端排序布局 */
.mobile-sorting-layout {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.8rem 0.1rem; /* 减少左右内边距，给按钮更多空间 */
  margin: 0 1rem 0rem 1rem;
  gap: 0.5rem; /* 添加按钮之间的间距 */
  /* 移除外层黑色框框 */
}

/* 移动端 Sort by 按钮 */
.mobile-sort-by {
  display: flex;
  align-items: center;
  gap: 0.3rem;
  padding: 0.6rem 0.8rem; /* 减少左右内边距 */
  background: linear-gradient(135deg, rgba(138, 43, 226, 0.2), rgba(195, 163, 255, 0.1));
  border: 2px solid rgba(195, 163, 255, 0.6);
  border-radius: 20px;
  color: #e0d0ff;
  font-weight: 600;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative; /* 添加相对定位，让弹出层相对于此元素定位 */
  flex-shrink: 0; /* 防止按钮被压缩 */
  min-width: auto; /* 允许按钮自适应内容宽度 */
}

.mobile-sort-by:hover {
  background: linear-gradient(135deg, rgba(138, 43, 226, 0.3), rgba(195, 163, 255, 0.15));
  border-color: rgba(195, 163, 255, 0.8);
  transform: translateY(-2px);
}

.mobile-sort-by svg {
  transition: transform 0.3s ease;
}

.mobile-sort-by svg.rotate {
  transform: rotate(180deg);
}

/* 移动端 Category 按钮 */
.mobile-category-btn {
  flex: 2; /* 增加flex值，让按钮更宽 */
  margin: 0 0.6rem; /* 减少左右边距 */
  padding: 0.6rem 2rem; /* 增加左右内边距 */
  background: rgba(120, 70, 200, 0.2);
  border: 1px solid rgba(120, 70, 200, 0.4);
  border-radius: 20px;
  color: #c3a3ff;
  font-weight: 600;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center; /* 确保文字居中 */
  min-width: 0; /* 允许flex正常工作 */
}

.mobile-category-btn:hover {
  background: rgba(120, 70, 200, 0.3);
  transform: translateY(-1px);
}

/* 移动端筛选器切换按钮 */
.mobile-filter-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 38px; /* 稍微减小宽度 */
  height: 38px; /* 稍微减小高度 */
  background: linear-gradient(135deg, rgba(195, 163, 255, 0.1), rgba(138, 43, 226, 0.05));
  border: 2px solid rgba(195, 163, 255, 0.6);
  border-radius: 50%;
  color: #c3a3ff;
  cursor: pointer;
  transition: all 0.3s ease;
  flex-shrink: 0; /* 防止按钮被压缩 */
}

.mobile-filter-toggle:hover {
  background: linear-gradient(135deg, rgba(195, 163, 255, 0.2), rgba(138, 43, 226, 0.1));
  border-color: rgba(195, 163, 255, 0.8);
  transform: translateY(-2px);
}

/* 移动端新筛选按钮区域 */
.mobile-new-filters {
  display: flex;
  flex-direction: row; /* 水平排列 */
  gap: 0.5rem; /* 按钮之间的间距 */
  padding: 0.8rem 0.5rem; /* 调整内边距 */
  margin: 0 1rem 1rem 1rem; /* 与上方布局保持一致的外边距 */
  background: transparent; /* 移除背景 */
  border: none; /* 移除边框 */
  animation: slideDown 0.3s ease-out;
  justify-content: space-between; /* 均匀分布按钮 */
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 移动端筛选按钮 */
.mobile-filter-btn {
  display: flex;
  align-items: center;
  justify-content: center; /* 居中对齐 */
  gap: 0.3rem; /* 文字和图标之间的间距 */
  flex: 1; /* 平均分配宽度 */
  padding: 0.6rem 0.8rem; /* 减少内边距适应一行显示 */
  background: linear-gradient(135deg, rgba(195, 163, 255, 0.05), rgba(138, 43, 226, 0.02));
  border: 1px solid rgba(195, 163, 255, 0.3);
  border-radius: 20px;
  color: #c3a3ff;
  font-weight: 500;
  font-size: 0.85rem; /* 稍微减小字体 */
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 0; /* 允许flex正常工作 */
  text-align: center; /* 文字居中 */
}

.mobile-filter-btn:hover {
  background: linear-gradient(135deg, rgba(195, 163, 255, 0.1), rgba(138, 43, 226, 0.05));
  border-color: rgba(195, 163, 255, 0.5);
  transform: translateY(-1px);
}

/* 移动端筛选下拉框样式 */
.mobile-filter-dropdown {
  position: fixed !important; /* 强制使用 fixed 定位 */
  top: 120px; /* 默认位置，会被 JavaScript 覆盖 */
  left: 1rem; /* 默认左边距，会被 JavaScript 覆盖 */
  width: 300px; /* 固定宽度 */
  max-width: calc(100vw - 2rem); /* 响应式最大宽度 */
  border-radius: 12px;
  z-index: 10000 !important; /* 调整 z-index 值，确保在其他元素之上但不会过高 */
  padding: 14px;
  animation: fadeIn 0.2s ease;
  backdrop-filter: blur(5px);
  margin-top: 0; /* 移除margin，使用JavaScript精确定位 */
  background: rgba(43, 32, 73, 0.98);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
  pointer-events: auto !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* 深色主题下的移动端筛选下拉框（已在上面定义，避免重复） */

/* 浅色主题下的移动端筛选下拉框（已在上面定义，避免重复） */

/* ================ 价格筛选弹出层样式 ================ */

/* 价格筛选弹出层 */
.price-filter-dropdown {
  position: absolute; /* 使用 absolute 定位，允许 JavaScript 动态调整 */
  top: 120px; /* 默认位置，会被 JavaScript 精确覆盖 */
  left: 1rem;
  width: 340px; /* 增加宽度以容纳输入框 */
  max-width: calc(100vw - 2rem);
  border-radius: 12px;
  z-index: 10000 !important;
  padding: 16px; /* 增加内边距 */
  animation: fadeIn 0.2s ease;
  backdrop-filter: blur(5px);
  background: rgba(43, 32, 73, 0.98);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
  pointer-events: auto !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* 价格筛选选项容器 */
.price-filter-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* 价格区间按钮网格 */
.price-ranges {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

/* 价格区间按钮 */
.price-range-btn {
  background: rgba(60, 40, 90, 0.6);
  border: 1px solid rgba(120, 70, 200, 0.3);
  border-radius: 20px;
  color: #e0e0e0;
  padding: 8px 16px;
  font-size: 0.85rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.price-range-btn:hover {
  background: rgba(90, 51, 160, 0.7);
  border-color: rgba(142, 45, 226, 0.8);
}

.price-range-btn.active {
  background: rgba(142, 45, 226, 0.8);
  border-color: rgba(142, 45, 226, 1);
  color: white;
}

/* 自定义价格输入区域 */
.custom-price-inputs {
  margin-top: 8px;
}

.price-input-group {
  display: flex;
  gap: 6px; /* 减小间距 */
}

.price-input {
  flex: 1;
  background: rgba(60, 40, 90, 0.6);
  border: 1px solid rgba(120, 70, 200, 0.3);
  border-radius: 15px; /* 减小圆角 */
  color: #e0e0e0;
  padding: 6px 8px; /* 减小内边距 */
  font-size: 0.8rem; /* 减小字体 */
  text-align: center;
  min-width: 0; /* 确保flex正常工作 */
  width: 100%; /* 确保不超出容器 */
  box-sizing: border-box; /* 包含边框和内边距在宽度内 */
}

.price-input::placeholder {
  color: rgba(224, 224, 224, 0.6);
}

.price-input:focus {
  outline: none;
  border-color: rgba(142, 45, 226, 0.8);
  background: rgba(90, 51, 160, 0.7);
}

/* 操作按钮 */
.price-actions {
  margin-top: 8px;
  display: flex;
  gap: 8px;
  justify-content: center;
}

.apply-price-btn {
  background: rgba(142, 45, 226, 0.8);
  border: 1px solid rgba(142, 45, 226, 1);
  border-radius: 20px;
  color: white;
  padding: 6px 16px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.apply-price-btn:hover {
  background: rgba(142, 45, 226, 1);
  transform: translateY(-1px);
}

.reset-price-btn {
  background: transparent;
  border: 1px solid rgba(120, 70, 200, 0.4);
  border-radius: 20px;
  color: #c3a3ff;
  padding: 6px 16px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.reset-price-btn:hover {
  background: rgba(120, 70, 200, 0.2);
  border-color: rgba(142, 45, 226, 0.6);
}

/* 浅色主题下的价格筛选弹出层 */
[data-theme="light"] .price-filter-dropdown {
  background: rgba(232, 208, 245, 0.98);
  box-shadow: 0 8px 20px rgba(142, 45, 226, 0.15);
}

[data-theme="light"] .price-range-btn {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(142, 45, 226, 0.3);
  color: #2d3748;
}

[data-theme="light"] .price-range-btn:hover {
  background: rgba(232, 208, 245, 0.8);
  border-color: rgba(142, 45, 226, 0.6);
}

[data-theme="light"] .price-range-btn.active {
  background: rgba(142, 45, 226, 0.9);
  border-color: rgba(142, 45, 226, 1);
  color: white;
}

[data-theme="light"] .price-input {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(142, 45, 226, 0.3);
  color: #2d3748;
  padding: 6px 8px; /* 与深色主题保持一致 */
  font-size: 0.8rem; /* 与深色主题保持一致 */
  border-radius: 15px; /* 与深色主题保持一致 */
}

[data-theme="light"] .price-input::placeholder {
  color: rgba(45, 55, 72, 0.6);
}

[data-theme="light"] .price-input:focus {
  border-color: rgba(142, 45, 226, 0.8);
  background: rgba(232, 208, 245, 0.8);
}

[data-theme="light"] .apply-price-btn {
  background: rgba(142, 45, 226, 0.9);
  border: 1px solid rgba(142, 45, 226, 1);
  color: white;
}

[data-theme="light"] .apply-price-btn:hover {
  background: rgba(142, 45, 226, 1);
}

[data-theme="light"] .reset-price-btn {
  border: 1px solid rgba(142, 45, 226, 0.4);
  color: #5a4fcf;
}

[data-theme="light"] .reset-price-btn:hover {
  background: rgba(232, 208, 245, 0.6);
  border-color: rgba(142, 45, 226, 0.6);
}

/* 移动端价格筛选弹出层专用样式 */
@media (max-width: 768px) {
  .price-filter-dropdown {
    position: fixed !important; /* 移动端强制使用 fixed 定位 */
    z-index: 2147483647 !important;
    pointer-events: auto !important;
    visibility: visible !important;
    opacity: 1 !important;
  }
}

/* PC端价格筛选弹出层专用样式 */
@media (min-width: 769px) {
  .price-filter-dropdown {
    position: absolute !important; /* PC端强制使用 absolute 定位 */
  }
}

/* ================ 平台筛选弹出层样式 ================ */

/* 平台筛选弹出层 */
.platform-filter-dropdown {
  position: absolute; /* 使用 absolute 定位，允许 JavaScript 动态调整 */
  top: 120px; /* 默认位置，会被 JavaScript 精确覆盖 */
  left: 1rem;
  width: 280px;
  max-width: calc(100vw - 2rem);
  border-radius: 12px;
  z-index: 2147483647 !important;
  padding: 16px;
  animation: fadeIn 0.2s ease;
  backdrop-filter: blur(5px);
  background: rgba(43, 32, 73, 0.98);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
  pointer-events: auto !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* 平台筛选选项容器 */
.platform-filter-options {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* 平台选项容器 */
.platform-options {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* 平台选项按钮 */
.platform-option-btn {
  display: flex;
  align-items: center;
  gap: 12px;
  background: rgba(60, 40, 90, 0.6);
  border: 1px solid rgba(120, 70, 200, 0.3);
  border-radius: 12px;
  color: #e0e0e0;
  padding: 12px 16px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
  width: 100%;
}

.platform-option-btn:hover {
  background: rgba(90, 51, 160, 0.7);
  border-color: rgba(142, 45, 226, 0.8);
  transform: translateY(-1px);
}

.platform-option-btn.active {
  background: rgba(142, 45, 226, 0.8);
  border-color: rgba(142, 45, 226, 1);
  color: white;
}

/* 平台图标 */
.platform-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  font-size: 1.2rem;
}

.platform-icon .fas,
.platform-icon .fab {
  color: currentColor;
}

/* 平台名称 */
.platform-name {
  flex: 1;
  font-weight: 500;
}

/* 浅色主题下的平台筛选弹出层 */
[data-theme="light"] .platform-filter-dropdown {
  background: rgba(232, 208, 245, 0.98);
  box-shadow: 0 8px 20px rgba(142, 45, 226, 0.15);
}

[data-theme="light"] .platform-option-btn {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(142, 45, 226, 0.3);
  color: #2d3748;
}

[data-theme="light"] .platform-option-btn:hover {
  background: rgba(232, 208, 245, 0.8);
  border-color: rgba(142, 45, 226, 0.6);
}

[data-theme="light"] .platform-option-btn.active {
  background: rgba(142, 45, 226, 0.9);
  border-color: rgba(142, 45, 226, 1);
  color: white;
}

/* 移动端平台筛选弹出层专用样式 */
@media (max-width: 768px) {
  .platform-filter-dropdown {
    position: fixed !important; /* 移动端强制使用 fixed 定位 */
    z-index: 2147483647 !important;
    pointer-events: auto !important;
    visibility: visible !important;
    opacity: 1 !important;
  }
}

/* PC端平台筛选弹出层专用样式 */
@media (min-width: 769px) {
  .platform-filter-dropdown {
    position: absolute !important; /* PC端强制使用 absolute 定位 */
  }
}
</style>