<template>
  <div class="register-page">
    <StarryBackground />
    <div class="register-container">
      <div class="register-header">
        <img src="@/assets/logo1.png" alt="AGTFIND Logo" class="logo">
        <h1>{{ $t('register.createAccount') }}</h1>
      </div>
      
      <div class="register-form">
        <div class="form-group">
          <label for="username">{{ $t('register.username') }} <span class="required">*</span></label>
          <input 
            type="text" 
            id="username" 
            v-model="formData.username" 
            :placeholder="$t('register.usernamePlaceholder')"
            class="form-input"
            :class="{ 'error': errors.username }"
          >
          <span v-if="errors.username" class="error-message">{{ errors.username }}</span>
        </div>
        
        <div class="form-group">
          <label for="email">{{ $t('register.email') }} <span class="required">*</span></label>
          <input 
            type="email" 
            id="email" 
            v-model="formData.email" 
            :placeholder="$t('register.emailPlaceholder')"
            class="form-input"
            :class="{ 'error': errors.email }"
          >
          <span v-if="errors.email" class="error-message">{{ errors.email }}</span>
        </div>
        
        <div class="form-group">
          <label for="password">{{ $t('register.password') }} <span class="required">*</span></label>
          <div class="password-input-container">
            <input 
              :type="showPassword ? 'text' : 'password'" 
              id="password" 
              v-model="formData.password" 
              :placeholder="$t('register.passwordPlaceholder')"
              class="form-input"
              :class="{ 'error': errors.password }"
            >
            <button 
              type="button"
              class="toggle-password"
              @click="showPassword = !showPassword"
            >
              <i :class="showPassword ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
            </button>
          </div>
          <span v-if="errors.password" class="error-message">{{ errors.password }}</span>
          
          <div class="password-strength-el" v-if="formData.password">
            <div class="strength-header">
              <div class="strength-label">{{ $t('register.passwordStrength') }}</div>
              <div class="strength-text" :class="passwordStrength.class">{{ $t(`register.${passwordStrength.key}`) }}</div>
            </div>
            <el-progress 
              :percentage="passwordStrength.percent"
              :status="passwordStrengthStatus"
              :stroke-width="10"
              :show-text="false"
              :color="passwordStrengthColor"
            ></el-progress>
            <div class="password-tips">
              <el-tag v-if="formData.password" size="small" :type="hasLowercase ? 'success' : 'info'" class="strength-tag">
                <i :class="hasLowercase ? 'el-icon-check' : 'el-icon-close'"></i> {{ $t('register.lowercase') }}
              </el-tag>
              <el-tag v-if="formData.password" size="small" :type="hasUppercase ? 'success' : 'info'" class="strength-tag">
                <i :class="hasUppercase ? 'el-icon-check' : 'el-icon-close'"></i> {{ $t('register.uppercase') }}
              </el-tag>
              <el-tag v-if="formData.password" size="small" :type="hasNumbers ? 'success' : 'info'" class="strength-tag">
                <i :class="hasNumbers ? 'el-icon-check' : 'el-icon-close'"></i> {{ $t('register.number') }}
              </el-tag>
              <el-tag v-if="formData.password" size="small" :type="hasSpecial ? 'success' : 'info'" class="strength-tag">
                <i :class="hasSpecial ? 'el-icon-check' : 'el-icon-close'"></i> {{ $t('register.special') }}
              </el-tag>
              <el-tag v-if="formData.password" size="small" :type="hasLength ? 'success' : 'info'" class="strength-tag">
                <i :class="hasLength ? 'el-icon-check' : 'el-icon-close'"></i> {{ $t('register.minLength') }}
              </el-tag>
            </div>
          </div>
        </div>
        
        <div class="form-group">
          <label for="confirmPassword">Confirm Password <span class="required">*</span></label>
          <div class="password-input-container">
            <input 
              :type="showConfirmPassword ? 'text' : 'password'" 
              id="confirmPassword" 
              v-model="formData.confirmPassword" 
              placeholder="Confirm your password"
              class="form-input"
              :class="{ 'error': errors.confirmPassword }"
            >
            <button 
              type="button"
              class="toggle-password"
              @click="showConfirmPassword = !showConfirmPassword"
            >
              <i :class="showConfirmPassword ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
            </button>
          </div>
          <span v-if="errors.confirmPassword" class="error-message">{{ errors.confirmPassword }}</span>
        </div>
        
        <div class="form-options">
          <div class="terms-check">
            <input type="checkbox" id="terms" v-model="formData.acceptTerms">
            <label for="terms">{{ $t('register.termsAgree') }} <a href="#" class="terms-link">{{ $t('register.termsAndConditions') }}</a> {{ $t('register.and') }} <a href="#" class="terms-link">{{ $t('register.privacyPolicy') }}</a></label>
          </div>
        </div>
        
        <button 
          type="button" 
          class="register-btn primary-btn" 
          :disabled="isLoading || !canProceed"
          @click="register"
        >
          <span v-if="!isLoading">{{ $t('register.registerButton') }}</span>
          <span v-else class="loading-spinner"></span>
        </button>
      </div>
      
      <div class="login-section">
        <p>Already have an account? <router-link to="/login" class="login-link">Login here</router-link></p>
      </div>
    </div>
  </div>
</template>

<script>
import { ElMessage, ElProgress, ElTag } from 'element-plus'
import { register } from '@/api/users'
import StarryBackground from '@/components/StarryBackground.vue'
import { useInviteCode } from '@/utils/inviteCodeUtils'

export default {
  name: 'RegisterView',
  components: {
    ElProgress,
    ElTag,
    StarryBackground
  },
  data() {
    return {
      showPassword: false,
      showConfirmPassword: false,
      isLoading: false,
      formData: {
        username: '',
        email: '',
        password: '',
        confirmPassword: '',
        acceptTerms: false
      },
      errors: {
        username: '',
        email: '',
        password: '',
        confirmPassword: ''
      }
    }
  },
  computed: {
    passwordStrength() {
      const password = this.formData.password
      if (!password) {
        return { percent: 0, label: 'None', class: '' }
      }
      
      let strength = 0
      const regexes = [
        /[a-z]/, // lowercase
        /[A-Z]/, // uppercase
        /[0-9]/, // numbers
        /[^a-zA-Z0-9]/ // special chars
      ]
      
      regexes.forEach(regex => {
        if (regex.test(password)) strength++
      })
      
      if (password.length < 6) {
        strength = 1
      } else if (password.length > 10) {
        strength++
      }
      
      const strengthMap = [
        { percent: 20, label: 'Very Weak', class: 'very-weak' },
        { percent: 40, label: 'Weak', class: 'weak' },
        { percent: 60, label: 'Medium', class: 'medium' },
        { percent: 80, label: 'Strong', class: 'strong' },
        { percent: 100, label: 'Very Strong', class: 'very-strong' }
      ]
      
      return strengthMap[Math.min(strength, 4)]
    },
    passwordStrengthStatus() {
      const strength = this.passwordStrength.class
      if (!strength) return ''
      if (strength === 'very-weak' || strength === 'weak') return 'exception'
      if (strength === 'medium') return 'warning'
      return 'success'
    },
    passwordStrengthColor() {
      const strength = this.passwordStrength.class
      if (!strength) return ''
      if (strength === 'very-weak') return '#ff4949'
      if (strength === 'weak') return '#e6a23c'
      if (strength === 'medium') return '#f1c40f'
      if (strength === 'strong') return '#67c23a'
      if (strength === 'very-strong') return '#27ae60'
      return ''
    },
    hasLowercase() {
      return /[a-z]/.test(this.formData.password)
    },
    hasUppercase() {
      return /[A-Z]/.test(this.formData.password)
    },
    hasNumbers() {
      return /[0-9]/.test(this.formData.password)
    },
    hasSpecial() {
      return /[^a-zA-Z0-9]/.test(this.formData.password)
    },
    hasLength() {
      return this.formData.password && this.formData.password.length >= 6
    },
    canProceed() {
      return (
        this.formData.username && 
        this.formData.email && 
        this.formData.password && 
        this.formData.confirmPassword && 
        this.formData.acceptTerms &&
        !this.errors.username &&
        !this.errors.email &&
        !this.errors.password &&
        !this.errors.confirmPassword
      )
    }
  },
  methods: {
    // 从注册响应中提取用户ID
    extractUserId(response) {
      console.log('注册响应数据:', response);

      // 根据您的后端响应格式，data字段直接就是用户ID
      if (response.data) {
        // 如果data是字符串或数字，直接返回
        if (typeof response.data === 'string' || typeof response.data === 'number') {
          console.log('找到用户ID在data字段:', response.data);
          return response.data;
        }

        // 如果data是对象，尝试常见的用户ID字段
        if (typeof response.data === 'object') {
          const possibleFields = ['userId', 'user_id', 'id', 'uid'];
          for (const field of possibleFields) {
            if (response.data[field]) {
              console.log(`找到用户ID在字段 data.${field}:`, response.data[field]);
              return response.data[field];
            }
          }
        }
      }

      // 检查 response 根级别的字段
      const rootFields = ['userId', 'user_id', 'id', 'uid'];
      for (const field of rootFields) {
        if (response[field]) {
          console.log(`找到用户ID在字段 ${field}:`, response[field]);
          return response[field];
        }
      }

      console.warn('未找到用户ID字段，响应结构:', response);
      return null;
    },

    // 处理邀请码注册统计
    async handleInviteCodeRegistration(userId) {
      try {
        if (!userId) {
          console.warn('用户ID不存在，无法记录邀请码注册');
          console.warn('请检查后端注册接口是否返回了用户ID');
          return;
        }

        console.log('开始记录邀请码注册统计，用户ID:', userId);

        // 获取邀请码
        const { getStoredInviteCode } = useInviteCode();
        const storedInviteCode = getStoredInviteCode();

        // 也尝试从URL获取邀请码作为备选
        const urlParams = new URLSearchParams(window.location.search);
        const urlInviteCode = urlParams.get('invite_code') || urlParams.get('inviteCode');

        const inviteCode = storedInviteCode || urlInviteCode;

        console.log('存储的邀请码:', storedInviteCode);
        console.log('URL中的邀请码:', urlInviteCode);
        console.log('最终使用的邀请码:', inviteCode);

        if (!inviteCode) {
          console.warn('未找到邀请码，跳过邀请码注册统计');
          return;
        }

        // 直接调用API记录注册
        const { InviteCodeUtils } = await import('@/utils/inviteCodeUtils');
        const success = await InviteCodeUtils.recordRegistration(inviteCode, userId);

        if (success) {
          console.log('邀请码注册统计记录成功');
        } else {
          console.warn('邀请码注册统计记录失败');
        }
      } catch (error) {
        console.error('邀请码注册统计记录失败:', error);
        // 不影响主要的注册流程，只记录错误
      }
    },

    validateEmail(email) {
      const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      return re.test(email)
    },
    validateUsername(username) {
      const re = /^[a-zA-Z0-9_]{6,20}$/
      return re.test(username)
    },
    validateForm() {
      let isValid = true
      
      // Validate username
      if (!this.formData.username) {
        this.errors.username = 'Username is required'
        isValid = false
      } else if (!this.validateUsername(this.formData.username)) {
        this.errors.username = 'Username must be 6-20 characters and contain only letters, numbers and underscore'
        isValid = false
      } else {
        this.errors.username = ''
      }
      
      // Validate email
      if (!this.formData.email) {
        this.errors.email = 'Email is required'
        isValid = false
      } else if (!this.validateEmail(this.formData.email)) {
        this.errors.email = 'Please enter a valid email address'
        isValid = false
      } else {
        this.errors.email = ''
      }
      
      // Validate password
      if (!this.formData.password) {
        this.errors.password = 'Password is required'
        isValid = false
      } else if (this.formData.password.length < 6) {
        this.errors.password = 'Password must be at least 6 characters'
        isValid = false
      } else {
        this.errors.password = ''
      }
      
      // Validate confirm password
      if (!this.formData.confirmPassword) {
        this.errors.confirmPassword = 'Please confirm your password'
        isValid = false
      } else if (this.formData.confirmPassword !== this.formData.password) {
        this.errors.confirmPassword = 'Passwords do not match'
        isValid = false
      } else {
        this.errors.confirmPassword = ''
      }
      
      return isValid
    },
    async register() {
      if (!this.validateForm()) {
        return;
      }
      
      try {
        this.isLoading = true;
        
        const response = await register({
          username: this.formData.username,
          email: this.formData.email,
          password: this.formData.password
        });
        
        if (response && response.code === 200) {
          ElMessage({
            message: 'Registration successful! Welcome to AGTFIND!',
            type: 'success',
            duration: 3000
          });

          if (response.data && response.data.tokenName && response.data.tokenValue) {
            localStorage.setItem(response.data.tokenName, response.data.tokenValue);
            localStorage.setItem('isLoggedIn', 'true');
            localStorage.setItem('rememberedUser', this.formData.username);
          }

          // 处理邀请码注册统计
          const userId = this.extractUserId(response);
          await this.handleInviteCodeRegistration(userId);

          this.$router.push('/');
        } else {
          ElMessage({
            message: response.msg || 'Registration failed. Please try again.',
            type: 'error',
            duration: 3000
          });
        }
      } catch (error) {
        ElMessage({
          message: 'Registration failed. Please try again later.',
          type: 'error',
          duration: 3000
        });
        console.error('Registration error:', error);
      } finally {
        this.isLoading = false;
      }
    }
  },
  watch: {
    'formData.username'() {
      if (this.formData.username) {
        if (!this.validateUsername(this.formData.username)) {
          this.errors.username = 'Username must be 6-20 characters and contain only letters, numbers and underscore'
        } else {
          this.errors.username = ''
        }
      }
    },
    'formData.email'() {
      if (this.formData.email) {
        if (!this.validateEmail(this.formData.email)) {
          this.errors.email = 'Please enter a valid email address'
        } else {
          this.errors.email = ''
        }
      }
    },
    'formData.password'() {
      if (this.formData.password) {
        if (this.formData.password.length < 6) {
          this.errors.password = 'Password must be at least 6 characters'
        } else {
          this.errors.password = ''
        }
        
        if (this.formData.confirmPassword && this.formData.confirmPassword !== this.formData.password) {
          this.errors.confirmPassword = 'Passwords do not match'
        } else if (this.formData.confirmPassword) {
          this.errors.confirmPassword = ''
        }
      }
    },
    'formData.confirmPassword'() {
      if (this.formData.confirmPassword) {
        if (this.formData.confirmPassword !== this.formData.password) {
          this.errors.confirmPassword = 'Passwords do not match'
        } else {
          this.errors.confirmPassword = ''
        }
      }
    }
  }
}
</script>

<style scoped>
.register-page {
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  position: relative;
  color: #e0e0e0;
  /* 背景设置为透明，让StarryBackground显示 */
  background: transparent;
  background-image: none;
  z-index: 1; /* 确保高于星空背景 */
}

.register-container {
  background: linear-gradient(135deg, rgba(36, 36, 40, 0.75), rgba(26, 26, 32, 0.85));
  width: 100%;
  max-width: 450px;
  border-radius: 15px;
  padding: 2.5rem;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4), 0 0 30px rgba(120, 70, 200, 0.2);
  border: 1px solid rgba(120, 70, 200, 0.4);
  position: relative;
  z-index: 5; /* 确保在星空背景之上 */
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.register-container:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 25px rgba(0, 0, 0, 0.5), 0 0 40px rgba(120, 70, 200, 0.4);
  border-color: rgba(120, 70, 200, 0.6);
}

.register-header {
  text-align: center;
  margin-bottom: 2rem;
}

.logo {
  width: 120px;
  margin-bottom: 1rem;
  filter: drop-shadow(0 0 10px rgba(195, 163, 255, 0.7));
}

.register-header h1 {
  color: #c3a3ff;
  font-size: 1.8rem;
  margin: 0;
  font-weight: 700;
  text-shadow: 0 0 15px rgba(195, 163, 255, 0.5);
}

/* Form styling */
.register-form {
  margin-bottom: 1.5rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  color: #c3a3ff;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.required {
  color: #ff6c7e;
  margin-left: 2px;
}

.form-input {
  width: 100%;
  padding: 0.8rem 1rem;
  border: 1px solid rgba(120, 70, 200, 0.3);
  border-radius: 8px;
  background: rgba(35, 35, 40, 0.6);
  color: #e0e0e0;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.form-input:focus {
  outline: none;
  border-color: #c3a3ff;
  box-shadow: 0 0 0 2px rgba(195, 163, 255, 0.2);
}

.form-input::placeholder {
  color: #999;
}

.form-input.error {
  border-color: #ff6c7e;
}

.error-message {
  display: block;
  color: #ff6c7e;
  font-size: 0.85rem;
  margin-top: 0.5rem;
  font-weight: 500;
}

.password-input-container {
  position: relative;
}

.toggle-password {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  background: transparent;
  border: none;
  color: #c3a3ff;
  cursor: pointer;
  padding: 5px;
}

.toggle-password:hover {
  color: #fff;
}

/* Element Plus 密码强度样式 */
.password-strength-el {
  margin-top: 0.8rem;
}

.strength-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.strength-label {
  color: #a9a9a9;
  font-size: 0.85rem;
}

.strength-text.very-weak,
.strength-text.weak {
  color: #ff6c7e;
}

.strength-text.medium {
  color: #ffbb3c;
}

.strength-text.strong,
.strength-text.very-strong {
  color: #7fc6a4;
}

.password-tips {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 0.8rem;
}

.strength-tag {
  font-size: 0.75rem;
  margin-right: 5px;
  border-radius: 4px;
}

/* 覆盖Element Plus组件样式以适应暗色主题 */
:deep(.el-progress-bar__outer) {
  background-color: rgba(120, 70, 200, 0.2) !important;
}

:deep(.el-tag) {
  background-color: rgba(35, 35, 40, 0.6);
  border: 1px solid rgba(120, 70, 200, 0.3);
  color: #a9a9a9;
}

:deep(.el-tag--success) {
  background-color: rgba(35, 35, 40, 0.6);
  border-color: #7fc6a4;
  color: #7fc6a4;
}

/* Form options */
.form-options {
  margin-bottom: 1.5rem;
}

.terms-check, .newsletter-option {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
  color: #a9a9a9;
}

.terms-check input[type="checkbox"],
.newsletter-option input[type="checkbox"] {
  accent-color: #c3a3ff;
  width: 16px;
  height: 16px;
  margin-top: 3px;
}

.terms-link {
  color: #c3a3ff;
  text-decoration: none;
  transition: color 0.3s ease;
}

.terms-link:hover {
  color: #fff;
  text-decoration: underline;
}

/* Buttons */
.register-btn {
  width: 100%;
  padding: 0.8rem;
  background: linear-gradient(135deg, #352458, #2d1e4a);
  color: #e0e0e0;
  border: 1px solid rgba(120, 70, 200, 0.3);
  border-radius: 30px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3), 0 0 20px rgba(120, 70, 200, 0.2);
  position: relative;
  overflow: hidden;
}

.register-btn:before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  transition: all 0.6s;
}

.register-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.5), 0 0 30px rgba(120, 70, 200, 0.5);
  background: linear-gradient(135deg, #42306a, #352458);
  border-color: rgba(120, 70, 200, 0.7);
}

.register-btn:hover:before {
  left: 100%;
}

.register-btn:active {
  transform: translateY(0);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.4), 0 0 20px rgba(120, 70, 200, 0.3);
  background: linear-gradient(135deg, #3a295f, #2a1c42);
}

.register-btn:after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  background: rgba(255, 255, 255, 0.5);
  opacity: 0;
  border-radius: 100%;
  transform: scale(1, 1) translate(-50%, -50%);
  transform-origin: 50% 50%;
}

.register-btn:focus:not(:active)::after {
  animation: ripple 1s ease-out;
}

@keyframes ripple {
  0% {
    transform: scale(0, 0);
    opacity: 0.7;
  }
  20% {
    transform: scale(20, 20);
    opacity: 0.5;
  }
  100% {
    opacity: 0;
    transform: scale(40, 40);
  }
}

/* 脉冲光晕效果 */
.register-btn:hover {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.5), 0 0 30px rgba(120, 70, 200, 0.5);
  }
  50% {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.6), 0 0 40px rgba(120, 70, 200, 0.7);
  }
  100% {
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.5), 0 0 30px rgba(120, 70, 200, 0.5);
  }
}

.register-btn:disabled {
  background: linear-gradient(135deg, #2d2d35, #22222a);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
  opacity: 0.7;
  animation: none;
}

/* 添加一个轻微的渐变动画 */
.register-btn {
  background-size: 200% 200%;
  animation: gradientShift 5s ease infinite;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* 加载动画修改 */
.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-top: 2px solid rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  animation: spin 1s cubic-bezier(0.5, 0.1, 0.5, 0.9) infinite;
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

.login-section {
  text-align: center;
  color: #a9a9a9;
}

.login-link {
  color: #c3a3ff;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
}

.login-link:hover {
  color: #fff;
  text-decoration: underline;
}

@media (max-width: 768px) {
  .register-container {
    padding: 1.5rem;
  }
  
  .logo {
    width: 100px;
  }
  
  .register-header h1 {
    font-size: 1.5rem;
  }
}

/* 动画关键帧 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>