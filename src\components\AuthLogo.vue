<template>
  <img :src="logoSrc" alt="AGTFIND Logo" class="auth-logo" />
</template>

<script>
import { emitter } from '@/utils/eventBus'

export default {
  name: 'AuthLogo',
  data() {
    return {
      isDarkMode: true // 默认深色主题
    }
  },
  computed: {
    logoSrc() {
      // 根据主题模式返回对应的logo图片
      return this.isDarkMode
        ? 'https://omgbuy.oss-eu-central-1.aliyuncs.com/ylkj-uploads/2025/07/29/20250729-144453_compressed_20250729144813A002.png' // 深色主题logo
        : 'https://omgbuy.oss-eu-central-1.aliyuncs.com/ylkj-uploads/2025/07/29/20250728-182903_compressed_20250729144751A001.png' // 浅色主题logo
    }
  },
  created() {
    // 从localStorage获取主题设置
    const savedDarkMode = localStorage.getItem('darkMode');
    this.isDarkMode = savedDarkMode !== null ? savedDarkMode === 'true' : true;

    // 监听主题变化事件
    emitter.on('theme-changed', this.handleThemeChange);
    emitter.on('apply-theme-to-page', this.handleThemeChange);
  },
  beforeUnmount() {
    // 清理事件监听
    emitter.off('theme-changed', this.handleThemeChange);
    emitter.off('apply-theme-to-page', this.handleThemeChange);
  },
  methods: {
    handleThemeChange(data) {
      if (data && typeof data.isDarkMode === 'boolean') {
        this.isDarkMode = data.isDarkMode;
      }
    }
  }
}
</script>

<style scoped>
.auth-logo {
  width: 120px;
  margin-bottom: 1rem;
  transition: filter 0.3s ease;
}

/* 深色主题Logo */
.login-page[data-theme="dark"] .auth-logo,
.login-page.dark-theme .auth-logo,
.register-page[data-theme="dark"] .auth-logo,
.register-page.dark-theme .auth-logo {
  filter: drop-shadow(0 0 10px rgba(195, 163, 255, 0.7));
}

/* 浅色主题Logo */
.login-page[data-theme="light"] .auth-logo,
.register-page[data-theme="light"] .auth-logo {
  filter: drop-shadow(0 0 10px rgba(147, 51, 234, 0.4));
}

@media (max-width: 768px) {
  .auth-logo {
    width: 100px;
  }
}
</style>
