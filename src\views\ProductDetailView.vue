<template>
  <div class="product-detail">
    <StarryBackground />

    <!-- 加载动画 -->
    <div class="loading-overlay" v-if="isLoading">
      <div class="loading-spinner">
        <div class="spinner-inner"></div>
      </div>
      <div class="loading-text">{{ loadingMessage }}</div>
    </div>

    <!-- 悬浮在导航栏图标下方的返回按钮 -->
    <div class="floating-back-button" @click="goBack">
      <i class="fas fa-arrow-left"></i> {{ $t('common.back') }}
    </div>

    <div id="product-main" class="product-container">
      <!-- 商品图片部分 -->
      <div class="product-images">
        <!-- 主图区域 -->
        <div class="product-image-container">
          <!-- 主图/视频切换区域 -->
          <div class="media-toggle-buttons" v-if="product.videoUrl">
            <button 
              class="media-toggle-btn" 
              :class="{ 'active': !showVideo }" 
              @click="showVideo = false">
              <i class="fas fa-image"></i>
            </button>
            <button 
              class="media-toggle-btn" 
              :class="{ 'active': showVideo }" 
              @click="toggleVideo">
              <i class="fas fa-video"></i>
            </button>
          </div>
          
          <!-- 主图轮播 -->
          <div class="main-product-image"
            :class="{ 'isZooming': isZooming, 'hidden': showVideo }"
            v-show="!showVideo">
            <!-- 如果有多张主图，显示轮播 -->
            <div v-if="mainImages.length > 1" class="main-image-carousel">
              <el-carousel
                height="100%"
                indicator-position="none"
                :arrow="isMobile ? 'never' : 'hover'"
                :autoplay="false"
                class="main-carousel"
                @change="handleMainImageChange"
                ref="mainCarousel">
                <el-carousel-item v-for="(image, index) in mainImages" :key="index" class="main-carousel-item">
                  <div class="main-image-wrapper"
                    @mousemove="handleImageZoom"
                    @mouseleave="resetZoom">
                    <img :src="image.ossImageUrl" :alt="product.name + ' main image ' + (index + 1)" ref="mainImage">
                    <div class="image-zoom-lens"></div>
                    <div class="zoom-hint" v-if="!isZooming && !isMobile">
                      <el-icon>
                        <ZoomIn />
                      </el-icon>
                      <span>{{ $t('product.zoomHint', 'Move the mouse to zoom in and view') }}</span>
                    </div>
                  </div>
                </el-carousel-item>
              </el-carousel>
              <!-- 页码指示器 -->
              <div v-if="mainImages.length > 1" class="carousel-counter">
                {{ currentMainImageIndex + 1 }}/{{ mainImages.length }}
              </div>
            </div>
            <!-- 如果只有一张主图，显示单张图片 -->
            <div v-else class="single-main-image"
              @mousemove="handleImageZoom"
              @mouseleave="resetZoom">
              <img :src="currentMainImage || product.mainImage || product.image" :alt="product.name" ref="mainImage">
              <div class="image-zoom-lens"></div>
              <div class="zoom-hint" v-if="!isZooming && !isMobile">
                <el-icon>
                  <ZoomIn />
                </el-icon>
                <span>{{ $t('product.zoomHint', 'Move the mouse to zoom in and view') }}</span>
              </div>
            </div>
          </div>
          
          <!-- 视频播放区域 -->
          <div class="main-product-video" v-show="showVideo" :class="{ 'hidden': !showVideo }">
            <video
              ref="productVideo"
              :src="product.videoUrl"
              controls
              muted
              playsinline
              :poster="product.mainImage || product.image"
              @play="isVideoPlaying = true"
              @pause="isVideoPlaying = false"
              @ended="isVideoPlaying = false"
              controlsList="nodownload"
              preload="auto"
              class="product-video"
            ></video>
          </div>

          <!-- 移除缩略图网格 -->
        </div>

        <!-- 历史购买信息 -->
        <el-card class="historical-purchase-info" shadow="never">
          <template #header>
            <div class="info-header">Historical Purchase Information</div>
          </template>
          <div class="info-row">
            <div class="info-item">
              <el-input
                class="info-input"
                disabled
                :placeholder="`Weight : ${product.weight || 'N/A'} g`"
                :value="`Weight : ${product.weight || 'N/A'} g`"
              />
            </div>
            <div class="info-item">
              <el-input
                class="info-input"
                disabled
                :placeholder="`Average Arrival : ${product.averageArrival || 'N/A'} days`"
                :value="`Average Arrival : ${product.averageArrival || 'N/A'} days`"
              />
            </div>
          </div>
        </el-card>
      </div>

      <!-- 商品信息部分 - 拆分为两部分 -->
      <div class="product-info">
        <!-- 第一部分: 价格、商品名称、标签、来源和社交统计 -->
        <div class="product-info-section product-info-top">
                  <!-- 收藏按钮 - 移至右上角 -->
        <button class="wishlist-btn-corner" @click="handleLike" :class="{ 'liked': product.isLiked }" title="Add to wishlist">
          <i :class="[product.isLiked ? 'fas fa-heart' : 'far fa-heart']"></i>
        </button>
          
          <!-- 价格区域 -->
          <div class="price-section">
            <div class="price-tag">
              <span class="price-amount">{{ currencySymbol }}{{ formatPrice(product.price) }}</span>
            </div>
          </div>
          
                  <!-- 放大视图容器 - 移到产品信息区域内，作为覆盖层 -->
        <div class="zoom-container" v-show="isZooming">
          <div class="zoom-header">
            <div class="zoom-title">
              <el-icon>
                <ZoomIn />
              </el-icon>
            </div>
            <button class="zoom-close" @click="resetZoom" title="关闭放大视图">
              <i class="fas fa-times"></i>
            </button>
          </div>
          <div class="image-zoom-result" @click="resetZoom"></div>
        </div>
            
          <!-- 商品名称 -->
          <el-tooltip :content="product.name" placement="top" effect="dark" :enterable="false" :show-after="300">
            <h1 class="product-name">{{ product.name }} <span class="product-count" v-if="product.name && product.name.includes('（')">{{ product.name.split('（')[1] }}</span></h1>
          </el-tooltip>

          <!-- 商品标签 - 动态渲染 -->
          <div class="product-tag-row" v-if="productTags && productTags.length > 0">
            <div class="product-tag" v-for="(tag, index) in productTags" :key="index">{{ tag }}</div>
          </div>
            
          <!-- FROM 1688 链接带紫色背景 -->
<div class="from-1688-bar">
  <span>FROM {{ product.platform === '微店' ? 'weidian' : product.platform || '1688' }} :</span>
  <a :href="product.fromUrl || '#'" class="from-link" target="_blank">{{ product.fromUrl || 'Click the link to jump to the target website' }}</a>
</div>

          <!-- 社交统计 - 新样式 -->
          <div class="social-stats-prominent">
            <div class="stat-item-prominent" @click="handleLike">
              <i class="fas fa-thumbs-up"></i>
              <span>{{ product.likes || 0 }}</span>
            </div>
            <div class="stat-item-prominent">
              <i class="fas fa-image"></i>
              <span>{{ qcImages.length || 0 }}</span>
            </div>
            <div class="stat-item-prominent" @click="scrollToComments">
              <i class="fas fa-comment"></i>
              <span>{{ sortedComments ? sortedComments.length : 0 }}</span>
            </div>
            <div class="stat-item-prominent">
              <i class="far fa-eye"></i>
              <span>{{ getTotalViews() }}</span>
            </div>
          </div>
        </div>

        <!-- 第二部分: 平台选择、货币选择和购买按钮 -->
        <div class="product-info-section product-info-bottom">
          <!-- 代理平台图标横排 -->
          <div class="platform-icons-row">
            <div v-for="platform in platforms" :key="platform.id" 
                class="platform-icon-item" 
                @click="setDefaultPlatform(platform)"
                :class="{ 'active': defaultPlatform && defaultPlatform.id === platform.id }">
              <img :src="platform.logo" :alt="platform.name" class="platform-logo-img">
            </div>
          </div>

          <!-- 货币选择区域 -->
          <div class="agent-platforms">
            <div class="platform-option">
              <span class="platform-value">RMB {{ rmb_price }}</span>
            </div>
            <div class="platform-option">
              <span class="platform-value">USD {{ usd_price }}</span>
            </div>
          </div>

          <!-- 购买按钮 -->
          <div class="buy-button-container">
            <button class="buy-now-btn" @click="buyNow">
              Buy Now On {{ defaultPlatform ? defaultPlatform.name : 'xxxAgent' }}
            </button>
          </div>
        </div>
      </div>
    </div>

        <!-- 实物图展示区域 -->
    <div class="product-gallery">
      <h2>{{ $t('product.qcPhotos') }} <span v-if="qcImageGroups.length > 0" class="group-total">({{ qcImageGroups.length }} {{ $t('product.groups', 'Groups') }})</span></h2>
      <div v-if="qcImages.length > 0" class="carousel-groups-container">
        <div class="carousel-groups-row">
          <div class="carousel-group" v-for="(group, groupIndex) in visibleQcImageGroups" :key="groupIndex" v-swipe="groupIndex"
            @swipe="handleSwipeEvent">
            <div class="carousel-group-title">
              <span>{{ $t('product.group', 'Group') }} {{ groupIndex + 1 }}</span>
              <span class="group-count">{{ getCurrentIndex(groupIndex) }}/{{ group.length }}</span>
            </div>
            <el-carousel height="100%" indicator-position="none" :arrow="isMobile ? 'never' : 'always'" :autoplay="false"
              class="single-carousel square-carousel" @change="(index) => updateGroupIndex(groupIndex, index)" :touch="true" :loop="false"
              ref="carousels">
              <el-carousel-item v-for="(img, index) in group" :key="index" class="carousel-item-full">
                <el-image :src="getImageUrl(img)" 
                  :alt="product.name + ' QC image ' + (groupIndex * 6 + index + 1)"
                  :preview-src-list="qcImagesUrls" :initial-index="groupIndex * 6 + index" fit="cover"
                  :preview-teleported="true" class="qc-image-full" @click="handleImageClick()">
                  <template #error>
                    <div class="image-error-placeholder">
                      <el-icon><Picture /></el-icon>
                      <p>image loading failed</p>
                    </div>
                  </template>
                  <template #placeholder>
                    <div class="image-loading-placeholder">
                      <el-icon class="is-loading"><Loading /></el-icon>
                    </div>
                  </template>
                </el-image>
              </el-carousel-item>
            </el-carousel>
          </div>
        </div>
        
        <!-- 加载更多按钮 -->
        <div v-if="hasMoreGroups" class="load-more-container">
          <button class="load-more-btn" @click="loadMoreGroups" :disabled="isLoadingMoreGroups">
            <span v-if="!isLoadingMoreGroups">
              <i class="fas fa-plus-circle"></i> 
              {{ $t('product.loadMore', 'Load More') }} 
              ({{ visibleGroupsCount }}/{{ qcImageGroups.length }})
            </span>
            <span v-else class="loading-spinner-small">
              <i class="fas fa-spinner fa-spin"></i> 
              {{ $t('product.loading', 'Loading...') }}
            </span>
          </button>
        </div>
      </div>
      <div v-else class="no-gallery-items">
        <p>{{ $t('product.noQcImages') }}</p>
      </div>
    </div>

    <!-- 评论区 -->
    <div id="comments" class="comments-section">
      <el-card class="comments-card" shadow="hover">
        <template #header>
          <div class="comments-header">
            <h2><el-icon>
                <ChatDotRound />
              </el-icon> {{ $t('product.customerReviews') }} ({{ sortedComments.length }})</h2>
            <el-tag type="info" size="large" effect="dark" round>{{ sortedComments.length }} {{
              $t('product.commentCount') }}</el-tag>
          </div>
        </template>
        
        <!-- 评论排序选项 -->
        <div class="comment-sort-options">
          <div class="sort-buttons">
            <button @click="commentSortOption = 'newest'; sortComments()"
              :class="['sort-btn', commentSortOption === 'newest' ? 'active' : '']">
              <i class="fas fa-clock"></i> {{ $t('product.sortByNewest') }}
            </button>
            <button @click="commentSortOption = 'popular'; sortComments()"
              :class="['sort-btn', commentSortOption === 'popular' ? 'active' : '']">
              <i class="fas fa-thumbs-up"></i> {{ $t('product.sortByPopular') }}
            </button>
          </div>
        </div>

        <!-- 评论输入框 - 仅登录用户可见 -->
        <div v-if="isLoggedIn" class="comment-form">
          <el-avatar :size="50" :src="getUserAvatar()" class="comment-avatar-input"></el-avatar>
          <div class="comment-input-wrapper">
            <div class="comment-input-container">
              <el-input v-model="newComment" type="textarea" :rows="3" :placeholder="$t('product.shareThoughts')"
                resize="none" class="comment-textarea" />
              
              <!-- 内嵌添加图片按钮 -->
              <div class="comment-inline-upload-btn" @click="triggerUploadClick">
                <el-icon class="upload-icon">
                  <Plus />
                </el-icon>
              </div>
            </div>

            <!-- 图片上传组件 - 保持隐藏但功能完整 -->
            <div class="comment-upload-section" style="display: none;">
              <el-upload ref="uploadRef" :action="uploadUrl" list-type="picture-card"
                :on-preview="handlePictureCardPreview" :on-remove="handleRemove" :on-success="handleUploadSuccess"
                :on-error="handleUploadError" :before-upload="beforeImageUpload" :limit="4" accept="image/*">
                <div class="upload-placeholder">
                  <el-icon class="upload-icon">
                    <Plus />
                  </el-icon>
                  <div class="upload-text">Add Image</div>
                </div>
              </el-upload>

              <!-- 图片预览弹窗 -->
              <el-dialog v-model="dialogVisible" :append-to-body="true" center>
                <img style="max-width: 100%; max-height: 80vh; object-fit: contain;" :src="dialogImageUrl" alt="预览图" />
              </el-dialog>
            </div>

            <!-- 已上传图片预览区 -->
            <div class="uploaded-images-preview" v-if="uploadedImages.length > 0">
              <div v-for="(image, index) in uploadedImages" :key="index" class="uploaded-image-item">
                <img :src="image.url" alt="上传的图片" />
                <div class="image-remove-btn" @click="handleRemoveByIndex(index)">
                  <el-icon><Close /></el-icon>
                </div>
              </div>
            </div>

            <div class="comment-controls">
              <div class="upload-info" v-if="uploadedImages.length > 0">
                <el-tag type="success" effect="plain" round>
                  <el-icon>
                    <Picture />
                  </el-icon>
                  {{ uploadedImages.length }} {{ uploadedImages.length > 1 ? $t('product.imagesSelected', 'images selected') : $t('product.imageSelected', 'image selected') }}
                </el-tag>
              </div>
              <el-button type="primary" @click="addComment"
                :disabled="!newComment.trim() && uploadedImages.length === 0" :loading="isSubmitting" round
                class="submit-comment-btn">
                <el-icon>
                  <Position />
                </el-icon>
                {{ $t('product.postComment') }}
              </el-button>
            </div>
          </div>
        </div>

        <!-- 未登录用户提示 -->
        <div v-else class="login-prompt-container">
          <el-result icon="warning" :title="$t('product.loginRequired', 'Login Required')"
            :sub-title="$t('product.loginToComment', 'Please login to post comments')">
            <template #icon>
              <div class="custom-icon-container">
                <div class="animated-lock-icon">
                  <el-icon class="lock-icon">
                    <Lock />
                  </el-icon>
                  <div class="pulse-circle"></div>
                </div>
              </div>
            </template>
            <template #extra>
              <el-button type="primary" size="large" round @click="goToLogin" class="login-btn-animated">
                <el-icon class="login-icon">
                  <User />
                </el-icon>
                <span>{{ $t('product.loginNow', 'Login Now') }}</span>
                <span class="btn-shine"></span>
              </el-button>
            </template>
          </el-result>
        </div>

        <!-- 评论列表 -->
        <div class="comments-divider">
          <div class="all-comments-button">
            <div class="cyber-line left"></div>
            <!-- 移除了中间的文本和图标，只保留动态线条 -->
            <div class="cyber-line right"></div>
          </div>
        </div>

        <el-timeline v-if="sortedComments.length > 0">
          <el-timeline-item v-for="(comment, index) in displayedComments" :key="index"
            :hollow="true" type="primary" size="large"
            class="comment-timeline-item">
            <el-card class="comment-card" shadow="hover">
              <div class="comment-item-content">
                <div class="comment-user-info">
                  <el-avatar :size="40" :src="comment.avatarUrl" />
                  <div class="comment-user-details">
                    <span class="comment-username">{{ comment.username }}</span>
                    <span class="comment-time">{{ formatDate(comment.createdAt) }}</span>
                  </div>
                </div>
                <p class="comment-text">{{ comment.text }}</p>

                <!-- 添加评论图片展示区域 -->
                <div v-if="comment.images && comment.images.length > 0" class="comment-images">
                  <el-image v-for="(image, imgIndex) in comment.images" :key="imgIndex"
                    :src="typeof image === 'string' ? image : (image.url || '')"
                    :preview-src-list="getCommentImageUrls(comment)" :initial-index="imgIndex"
                    class="comment-image-item" fit="cover" :preview-teleported="true" :z-index="3000"
                    hide-on-click-modal
                    :preview-title="getImagePreviewTitle(imgIndex, comment.images.length, comment.username)">
                    <template #placeholder>
                      <div class="image-loading">
                        <el-icon class="is-loading">
                          <Loading />
                        </el-icon>
                      </div>
                    </template>
                    <template #error>
                      <div class="image-error">
                        <el-icon>
                          <Picture />
                        </el-icon>
                      </div>
                    </template>
                    <template #viewer>
                      <div class="image-preview-info">
                        <span>{{ imgIndex + 1 }}/{{ getCommentImageUrls(comment).length }}</span>
                      </div>
                    </template>
                    <div class="image-zoom-indicator">
                      <el-icon>
                        <ZoomIn />
                      </el-icon>
                    </div>
                  </el-image>
                </div>

                <div class="comment-actions">
                  <el-button @click="(e) => { e.preventDefault(); likeComment(index); }" :type="comment.isLiked ? 'primary' : ''" size="small" text
                    class="comment-action-btn">
                    <el-icon>
                      <i :class="comment.isLiked ? 'fas fa-thumbs-up' : 'far fa-thumbs-up'"></i>
                    </el-icon>
                    <span>{{ comment.likes }}</span>
                  </el-button>

                  <el-button v-if="isLoggedIn" @click="replyToComment(index)" size="small" text
                    class="comment-action-btn">
                    <el-icon>
                      <ChatLineRound />
                    </el-icon>
                    <span>{{ $t('product.reply') }}</span>
                  </el-button>

                  <el-button v-if="isUserComment(comment)" @click="confirmDeleteComment(index)" size="small"
                    type="danger" text class="comment-action-btn">
                    <el-icon>
                      <Delete />
                    </el-icon>
                    <span>{{ $t('product.delete') }}</span>
                  </el-button>
                </div>
              </div>
            </el-card>
          </el-timeline-item>

          <!-- 加载更多按钮 -->
          <div v-if="displayedComments.length < sortedComments.length" class="load-more-container">
            <el-button @click="loadMoreComments" :loading="isLoadingMore" type="default" class="load-more-btn">
              <i class="fas fa-spinner fa-spin" v-if="isLoadingMore"></i>
              {{ $t('product.loadMoreComments', '加载更多评论') }}
            </el-button>
            <p class="comments-counter">{{ displayedComments.length }} / {{ sortedComments.length }}</p>
          </div>
        </el-timeline>

        <!-- 无评论状态 -->
        <el-result v-if="sortedComments.length === 0" icon="info"
          :title="$t('product.noComments', 'No comments yet')">
          <template #icon>
            <div class="custom-icon-container empty-comments-icon">
              <div class="animated-comments-icon">
                <el-icon class="comments-icon">
                  <ChatRound />
                </el-icon>
                <div class="comments-pulse-circle"></div>
              </div>
            </div>
          </template>
          <template #sub-title>
            <div class="no-comments-subtitle">{{ $t('product.beFirstToComment', 'Be the first to comment!') }}</div>
          </template>
          <template #extra>
            <el-button type="primary" size="large" round @click="isLoggedIn ? scrollToCommentForm() : goToLogin()"
              class="be-first-comment-btn">
              <i class="fas fa-comment-dots"></i>
              <span>{{ $t('product.postFirstComment', 'Post first comment') }}</span>
            </el-button>
          </template>
        </el-result>
      </el-card>
    </div>

    <!-- 相关推荐 -->
    <div class="related-products">
      <h2>{{ $t('product.relatedProducts') }}</h2>
      <div class="related-products-row" :class="{'dragging': isDragging}" 
        @mousedown="startDrag" @mousemove="onDrag" @mouseup="stopDrag" @mouseleave="stopDrag"
        @touchstart="startTouch" @touchmove="onTouch" @touchend="stopTouch">
        <div v-for="item in showRelatedProducts" :key="item.productId" class="related-product-col">
          <RelatedProductCard 
            :product="transformProductForCard(item)"
            :currency-symbol="currencySymbol"
            :exchange-rate="exchangeRate"
            :isDarkMode="isDarkMode"
            @product-click="handleRelatedProductClick"
            @like-updated="handleLikeUpdated"
            @collect-updated="handleCollectUpdated"
            @views-click="handleViewsClick"
            @comments-click="handleCommentsClick"
          />
        </div>
      </div>
    </div>

    <!-- 平台选择弹窗 -->
    <el-dialog v-model="platformDialogVisible" title="Choose agent platform" width="500px" :show-close="true"
      class="platform-dialog">
      <div class="platform-list">
        <div v-for="platform in platforms" :key="platform.id" class="platform-item"
          @click="goToPlatform(platform.urltemplate)">
          <div class="platform-logo">
            <img :src="platform.logo" :alt="platform.name">
          </div>
          <div class="platform-info">
            <div class="platform-name">{{ platform.name }}</div>
            <div class="platform-coupon">{{ platform.coupon }}</div>
          </div>
          <div class="platform-arrow">
            <el-icon>
              <ArrowRight />
            </el-icon>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 默认平台设置弹窗 -->
    <el-dialog v-model="platformSettingsVisible" title="Set default platform" :width="isMobile ? '90%' : '650px'"
      :show-close="true" class="platform-dialog" :modal-class="isMobile ? 'mobile-platform-dialog' : ''">
      <div class="platform-container">
        <div class="platform-grid">
          <div v-for="platform in platforms" :key="platform.id" class="platform-card"
            :class="{ 'platform-selected': defaultPlatform && defaultPlatform.id === platform.id }"
            @click="setDefaultPlatform(platform)">
            <div class="platform-logo">
              <img :src="platform.logo" :alt="platform.name">
            </div>
            <div class="platform-name">{{ platform.name }}</div>
            <div class="platform-exchange-rate">
              <span class="rate-text">{{ getPlatformExchangeRate(platform.name) }}</span>
            </div>
            <div class="platform-check" v-if="defaultPlatform && defaultPlatform.id === platform.id">
              <i class="fas fa-check"></i>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import { ElMessage, ElImage, ElDialog, ElIcon, ElMessageBox, ElCarousel, ElCarouselItem } from 'element-plus'
import { ArrowRight } from '@element-plus/icons-vue'
import { ChatDotRound, User, Position, Delete, ChatLineRound, Lock, ChatRound, Plus, Picture, ZoomIn, Loading, Close } from '@element-plus/icons-vue'
import productsApi from '@/api/products'
import commentsApi from '@/api/comments'
import platformLinksApi from '@/api/platformLinks'
import { emitter } from '@/utils/eventBus'
import StarryBackground from '@/components/StarryBackground.vue'
import RelatedProductCard from '@/components/RelatedProductCard'
// import ProductCard from '@/components/ProductCard'

export default {
  name: 'ProductDetailView',
  directives: {
    swipe: {
      mounted(el, binding) {
        const groupIndex = binding.value;

        let startX = 0;
        let startY = 0;
        let startTime = 0;

        el.addEventListener('touchstart', (e) => {
          startX = e.touches[0].clientX;
          startY = e.touches[0].clientY;
          startTime = Date.now();
        }, { passive: true });

        el.addEventListener('touchend', (e) => {
          const endX = e.changedTouches[0].clientX;
          const endY = e.changedTouches[0].clientY;
          const endTime = Date.now();

          const diffX = endX - startX;
          const diffY = endY - startY;
          const duration = endTime - startTime;

          // 确认是水平滑动而非垂直滑动
          if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > 30 && duration < 300) {
            // 分发自定义事件
            el.dispatchEvent(new CustomEvent('swipe', {
              detail: {
                direction: diffX > 0 ? 'right' : 'left',
                groupIndex: groupIndex
              }
            }));
          }
        }, { passive: true });
      }
    }
  },
  components: {
    ElImage,
    ElDialog,
    ElIcon,
    ElCarousel,
    ElCarouselItem,
    ArrowRight,
    ChatDotRound,
    User,
    Position,
    Delete,
    ChatLineRound,
    StarryBackground,
    Lock,
    ChatRound,
    Plus,
    Picture,
    ZoomIn,
    Loading,
    Close,
    RelatedProductCard,
    // ProductCard,
  },
  data() {
    return {
      productId: null,
      product: {
        id: null,
        productId: null,
        name: '',
        price: 0,
        originalPrice: 0,
        mainImage: '',
        image: '',
        storeName: '',
        storeLogoUrl: '',
        description: '',
        likes: 0,
        views: 0,
        isLiked: false,
        comments: [],
        qcImages: [],
        sku: '',
        videoUrl: '' // 添加视频URL字段
      },
      // 添加图片上传相关的数据
      uploadedImages: [],
      uploadUrl: '/api/front/omg/products/upload',
      dialogImageUrl: '',
      dialogVisible: false,

      thumbnails: [],
      productGallery: [],
      qcImages: [],
      qcImagesUrls: [],
      mainImages: [], // 存储主图列表
      currentMainImageIndex: 0, // 当前主图索引
      groupCurrentIndexes: {},  // 存储每个组的当前索引
      relatedProducts: [],
      newComment: '',
      isSubmitting: false,
      platformDialogVisible: false,
      platformSettingsVisible: false,
      defaultPlatform: null,
      platforms: [], // 现在为空数组，将从API获取数据
      currency: 'USD',
      currencySymbol: '$',
      exchangeRate: 1,
      isLoggedIn: false,
      commentSortOption: 'newest',
      sortedComments: [],
      displayedComments: [], // 当前显示的评论
      commentsPerPage: 10,   // 每页显示的评论数量
      currentPage: 1,        // 当前评论页数
      isLoadingMore: false,  // 是否正在加载更多评论
      showVideo: true,      // 控制显示图片还是视频，默认显示视频
      isVideoPlaying: false, // 视频播放状态
      productSKUImages: [
        'https://agtfind.oss-eu-central-1.aliyuncs.com/logo/superbuy.png.png',
        'https://static.kakobuy.com/www/pic/kkb/logo.png',
        'https://agtfind.oss-eu-central-1.aliyuncs.com/logo/cssbuy.png.png',
        'https://agtfind.oss-eu-central-1.aliyuncs.com/logo/dai.png.webp',
        'https://www.itaobuy.com/assets/<EMAIL>',
        'https://agtfind.oss-eu-central-1.aliyuncs.com/logo/cnfans.png.png'
      ], // SKU缩略图数组
      currentSKUIndex: 0, // 当前选中的SKU索引
      currentMainImage: '', // 当前显示的主图
      isZooming: false, // 控制放大镜显示
      zoomRatio: 2.5, // 放大倍数
      zoomLensSize: 100, // 放大镜尺寸(px)
      windowWidth: window.innerWidth, // 添加窗口宽度状态
      touchStartX: 0, // 触摸起始位置
      currentGroupIndex: null, // 当前操作的组索引
      isShowingMore: false, // 是否显示"更多"卡片
      isDragging: false,
      startX: 0,
      scrollLeft: 0,
      rmb_price: '36.4',  // 默认RMB价格
      usd_price: '5.6',   // 默认USD价格
      visibleGroupsCount: window.innerWidth <= 768 ? 6 : 12, // 移动端显示6组，PC端显示12组
      isLoadingMoreGroups: false, // 是否正在加载更多图片组
      retryCount: 0, // 重试计数器
      maxRetries: 5, // 增加最大重试次数到5次
      loadingMessage: '正在加载商品信息...' // 加载状态消息
    }
  },
  created() {
    // 重置重试计数器
    this.retryCount = 0;

    // Get product ID from route params
    this.productId = this.$route.params.id;

    // Check user login status
    this.checkLoginStatus();

    // Initialize currency settings
    this.initCurrency();

    // Load default platform settings
    this.loadDefaultPlatform();

    // Load platform links from API
    this.fetchPlatformLinks();

    // Get tab parameter to load
    const tab = this.$route.query.tab;

    // Get product details first, then record view
    this.fetchProductDetails().then(() => {
      // Record view count after product info is loaded
      // this.incrementProductView();

      // Initialize sorted comments array
      this.sortComments();

      // If need to navigate to comments section
      if (tab === 'comments') {
        // Delay navigation to comments section, wait for page to load
        setTimeout(() => {
          this.scrollToComments();
        }, 500);
      }
    }).catch(err => {
      console.error('Failed to get product details, cannot record views:', err);
      this.handleFetchError();
    });

    // Listen for currency update events
    emitter.on('currency-changed', this.updateCurrency);

    // 添加窗口尺寸变化监听器
    window.addEventListener('resize', this.handleResize);
  },
  mounted() {
    // 监听主题变化
    this.themeObserver = new MutationObserver(() => {
      this.$forceUpdate(); // 强制更新组件以响应主题变化
    });

    this.themeObserver.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['data-theme']
    });
    
    // 页面加载后尝试自动播放视频
    this.$nextTick(() => {
      // 延迟一段时间后执行，确保视频元素已完全加载
      setTimeout(() => {
        if (this.product.videoUrl && this.showVideo) {
          this.autoPlayVideo();
        }
      }, 1000);
    });
  },
  beforeUnmount() {
    // Remove event listeners
    emitter.off('currency-changed', this.updateCurrency);

    // 移除窗口尺寸变化监听器
    window.removeEventListener('resize', this.handleResize);

    // 清理主题监听器
    if (this.themeObserver) {
      this.themeObserver.disconnect();
    }
  },
  methods: {
    // 获取平台对应的汇率信息
    getPlatformExchangeRate(platformName) {
      // 查找对应的平台对象
      const platform = this.platforms.find(p => p.name === platformName);
      
      // 如果找到平台且有exchangeRate字段，则使用该值生成汇率显示文本
      if (platform && platform.exchangeRate) {
        const rate = parseFloat(platform.exchangeRate).toFixed(2);
        return `¥${rate}-$1.00-€0.93`;
      }
      
      // 如果没有找到平台或exchangeRate字段，使用默认汇率文本
      return '¥6.5-$1.00-€0.93';
    },

    // 处理获取商品详情失败的错误
    handleFetchError() {
      this.retryCount++;

      console.log(`商品详情获取失败，第${this.retryCount}次重试，最大重试次数：${this.maxRetries}`);

      if (this.retryCount <= this.maxRetries) {
        // 还有重试机会，静默重试
        // 使用指数退避策略：第1次重试1秒，第2次2秒，第3次4秒，第4次8秒，第5次16秒
        const retryDelay = Math.pow(2, this.retryCount - 1) * 1000;

        // 更新加载消息，让用户知道正在重试
        this.loadingMessage = `网络不稳定，正在重试... (${this.retryCount}/${this.maxRetries})`;

        console.log(`将在${retryDelay}ms后进行第${this.retryCount}次重试`);

        setTimeout(() => {
          this.loadingMessage = '正在加载商品信息...';
          this.fetchProductDetails();
        }, retryDelay);
      } else {
        // 超过最大重试次数，显示友好的错误提示
        this.isLoading = false;
        this.loadingMessage = '正在加载商品信息...';

        ElMessage({
          type: 'error',
          message: '商品信息加载失败，请检查网络连接后刷新页面重试',
          duration: 8000,
          showClose: true
        });

        // 8秒后自动刷新页面
        setTimeout(() => {
          window.location.reload();
        }, 8000);
      }
    },

    async fetchProductDetails() {
      try {
        this.isLoading = true;

        console.log(`开始获取商品详情，商品ID: ${this.productId}`);

        // 直接调用API，不需要额外的超时控制（axios已经设置了30秒超时）
        const response = await productsApi.getProductDetail(this.productId);

        

        if (response.code === 200 && response.data) {
          // 成功获取数据，重置重试计数器
          this.retryCount = 0;

          const productData = response.data;

          // 处理评论数据，确保每个评论有必要的字段
          const processedComments = productData.comments
            ? productData.comments.map(comment => {
              // 处理评论图片数据，确保图片数组格式正确
              let commentImages = [];

              if (comment.images) {
                // 尝试解析JSON字符串
                if (typeof comment.images === 'string') {
                  try {
                    // 解析JSON字符串为数组
                    const parsedImages = JSON.parse(comment.images);

                    // 处理不同类型的解析结果
                    if (Array.isArray(parsedImages)) {
                      // 如果是URL数组，将每个URL转换为带url属性的对象
                      commentImages = parsedImages.map(img => {
                        if (typeof img === 'string') {
                          return { url: img };
                        } else if (typeof img === 'object' && img !== null && img.url) {
                          return { url: img.url };
                        }
                        return null;
                      }).filter(Boolean); // 过滤掉空值
                    }
                  } catch (e) {
                    console.error('Failed to parse comment images JSON:', e);
                    // 尝试作为单个URL处理
                    if (comment.images.trim()) {
                      commentImages = [{ url: comment.images.trim() }];
                    }
                  }
                }
                // 如果已经是数组，检查格式
                else if (Array.isArray(comment.images)) {
                  commentImages = comment.images.map(img => {
                    // 如果是简单的URL字符串
                    if (typeof img === 'string') {
                      return { url: img };
                    }
                    // 如果已经是包含url属性的对象
                    else if (typeof img === 'object' && img !== null) {
                      return { url: img.url || img.imageUrl || '' };
                    }
                    return null;
                  }).filter(img => img && img.url); // 过滤掉没有URL的图片
                }
              }

              return {
                ...comment,
                text: comment.content || comment.text,
                images: commentImages,
                isLiked: false // 初始化为false，等待后续检查
              };
            })
            : [];

          // 更新商品数据
          this.product = {
            ...productData,
            comments: processedComments,
            // 使用API返回的实际价格，不再硬编码为6
            price: productData.price || 0,
            // 暂时不设置videoUrl，后面会根据video字段决定
          };

          // 处理主图列表
          if (productData.mainImages && Array.isArray(productData.mainImages) && productData.mainImages.length > 0) {
            // 按返回数据的顺序，只保留有ossImageUrl的图片
            this.mainImages = productData.mainImages
              .filter(img => img.ossImageUrl); // 只保留有ossImageUrl的图片，不排序

            if (this.mainImages.length > 0) {
              // 设置当前主图为第一张
              this.currentMainImage = this.mainImages[0].ossImageUrl;
              this.currentMainImageIndex = 0;
            } else {
              // 如果过滤后没有图片，使用原始主图
              this.mainImages = [{
                ossImageUrl: this.product.mainImage,
                isMain: 1
              }];
              this.currentMainImage = this.product.mainImage;
              this.currentMainImageIndex = 0;
            }
          } else if (this.product.mainImage) {
            // 如果没有mainImages数组，使用单张主图
            this.mainImages = [{
              ossImageUrl: this.product.mainImage,
              isMain: 1
            }];
            this.currentMainImage = this.product.mainImage;
            this.currentMainImageIndex = 0;
          }

          // 处理商品图片
          if (this.currentMainImage) {
            this.thumbnails = [this.currentMainImage];
            // 初始化SKU图片
            this.initProductSKUImages();
          }
          
          // 处理视频URL - 直接使用video字段的值作为视频URL
          if (productData.video) {
            // 直接使用video字段的值作为视频URL
            this.product.videoUrl = productData.video;
            this.showVideo = true; // 确保显示视频而不是图片
          } else if (productData.videoUrl) {
            // 如果直接提供了videoUrl字段，作为备选
            this.product.videoUrl = productData.videoUrl;
            this.showVideo = true;
          } else {
            // 没有视频
            this.product.videoUrl = '';
            this.showVideo = false; // 默认显示图片而不是视频
          }

          // 处理QC图片
          if (this.product.qcImages && this.product.qcImages.length > 0) {
            // 确保每个QC图片对象都有有效的imageUrl
            this.qcImages = this.product.qcImages.map(img => {
              // 如果imageUrl为空，尝试使用其他可能的字段
              if (!img.imageUrl && (img.url || img.src || img.image)) {
                return {
                  ...img,
                  imageUrl: img.url || img.src || img.image
                };
              }
              return img;
            }).filter(img => img.imageUrl); // 过滤掉没有imageUrl的图片
            
            this.qcImagesUrls = this.qcImages.map(img => img.imageUrl).filter(url => url);

            // 如果没有SKU图片，使用QC图片作为SKU图片
            if (this.productSKUImages.length === 0 && this.qcImagesUrls.length > 0) {
              this.productSKUImages = [...this.qcImagesUrls];
            }
          } else {
            this.qcImages = [];
            this.qcImagesUrls = [];
          }

          // 更新浏览次数
          this.recordProductView();

          // 检查用户是否已点赞
          if (this.isLoggedIn) {
            await this.checkUserLikesForComments();
          }

          // 初始化排序后的评论列表
          this.sortComments();

          // 初始化价格显示
          this.initializePrices();

          // 加载相关商品
          this.fetchRelatedProducts();
          
          // 数据加载完成后，如果有视频且设置为显示视频，则自动播放
          if (this.product.videoUrl && this.showVideo) {
            setTimeout(() => {
              this.autoPlayVideo();
            }, 500);
          }
        }
      } catch (error) {
        console.error('Error fetching product details:', error);
        this.handleFetchError();
      } finally {
        this.isLoading = false;
      }
    },
    async fetchRelatedProducts() {
      try {
        // Call recommended products API, using current product category as parameter
        const categoryId = this.product.categoryId;
        const brandId = this.product.brandId; // 可选，如果产品有品牌ID
        
        
        // 正确传递参数：userId, categoryId, brandId
        const response = await productsApi.getRecommendProducts(
          this.$route.params.userId,
          categoryId,
          brandId,
          20 // 请求更多的相关产品，确保有足够的产品可以滚动
        );


        if (response.code === 200 && response.data) {
          // Ensure we don't show current product as recommendation
          this.relatedProducts = response.data
            .filter(item => item.productId !== this.productId);
          
          
        } else {
          // 如果接口返回失败，设置为空数组
          this.relatedProducts = [];
        }
      } catch (error) {
        console.error('Exception getting recommended products:', error);
        
        // 如果API调用失败，设置为空数组，不添加假数据
        this.relatedProducts = [];
      }
    },
    async handleLike() {
      if (this.isLoading) return

      try {
        this.isLoading = true

        // If user is not logged in, prompt login
        if (!localStorage.getItem('isLoggedIn')) {
          this.showToast('Please log in to like this product', 'warning');
          this.$router.push('/login');
          return;
        }

        if (!this.product.isLiked) {
          // Check if user has already liked this product today
          const checkResponse = await productsApi.checkLikeToday(this.productId);

          if (checkResponse.code === 200 && checkResponse.data && checkResponse.data.isLikedToday) {
            this.showToast('You have already liked this product today', 'warning');
            return;
          }

          await productsApi.likeProduct(this.productId)
          this.product.likes++
          this.product.isLiked = true
          this.showToast('Like successful')
        } else {
          await productsApi.unlikeProduct(this.productId)
          this.product.likes--
          this.product.isLiked = false
          this.showToast('Unlike successful')
        }
      } catch (error) {
        this.showToast('Failed to update like status', 'error')
        console.error('Like error:', error)
      } finally {
        this.isLoading = false
      }
    },
    async handleCommentLike(comment) {
      if (this.isLoading) return

      try {
        if (!comment.isLiked) {
          comment.likes++
          comment.isLiked = true
        } else {
          comment.likes--
          comment.isLiked = false
        }
      } catch (error) {
        console.error('Error updating comment like:', error)
      }
    },
    async submitComment() {
      if (!this.newComment.trim()) {
        this.showToast('Please enter a comment', 'warning')
        return
      }

      try {
        this.isLoading = true

        const commentData = {
          content: this.newComment.trim(),
          rating: 5,
          userId: this.userId
        }

        await productsApi.addProductComment(this.productId, commentData)

        // Add to local comment list
        const newCommentObj = {
          username: 'You', // Actual should use login user information
          avatar: 'https://via.placeholder.com/40?text=YOU',
          text: this.newComment,
          date: new Date(),
          likes: 0,
          isLiked: false
        }

        this.product.comments.unshift(newCommentObj)
        this.newComment = ''
        this.showToast('Comment added successfully')

      } catch (error) {
        this.showToast('Failed to add comment', 'error')
        console.error('Error adding comment:', error)
      } finally {
        this.isLoading = false
      }
    },
    formatPrice(price) {
      if (!price) return '0.00'
      // 现在后台录入的是人民币价格，需要转换为美元显示
      const rmbPrice = parseFloat(price);

      // 获取当前汇率
      let exchangeRate = 6.5; // 默认汇率
      if (this.defaultPlatform && this.defaultPlatform.exchangeRate) {
        exchangeRate = parseFloat(this.defaultPlatform.exchangeRate);
      }

      // 将人民币价格转换为美元价格
      const usdPrice = rmbPrice / exchangeRate;
      return usdPrice.toFixed(2);
    },
    formatDate(date) {
      if (!date) return ''
      return new Date(date).toLocaleDateString() + ' ' + new Date(date).toLocaleTimeString()
    },
    showToast(message, type = 'success') {
      ElMessage({
        message,
        type,
        duration: 2000
      })
    },
    updateCurrency(currencyInfo) {
      this.currentCurrency = currencyInfo.currency
      this.currencySymbol = currencyInfo.symbol
      this.exchangeRate = currencyInfo.rate
    },
    initCurrency() {
      // Try to get currency info from local storage
      const savedCurrency = localStorage.getItem('preferredCurrency')
      if (savedCurrency) {
        const currencySymbols = {
          'USD': '$',
          'CNY': '¥',
          'EUR': '€',
          'GBP': '£',
          'JPY': '¥'
        }
        const exchangeRates = {
          'USD': 1,
          'CNY': 7.2,
          'EUR': 0.93,
          'GBP': 0.79,
          'JPY': 150.5
        }

        if (currencySymbols[savedCurrency]) {
          this.currentCurrency = savedCurrency
          this.currencySymbol = currencySymbols[savedCurrency]
          this.exchangeRate = exchangeRates[savedCurrency]
        }
      }
    },
    goBack() {
      this.$router.go(-1)
    },
    scrollToComments() {
      document.getElementById('comments').scrollIntoView({
        behavior: 'smooth'
      })
    },
    calculateDiscount(currentPrice, originalPrice) {
      return Math.round((1 - currentPrice / originalPrice) * 100)
    },
    addComment() {
      // Again check login status
      if (!this.isLoggedIn) {
        this.goToLogin();
        return;
      }

      if (!this.newComment.trim() && this.uploadedImages.length === 0) {
        this.showToast(this.$t('product.emptyCommentError', 'Please enter text or upload images'), 'warning');
        return;
      }

      // Get user information
      const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
      const username = userInfo.userName || 'User';
      const avatar = userInfo.userAvatar || 'https://pub-house.oss-cn-beijing.aliyuncs.com/images/2024/11/12/1731348290089.jpg';
      const userId = userInfo.userId || userInfo.id; // 确保获取用户ID

      if (!userId) {
        this.showToast('用户信息不完整，请重新登录', 'error');
        this.goToLogin();
        return;
      }

      this.isSubmitting = true;

      // 简化图片数据，只提取URL数组
      const imageUrls = this.uploadedImages.map(img => img.url);

      // 创建评论数据，包含图片URL信息
      const commentData = {
        content: this.newComment.trim(),
        rating: 5,
        userId: userId,
        // 直接使用URL数组作为图片数据
        images: JSON.stringify(imageUrls)
      };

      

      // 调用API添加评论
      commentsApi.addProductComment(this.productId, commentData)
        .then(response => {
          

          // 假设API返回的结果包含新创建的评论ID和时间戳
          const commentId = response.data?.id || response.data?.commentId || Date.now();
          const createdAt = response.data?.createdAt || response.data?.createTime || new Date();

          // 将新评论添加到列表顶部
          const newComment = {
            id: commentId,        // 确保设置了ID
            username: username,
            avatarUrl: avatar,
            text: this.newComment.trim(),
            createdAt: createdAt,
            likes: 0,
            isLiked: false,
            userId: userId,       // 添加用户ID到UI展示的数据
            images: imageUrls.map(url => ({ url }))  // 简化为只有URL的对象数组
          };

          
          this.product.comments.unshift(newComment);

          // 重新排序评论
          this.sortComments();

          this.newComment = '';
          this.uploadedImages = []; // 清空已上传图片

          // 重置上传组件
          if (this.$refs.uploadRef) {
            this.$refs.uploadRef.clearFiles();
          }

          this.showToast(this.$t('product.commentSuccess'));
        })
        .catch(error => {
          console.error('发布评论错误:', error);
          this.showToast(this.$t('product.commentError'), 'error');
        })
        .finally(() => {
          this.isSubmitting = false;
        });
    },
    likeComment(index, event) {
      // 如果传入了事件对象，阻止默认行为
      if (event) {
        event.preventDefault();
        event.stopPropagation();
      }
      
      // 检查用户登录状态
      if (!this.isLoggedIn) {
        this.showToast(this.$t('product.loginToLike'), 'warning');
        this.goToLogin();
        return;
      }

      if (this.isLoading) return;

      // 获取评论对象的引用
      const comment = this.sortedComments[index];

      // 在原始数组中找到相同的评论对象
      const originalCommentIndex = this.product.comments.findIndex(c =>
        (c.id === comment.id) || (c.commentId === comment.commentId)
      );

      if (originalCommentIndex === -1) {
        console.error('无法在原始数组中找到评论');
        return;
      }

      // 获取原始评论引用
      const originalComment = this.product.comments[originalCommentIndex];

    
      
      this.isLoading = true;

      try {
        // 获取当前登录用户信息
        const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
        const userId = userInfo.userId || userInfo.id;

        if (!userId) {
          this.showToast('User information is incomplete. Please log in again', 'error');
          this.goToLogin();
          return;
        }

        if (comment.isLiked) {
          // 取消点赞
          if (comment.commentId) {
            
            commentsApi.unlikeComment(comment.commentId, userId)
              .then(() => {
                

                // 更新两个数组中的评论对象
                comment.likes = Math.max(0, comment.likes - 1);
                comment.isLiked = false;
                originalComment.likes = Math.max(0, originalComment.likes - 1);
                originalComment.isLiked = false;

                this.showToast(this.$t('product.unlikeSuccess'));
                // 重新排序评论
                this.sortComments();
              })
              .catch(error => {
                console.error('取消点赞失败:', error);
                this.showToast(this.$t('product.unlikeError'), 'error');
              })
              .finally(() => {
                this.isLoading = false;
              });
          } else {
            // 对于本地新评论，仅更新UI
            

            // 更新两个数组中的评论对象
            comment.likes = Math.max(0, comment.likes - 1);
            comment.isLiked = false;
            originalComment.likes = Math.max(0, originalComment.likes - 1);
            originalComment.isLiked = false;

            this.showToast(this.$t('product.unlikeSuccess'));
            // 重新排序评论
            this.sortComments();
            this.isLoading = false;
          }
        } else {
          // 添加点赞
          if (comment.commentId) {
            
            commentsApi.likeComment(comment.commentId, userId)
              .then(() => {
                

                // 更新两个数组中的评论对象
                comment.likes++;
                comment.isLiked = true;
                originalComment.likes++;
                originalComment.isLiked = true;

                this.showToast(this.$t('product.likeSuccess'));
                // 重新排序评论
                this.sortComments();
              })
              .catch(error => {
                console.error('点赞失败:', error);
                this.showToast(this.$t('product.likeError'), 'error');
              })
              .finally(() => {
                this.isLoading = false;
              });
          } else {
            // 对于本地新评论，仅更新UI


            // 更新两个数组中的评论对象
            comment.likes++;
            comment.isLiked = true;
            originalComment.likes++;
            originalComment.isLiked = true;

            this.showToast(this.$t('product.likeSuccess'));
            // 重新排序评论
            this.sortComments();
            this.isLoading = false;
          }
        }
      } catch (error) {
        console.error('点赞操作处理异常:', error);
        this.showToast(this.$t('product.likeOperationError'), 'error');
        this.isLoading = false;
      }
    },
    replyToComment(index) {
      const username = this.sortedComments[index].username
      this.newComment = `@${username} `
      this.$nextTick(() => {
        document.querySelector('.comment-form textarea').focus()
      })
    },
    goToProductDetail(productId) {
      if (this.isLoading) return; // 正在加载时禁止切换
      if (this.$route.params.id === String(productId)) {
        // 如果点击的是当前商品，强制刷新
        this.fetchProductDetails();
        window.scrollTo({ top: 0, behavior: 'smooth' });
      } else {
        this.$router.push({ name: 'product-detail', params: { id: productId } }).then(() => {
          this.fetchProductDetails();
          window.scrollTo({ top: 0, behavior: 'smooth' });
        });
      }
    },
    showPlatforms() {
      this.platformDialogVisible = true
    },
    showPlatformSettings() {
      this.platformSettingsVisible = true
    },
    setDefaultPlatform(platform) {
      this.defaultPlatform = platform
      localStorage.setItem('defaultPlatform', JSON.stringify(platform))
      
      // 现在后台录入的是人民币价格
      const rmbPrice = this.product.price || 0;

      // 如果平台有exchangeRate字段，使用该值更新价格
      if (platform && platform.exchangeRate) {
        const rate = parseFloat(platform.exchangeRate);

        // 添加详细日志，查看计算过程
        console.log('设置默认平台价格计算:', {
          平台名称: platform.name,
          汇率值: rate,
          人民币价格: rmbPrice,
          计算过程: `${rmbPrice} ÷ ${rate} = ${rmbPrice / rate}`,
          转换后USD价格: (rmbPrice / rate).toFixed(2)
        });

        this.rmb_price = rmbPrice.toFixed(2); // 直接显示人民币价格，保留两位小数
        this.usd_price = (rmbPrice / rate).toFixed(2); // 人民币除以汇率得到美元价格，保留两位小数
      } else {
        // 否则根据平台名称设置汇率
        this.updateExchangeRateByPlatform(platform.name);
      }
      
      this.showToast(`${platform.name} set as default platform`)
      
      // 只有当设置对话框打开时才关闭它
      if (this.platformSettingsVisible) {
        this.platformSettingsVisible = false
      }
    },
    loadDefaultPlatform() {
      const saved = localStorage.getItem('defaultPlatform')
      if (saved) {
        try {
          const parsedPlatform = JSON.parse(saved)
          // 验证平台数据是否完整
          if (parsedPlatform && parsedPlatform.id && parsedPlatform.name && parsedPlatform.urltemplate) {
            // 确保exchangeRate字段存在，如果不存在则使用默认值
            if (!parsedPlatform.exchangeRate) {
              parsedPlatform.exchangeRate = '6.5';
            }
            this.defaultPlatform = parsedPlatform
          } else {
            console.warn('Invalid default platform data, removing from localStorage')
            localStorage.removeItem('defaultPlatform')
          }
        } catch (e) {
          console.error('Failed to load default platform', e)
          localStorage.removeItem('defaultPlatform')
        }
      }
    },
    goToPlatform(urlTemplate) {
      // 检查urlTemplate是否有效
      if (!urlTemplate || typeof urlTemplate !== 'string') {
        ElMessage({
          message: this.$t('product.invalidPlatformLink', '购买平台链接无效'),
          type: 'error',
          duration: 3000
        });
        return;
      }

      // 获取SKU ID
      const skuId = this.product.sku || '';

      // 添加调试信息
      console.log('跳转平台调试信息:', {
        productId: this.product.productId,
        productSku: this.product.sku,
        skuId: skuId,
        urlTemplate: urlTemplate,
        productData: this.product
      });

      // 如果没有SKU，显示错误提示
      if (!skuId) {
        ElMessage({
          message: this.$t('product.skuNotAvailable', '商品SKU不可用，无法跳转到购买平台'),
          type: 'error',
          duration: 3000
        });
        return;
      }

      // 替换URL模板中的{sku}为实际的SKU ID
      const url = urlTemplate.replace('{sku}', skuId);

      // 在新标签页打开URL
      window.open(url, '_blank');
      this.platformDialogVisible = false;

      ElMessage({
        message: this.$t('product.redirectingToPlatform', '正在跳转到购买平台...'),
        type: 'success',
        duration: 2000
      });
    },
    buyNow() {
      if (!this.defaultPlatform) {
        this.platformDialogVisible = true;
        return;
      }

      // 使用默认平台的URL模板
      const urlTemplate = this.defaultPlatform.urltemplate || this.defaultPlatform.url;

      // 检查URL模板是否有效
      if (!urlTemplate || typeof urlTemplate !== 'string') {
        ElMessage({
          message: this.$t('product.invalidPlatformLink', '购买平台链接无效'),
          type: 'error',
          duration: 3000
        });
        return;
      }

      // 获取SKU ID
      const skuId = this.product.sku || '';

      // 添加调试信息
      console.log('立即购买调试信息:', {
        defaultPlatform: this.defaultPlatform,
        productId: this.product.productId,
        productSku: this.product.sku,
        skuId: skuId,
        urlTemplate: urlTemplate,
        productData: this.product
      });

      // 如果没有SKU，显示错误提示
      if (!skuId) {
        ElMessage({
          message: this.$t('product.skuNotAvailable', '商品SKU不可用，无法跳转到购买平台'),
          type: 'error',
          duration: 3000
        });
        return;
      }

      // 替换URL模板中的{sku}为实际的SKU ID
      const finalUrl = urlTemplate.replace('{sku}', skuId);

      // 打开新窗口
      window.open(finalUrl, '_blank');

      ElMessage({
        message: this.$t('product.redirectingToPlatform', '正在跳转到购买平台...'),
        type: 'success',
        duration: 2000
      });
    },
    scrollToProductMain() {
      document.getElementById('product-main').scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      })
    },
    // Method to increase view count, ensuring correct execution
    async incrementProductView() {
      if (!this.productId) {
        console.error('Cannot record view: productId is undefined');
        return;
      }

      

      // Use delay to ensure page is loaded
      setTimeout(async () => {
        try {
          
          const response = await productsApi.incrementProductViews(this.productId);
          

          // If API call successful, update local view display
          if (response && response.code === 200) {
            // Update local view count (optional)
            if (this.product) {
              this.product.views = (this.product.views || 0) + 1;
            }
          }
        } catch (error) {
          console.error('Failed to record view:', error);
          // Continue execution, doesn't affect page display
        }
      }, 2000); // Record view count after 2 seconds
    },
    goToLogin() {
      this.$router.push({
        path: '/login',
        query: { redirect: `/product/${this.productId}` }
      });
    },
    // Check user login status
    checkLoginStatus() {
      this.isLoggedIn = localStorage.getItem('isLoggedIn') === 'true';
    },
    isUserComment(comment) {
      const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
      return comment.username === userInfo.userName;
    },
    deleteComment(index) {
      if (!this.isLoggedIn) return;

      // 获取排序后的评论对象引用
      const comment = this.sortedComments[index];

      // 在原始数组中找到相同的评论对象
      const originalCommentIndex = this.product.comments.findIndex(c =>
        (c.id === comment.id) || (c.commentId === comment.commentId)
      );

      if (originalCommentIndex === -1) {
        console.error('无法在原始数组中找到评论');
        return;
      }

      try {
        this.isLoading = true;

        // 如果评论有ID，调用API进行删除
        if (comment.commentId) {
          commentsApi.deleteComment(comment.commentId)
            .then(() => {
              // 从原始评论列表中移除
              this.product.comments.splice(originalCommentIndex, 1);
              // 重新排序评论列表（会自动移除sortedComments中的对应项）
              this.sortComments();

              this.showToast(this.$t('product.deleteSuccess'));
            })
            .catch(error => {
              console.error('Failed to delete comment:', error);
              this.showToast(this.$t('product.deleteError'), 'error');
            })
            .finally(() => {
              this.isLoading = false;
            });
        } else {
          // 如果评论没有ID（本地添加未同步到服务器的评论）
          // 从原始评论列表中移除
          this.product.comments.splice(originalCommentIndex, 1);
          // 重新排序评论列表
          this.sortComments();

          this.showToast(this.$t('product.deleteSuccess'));
          this.isLoading = false;
        }
      } catch (error) {
        console.error('Error deleting comment:', error);
        this.showToast(this.$t('product.deleteOperationError'), 'error');
        this.isLoading = false;
      }
    },
    // 添加记录商品浏览次数的方法
    recordProductView() {
      // Use delay to ensure page is loaded
      setTimeout(async () => {
        try {
          
          const response = await productsApi.incrementProductViews(this.productId);
          

          // If API call successful, update local view display
          if (response && response.code === 200) {
            // Update local view count (optional)
            if (this.product) {
              this.product.views = (this.product.views || 0) + 1;
            }
          }
        } catch (error) {
          console.error('Failed to record view:', error);
          // Continue execution, doesn't affect page display
        }
      }, 2000); // Record view count after 2 seconds
    },
    // 新增方法：检查用户对所有评论的点赞状态
    async checkUserLikesForComments() {
      try {
        // 获取当前登录用户信息
        const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
        const userId = userInfo.userId || userInfo.id;

        if (!userId) {
          console.warn('No user ID found, skipping like status check');
          return;
        }

        // 获取所有评论ID
        const commentIds = this.product.comments
          .filter(comment => comment.commentId)
          .map(comment => comment.commentId);

        if (commentIds.length === 0) {
          return;
        }

        // 调用后端API检查点赞状态
        const response = await commentsApi.checkCommentsLikeStatus({
          userId: userId,
          commentIds: commentIds
        });

        if (response.code === 200 && response.data) {
          // 更新评论的点赞状态
          const likedComments = response.data || [];

          this.product.comments.forEach(comment => {
            if (comment.commentId) {
              // 如果评论ID在返回的数据中存在，说明用户已点赞
              comment.isLiked = likedComments.some(likedComment =>
                likedComment.commentId === comment.commentId
              );
            }
          });
        }
      } catch (error) {
        console.error('Error checking comments like status:', error);
      }
    },
    // 获取当前用户头像
    getUserAvatar() {
      const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
      return userInfo.userAvatar || 'https://pub-house.oss-cn-beijing.aliyuncs.com/images/2024/11/12/1731348290089.jpg';
    },
    // 确认删除评论
    confirmDeleteComment(index) {
      ElMessageBox.confirm(
        this.$t('product.deleteConfirmMessage'),
        this.$t('product.deleteConfirmTitle'),
        {
          confirmButtonText: this.$t('common.confirm'),
          cancelButtonText: this.$t('common.cancel'),
          type: 'warning',
          customClass: 'custom-message-box',
          distinguishCancelAndClose: true,
        }
      )
        .then(() => {
          this.deleteComment(index);
        })
        .catch(() => {
          // 用户取消操作
        });
    },
    // 评论排序方法
    sortComments() {
      if (this.commentSortOption === 'newest') {
        this.sortedComments = [...this.product.comments].sort((a, b) =>
          new Date(b.createdAt || b.createTime || 0) - new Date(a.createdAt || a.createTime || 0)
        );
      } else if (this.commentSortOption === 'popular') {
        this.sortedComments = [...this.product.comments].sort((a, b) =>
          (b.likes || 0) - (a.likes || 0)
        );
      }

      // 重置当前页码和已显示评论
      this.currentPage = 1;
      this.updateDisplayedComments();
    },

    // 更新显示的评论
    updateDisplayedComments() {
      const endIndex = this.currentPage * this.commentsPerPage;
      this.displayedComments = this.sortedComments.slice(0, endIndex);
    },

    // 加载更多评论
    loadMoreComments() {
      this.isLoadingMore = true;

      // 模拟网络延迟
      setTimeout(() => {
        this.currentPage += 1;
        this.updateDisplayedComments();
        this.isLoadingMore = false;
      }, 600);
    },

    // 初始化产品SKU图片
    initProductSKUImages() {
      // 初始化SKU图片数组
      this.productSKUImages = [];

      // 首先添加所有主图，只使用ossImageUrl
      if (this.mainImages && this.mainImages.length > 0) {
        this.mainImages.forEach(img => {
          if (img.ossImageUrl && !this.productSKUImages.includes(img.ossImageUrl)) {
            this.productSKUImages.push(img.ossImageUrl);
          }
        });
      } else if (this.product.mainImage) {
        // 如果没有mainImages数组，使用单张主图
        this.productSKUImages.push(this.product.mainImage);
      }

      // 如果产品没有图片，检查是否有QC图片
      if (this.productSKUImages.length === 0 && this.qcImagesUrls && this.qcImagesUrls.length > 0) {
        this.productSKUImages = [...this.qcImagesUrls];
      }

      // 如果仍然没有图片，添加一个默认图片
      if (this.productSKUImages.length === 0) {
        this.productSKUImages.push('https://pub-house.oss-cn-beijing.aliyuncs.com/images/default-product.jpg');
      }

      // 设置当前主图
      this.currentMainImage = this.productSKUImages[0];
      this.currentSKUIndex = 0;
    },

    // 处理主图轮播变化
    handleMainImageChange(index) {
      this.currentMainImageIndex = index;
      if (this.mainImages && this.mainImages[index] && this.mainImages[index].ossImageUrl) {
        this.currentMainImage = this.mainImages[index].ossImageUrl;
      }
    },



    // 选择SKU图片 (保留方法以防其他地方调用)
    selectSKUImage(index) {
      // 由于移除了缩略图，这个方法现在主要用于兼容性
      if (this.mainImages.length > 1 && index < this.mainImages.length) {
        this.currentMainImageIndex = index;
        if (this.$refs.mainCarousel) {
          this.$refs.mainCarousel.setActiveItem(index);
        }
      }
    },
    scrollToCommentForm() {
      document.getElementById('comments').scrollIntoView({
        behavior: 'smooth'
      })
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    handleRemove(file) {
      // 从已上传图片数组中删除该图片
      const fileIndex = this.uploadedImages.findIndex(item => item.uid === file.uid);
      if (fileIndex !== -1) {
        this.uploadedImages.splice(fileIndex, 1);
      }
    },
    handleUploadSuccess(response, file) {
      
      try {
        // 检查接口返回的数据结构，适应实际后端返回格式
        if (response.code === 200) {
          // 提取图片URL，简化处理逻辑
          let imageUrl = '';

          // 如果返回的是JSON字符串，尝试解析
          if (typeof response.data === 'string' && response.data.startsWith('{')) {
            try {
              const parsedData = JSON.parse(response.data);
              imageUrl = parsedData.url || parsedData.imageUrl || '';
            } catch (e) {
              // 如果解析失败，则使用原始数据
              imageUrl = response.data;
            }
          } else {
            // 从各种可能的位置提取URL
            imageUrl = response.data || response.url || '';

            // 如果是对象，尝试提取URL属性
            if (typeof imageUrl === 'object' && imageUrl !== null) {
              imageUrl = imageUrl.url || imageUrl.imageUrl || '';
            }
          }

          // 确保URL不为空
          if (imageUrl) {
            // 构建简化的图片对象，只保留必要信息
            this.uploadedImages.push({
              uid: file.uid, // 保留uid用于内部识别和删除
              url: imageUrl
            });

            this.showToast(this.$t('product.uploadSuccess', 'Image uploaded successfully'));
            
          } else {
            this.handleUploadError(new Error('No image URL found in response'), file);
          }
        } else {
          this.handleUploadError(new Error(response.message || 'Unknown error'), file);
        }
      } catch (error) {
        console.error('Error processing upload response:', error);
        this.handleUploadError(error, file);
      }
    },
    handleUploadError(error, file) {
      console.error('Upload error:', error, file);
      this.showToast(this.$t('product.uploadError', 'Failed to upload image'), 'error');

      // 从上传列表中移除失败的文件
      if (this.$refs.uploadRef) {
        this.$refs.uploadRef.handleRemove(file);
      }
    },
    
    // 触发上传按钮点击
    triggerUploadClick() {
      // 查找上传组件的input元素并点击它
      if (this.$refs.uploadRef) {
        const uploadInput = this.$refs.uploadRef.$el.querySelector('input[type="file"]');
        if (uploadInput) {
          uploadInput.click();
        }
      }
    },
    
    // 通过索引删除已上传图片
    handleRemoveByIndex(index) {
      if (index >= 0 && index < this.uploadedImages.length) {
        // 查找对应的文件
        const fileToRemove = this.$refs.uploadRef.uploadFiles.find(
          file => file.url === this.uploadedImages[index].url
        );
        
        // 如果找到文件，调用上传组件的移除方法
        if (fileToRemove) {
          this.$refs.uploadRef.handleRemove(fileToRemove);
        } else {
          // 如果找不到文件，直接从数组中移除
          this.uploadedImages.splice(index, 1);
        }
      }
    },
    beforeImageUpload(file) {
      const isImage = file.type.startsWith('image/');
      const isLt2M = file.size / 1024 / 1024 < 5;

      if (!isImage) {
        this.showToast(this.$t('product.formatError', 'You can only upload image files!'), 'error');
        return false;
      }

      if (!isLt2M) {
        this.showToast(this.$t('product.sizeError', 'Image size cannot exceed 5MB!'), 'error');
        return false;
      }

      return true;
    },
    getCommentImageUrls(comment) {
      if (!comment.images || !comment.images.length) return [];

      // 提取并过滤有效的图片URL
      const validUrls = comment.images.map(image => {
        return typeof image === 'string' ? image : (image.url || '');
      }).filter(url => url); // 过滤掉空URL

      // 返回处理后的URL数组
      return validUrls;
    },
    // 添加显示图片预览信息的方法
    getImagePreviewTitle(index, total, username) {
      return `${username}'s photo · ${index + 1}/${total}`;
    },
    // 处理图片放大功能
    handleImageZoom(event) {
      // 启用放大效果
      this.isZooming = true;

      // 获取图片元素和相关DOM节点
      // 对于轮播图，需要获取当前活动的图片元素
      let imgElement;
      if (this.mainImages.length > 1) {
        // 轮播模式：获取当前活动的图片元素
        const activeCarouselItem = event.currentTarget.closest('.main-image-wrapper');
        imgElement = activeCarouselItem ? activeCarouselItem.querySelector('img') : null;
      } else {
        // 单图模式：使用ref获取图片元素
        imgElement = this.$refs.mainImage;
      }

      const lens = event.currentTarget.querySelector('.image-zoom-lens');
      const result = document.querySelector('.zoom-container .image-zoom-result');

      if (!imgElement || !lens || !result) return;

      // 计算鼠标相对于图片的位置
      const rect = imgElement.getBoundingClientRect();
      const x = event.clientX - rect.left;
      const y = event.clientY - rect.top;

      // 计算镜片位置和尺寸
      const lensSize = this.zoomLensSize;
      const lensHalfSize = lensSize / 2;

      // 确保镜片不超出图片边界
      let lensLeft = Math.max(0, Math.min(x - lensHalfSize, rect.width - lensSize));
      let lensTop = Math.max(0, Math.min(y - lensHalfSize, rect.height - lensSize));

      // 更新镜片位置
      lens.style.width = `${lensSize}px`;
      lens.style.height = `${lensSize}px`;
      lens.style.left = `${lensLeft}px`;
      lens.style.top = `${lensTop}px`;
      lens.style.opacity = '1';

      // 设置放大结果区域 - 使用当前主图而不是DOM元素的src
      result.style.backgroundImage = `url(${this.currentMainImage || this.product.mainImage || this.product.image})`;

      // 增加放大倍率，使图像更清晰
      const zoomRatio = 2.5;

      result.style.backgroundSize = `${rect.width * zoomRatio}px ${rect.height * zoomRatio}px`;

      // 计算背景位置，确保镜头位置对应放大区域的中心
      // 修正计算方法，确保放大区域正确对应鼠标位置
      const percentX = lensLeft / (rect.width - lensSize);
      const percentY = lensTop / (rect.height - lensSize);

      const bgX = percentX * (rect.width * zoomRatio - result.offsetWidth);
      const bgY = percentY * (rect.height * zoomRatio - result.offsetHeight);

      result.style.backgroundPosition = `-${bgX}px -${bgY}px`;
    },
    resetZoom() {
      this.isZooming = false;

      // 重置镜片和结果区域
      const lens = document.querySelector('.main-product-image .image-zoom-lens');

      if (lens) {
        lens.style.opacity = '0';
      }

      // 不需要在这里处理result的隐藏，因为我们使用v-show绑定到isZooming
    },
    goToProductList() {
      this.$router.push({ path: '/products' });
    },
    updateGroupIndex(groupIndex, currentIndex) {
      // 直接赋值，不使用$set
      this.groupCurrentIndexes[groupIndex] = currentIndex;
    },
    getCurrentIndex(groupIndex) {
      return (this.groupCurrentIndexes[groupIndex] || 0) + 1;
    },
    handleResize() {
      this.windowWidth = window.innerWidth;
      
      // 根据窗口宽度动态调整QC图片组显示数量
      // 注意：这里只在窗口宽度跨越移动端边界时才更新，避免重置已加载的组
      const isMobileNow = this.windowWidth <= 768;
      const wasMobile = this.visibleGroupsCount <= 6;
      
      // 如果从PC变为移动端，且当前显示超过6组，则限制为6组
      if (isMobileNow && !wasMobile && this.visibleGroupsCount > 6) {
        this.visibleGroupsCount = 6;
      } 
      // 如果从移动端变为PC端，且当前显示为默认的6组，则增加到12组
      else if (!isMobileNow && wasMobile && this.visibleGroupsCount === 6) {
        this.visibleGroupsCount = 12;
      }
    },
    
    // 切换视频播放状态
    toggleVideo() {
      this.showVideo = true;
      
      // 如果切换到视频，需要确保视频元素存在
      this.$nextTick(() => {
        const videoElement = this.$refs.productVideo;
        if (videoElement) {
          if (this.isVideoPlaying) {
            videoElement.pause();
            this.isVideoPlaying = false;
          } else {
            videoElement.play().catch(err => {
              console.error('视频播放失败:', err);
              // 自动播放可能被浏览器阻止，这是正常的
            });
          }
        }
      });
    },
    
    // 自动播放视频的方法
    autoPlayVideo() {
      const videoElement = this.$refs.productVideo;
      if (videoElement) {
        // 设置视频属性以提高自动播放成功率
        videoElement.muted = true; // 静音播放更容易被允许
        videoElement.playsInline = true; // 防止全屏播放
        
        // 尝试播放
        videoElement.play()
          .then(() => {
            
            this.isVideoPlaying = true;
          })  
          .catch(err => {
            console.warn('视频自动播放失败(这可能是正常的浏览器安全策略):', err);
            // 自动播放被阻止是正常的，不需要特别处理
          });
      } else {
        console.warn('视频元素不存在，无法自动播放');
      }
    },
    
    // 加载更多QC图片组
    loadMoreGroups() {
      if (this.isLoadingMoreGroups || !this.hasMoreGroups) return;
      
      this.isLoadingMoreGroups = true;
      
      // 模拟加载延迟，实际应用中可以移除
      setTimeout(() => {
        // 移动端每次增加6组，PC端每次增加12组
        const increment = this.windowWidth <= 768 ? 6 : 12;
        this.visibleGroupsCount += increment;
        this.isLoadingMoreGroups = false;
      }, 800);
    },
    handleTouchStart(event, groupIndex) {
      // 存储初始触摸位置
      this.touchStartX = event.touches[0].clientX;
      this.currentGroupIndex = groupIndex;
    },
    handleTouchEnd(event, groupIndex) {
      if (!this.isMobile) return; // 仅在移动端启用

      const touchEndX = event.changedTouches[0].clientX;
      const diff = touchEndX - this.touchStartX;
      const currentIndex = this.groupCurrentIndexes[groupIndex] || 0;
      const maxIndex = this.qcImageGroups[groupIndex].length - 1;

      // 检测左右滑动
      if (Math.abs(diff) >= 50) { // 确认是有效的滑动
        try {
          // 获取轮播实例数组
          const carousels = this.$refs.carousels;
          if (Array.isArray(carousels) && carousels.length > groupIndex) {
            const carousel = carousels[groupIndex];

            if (diff > 0 && currentIndex > 0) {
              // 向右滑动，显示上一张
              carousel.prev();
            } else if (diff < 0 && currentIndex < maxIndex) {
              // 向左滑动，显示下一张
              carousel.next();
            }
          }
        } catch (error) {
          console.error('轮播控制错误:', error);
        }
      }
    },
    handleSwipeEvent(event) {
      const { direction, groupIndex } = event.detail;

      try {
        // 获取轮播实例数组
        const carousels = this.$refs.carousels;
        if (Array.isArray(carousels) && carousels.length > groupIndex) {
          const carousel = carousels[groupIndex];
          const currentIndex = this.groupCurrentIndexes[groupIndex] || 0;
          const maxIndex = this.qcImageGroups[groupIndex].length - 1;

          if (direction === 'right' && currentIndex > 0) {
            carousel.prev();
          } else if (direction === 'left' && currentIndex < maxIndex) {
            carousel.next();
          }
        }
      } catch (error) {
        console.error('轮播控制错误:', error);
      }
    },
    handleImageClick() {
      // 处理图片点击事件，打开预览
      // 注意：点击时预览已由el-image组件自动处理
    },
    startDrag(e) {
      this.isDragging = true;
      const container = this.$el.querySelector('.related-products-row');
      if (!container) return;
      this.startX = e.pageX - container.offsetLeft;
      this.scrollLeft = container.scrollLeft;
    },
    onDrag(e) {
      if (!this.isDragging) return;
      e.preventDefault();
      const container = this.$el.querySelector('.related-products-row');
      if (!container) return;
      const x = e.pageX - container.offsetLeft;
      const walk = (x - this.startX) * 1.5; // 降低滚动速度
      container.scrollLeft = this.scrollLeft - walk;
    },
    stopDrag() {
      this.isDragging = false;
    },
    // 触摸设备支持
    startTouch(e) {
      this.isDragging = true;
      const container = this.$el.querySelector('.related-products-row');
      if (!container) return;
      this.startX = e.touches[0].pageX - container.offsetLeft;
      this.scrollLeft = container.scrollLeft;
    },
    onTouch(e) {
      if (!this.isDragging) return;
      const container = this.$el.querySelector('.related-products-row');
      if (!container) return;
      const x = e.touches[0].pageX - container.offsetLeft;
      const walk = (x - this.startX) * 1.5; // 降低滚动速度
      container.scrollLeft = this.scrollLeft - walk;
    },
    stopTouch() {
      this.isDragging = false;
    },
    async fetchPlatformLinks() {
      try {
        const response = await platformLinksApi.getOPlatformLinks();
        
        if (response.code === 200 && response.data && Array.isArray(response.data)) {
          // 处理API返回的数据，确保每个平台对象都有正确的字段
          this.platforms = response.data.map(platform => {
            return {
              ...platform,
              // 确保logo字段存在，用于兼容现有代码
              logo: platform.logoUrl || platform.logo || '',
              // 保持urltemplate字段不变，这是正确的字段名
              urltemplate: platform.urltemplate || platform.url || '',
              // 确保exchangeRate字段存在，如果不存在则使用默认值
              exchangeRate: platform.exchangeRate || '6.5'
            };
          });
          
          // 验证当前默认平台是否在新数据中存在
          if (this.defaultPlatform) {
            const validPlatform = this.platforms.find(p => p.id === this.defaultPlatform.id);
            if (!validPlatform) {
              console.warn('当前默认平台在新数据中不存在，清除默认设置');
              this.defaultPlatform = null;
              localStorage.removeItem('defaultPlatform');
            } else {
              // 更新默认平台数据为最新的API数据
              this.defaultPlatform = validPlatform;
              localStorage.setItem('defaultPlatform', JSON.stringify(validPlatform));
            }
          }
          
          // 如果没有默认平台，设置第一个平台为默认
          if (!this.defaultPlatform && this.platforms.length > 0) {
            this.defaultPlatform = this.platforms[0];
            localStorage.setItem('defaultPlatform', JSON.stringify(this.defaultPlatform));
            
            // 初始化价格显示
            this.initializePrices();
          }
        } else {
          console.warn('平台链接API返回数据格式不正确:', response);
        }
      } catch (error) {
        console.error('获取平台链接失败:', error);
      }
    },
    transformProductForCard(product) {
      // 处理图片URL，确保取到正确的图片
      let imageUrl = '';
      if (product.mainImage) {
        // 如果mainImage是逗号分隔的多个URL，取第一个
        imageUrl = product.mainImage.split(',')[0].trim();
      } else if (product.image) {
        // 如果有image字段，使用image
        imageUrl = product.image;
      }
      
      
      return {
        ...product,
        id: product.productId,
        image: imageUrl,
        price: product.price,
        originalPrice: product.originalPrice,
        likes: product.likes || 0,
        views: product.views || 0,
        comments: product.comments || [],
        isLiked: product.isLiked || false,
        isCollected: product.isCollected || false
      };
    },
    handleProductCardClick(product, action) {
      if (action === 'details') {
        this.goToProductDetail(product.productId || product.id);
      } else if (action === 'views') {
        // 处理查看次数点击
      } else if (action === 'comments') {
        // 处理评论点击，可以跳转到产品详情页的评论区
        this.goToProductDetail(product.productId || product.id);
      }
    },
    handleLikeUpdated(product) {
      // 更新相关产品的点赞状态
      const index = this.relatedProducts.findIndex(p => p.productId === product.id);
      if (index > -1) {
        this.relatedProducts[index].likes = product.likes;
        this.relatedProducts[index].isLiked = product.isLiked;
      }
    },
    handleCollectUpdated(product) {
      // 更新相关产品的收藏状态
      const index = this.relatedProducts.findIndex(p => p.productId === product.id);
      if (index > -1) {
        this.relatedProducts[index].isCollected = product.isCollected;
      }
    },
    handleRelatedProductClick(product) {
      this.goToProductDetail(product.productId);
    },
    handleViewsClick(product) {
      this.goToProductDetail(product.productId);
    },
    handleCommentsClick(product) {
      this.goToProductDetail(product.productId);
    },
    // 根据平台名称更新汇率
    updateExchangeRateByPlatform(platformName) {
      // 使用API返回的exchangeRate字段，而不是硬编码的汇率值
      const platform = this.platforms.find(p => p.name === platformName);

      // 现在后台录入的是人民币价格
      const rmbPrice = this.product.price || 0;

      // 如果找到平台且有exchangeRate字段，则使用该值
      if (platform && platform.exchangeRate) {
        const rate = parseFloat(platform.exchangeRate);

        // 添加详细日志，查看计算过程
        console.log('价格计算详情:', {
          平台名称: platformName,
          汇率值: rate,
          人民币价格: rmbPrice,
          计算过程: `${rmbPrice} ÷ ${rate} = ${rmbPrice / rate}`,
          转换后USD价格: (rmbPrice / rate).toFixed(2)
        });

        // 更新价格数据，人民币价格直接显示，美元价格通过除法计算
        this.rmb_price = rmbPrice.toFixed(2);
        this.usd_price = (rmbPrice / rate).toFixed(2); // 人民币除以汇率得到美元价格，保留两位小数
      } else {
        // 如果没有找到平台或exchangeRate字段，使用默认汇率6.5
        console.log('使用默认汇率6.5计算价格');
        this.rmb_price = rmbPrice.toFixed(2);
        this.usd_price = (rmbPrice / 6.5).toFixed(2); // 人民币除以汇率得到美元价格，保留两位小数
      }
    },
    // 初始化价格显示
    initializePrices() {
      // 现在后台录入的是人民币价格，需要转换为美元
      const rmbPrice = this.product.price || 0;

      // 如果已经有默认平台，使用该平台的汇率进行转换
      if (this.defaultPlatform && this.defaultPlatform.exchangeRate) {
        const rate = parseFloat(this.defaultPlatform.exchangeRate);
        this.rmb_price = rmbPrice.toFixed(2); // 显示人民币价格，保留两位小数
        this.usd_price = (rmbPrice / rate).toFixed(2); // 人民币除以汇率得到美元价格，保留两位小数
      } else {
        // 否则使用默认汇率6.5进行转换
        this.rmb_price = rmbPrice.toFixed(2); // 显示人民币价格，保留两位小数
        this.usd_price = (rmbPrice / 6.5).toFixed(2); // 人民币除以汇率得到美元价格，保留两位小数
      }
    },
    // 验证图片URL是否有效
    isValidImageUrl(url) {
      if (!url) return false;
      return typeof url === 'string' && url.trim() !== '';
    },
    
    // 获取图片URL，如果无效则返回占位图
    getImageUrl(img) {
      if (!img) return this.getPlaceholderImage();
      
      const url = img.imageUrl || img.url || img.src || '';
      return this.isValidImageUrl(url) ? url : this.getPlaceholderImage();
    },
    
    // 返回一个内联的占位图像
    getPlaceholderImage() {
      // 返回一个简单的灰色背景占位图的Base64编码
      return 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiB2aWV3Qm94PSIwIDAgMjAwIDIwMCI+PHJlY3Qgd2lkdGg9IjIwMCIgaGVpZ2h0PSIyMDAiIGZpbGw9IiMzMzMiLz48dGV4dCB4PSI1MCUiIHk9IjUwJSIgZm9udC1zaXplPSIyMCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgYWxpZ25tZW50LWJhc2VsaW5lPSJtaWRkbGUiIGZpbGw9IiNhYWEiPuWbvueJh+aXoOazlei0reWKoDwvdGV4dD48L3N2Zz4=';
    },

    // 计算总浏览量（真实浏览量 + 虚拟浏览量）
    getTotalViews() {
      if (!this.product) return 0;
      const realViews = parseInt(this.product.views || 0);
      const virtualViews = parseInt(this.product.virtualViews || 0);
      return realViews + virtualViews;
    },
  },
  computed: {
    productTags() {
      // 处理商品标签，支持字符串或数组格式
      if (!this.product.tag) return [];
      
      // 如果是字符串，检查是否为JSON格式的数组
      if (typeof this.product.tag === 'string') {
        try {
          // 尝试解析JSON字符串
          const parsedTags = JSON.parse(this.product.tag);
          if (Array.isArray(parsedTags)) {
            return parsedTags;
          }
          // 如果不是数组，则将字符串作为单个标签返回
          return [this.product.tag];
        } catch (e) {
          // 如果解析失败，则将字符串作为单个标签返回
          return [this.product.tag];
        }
      }
      
      // 如果已经是数组，直接返回
      if (Array.isArray(this.product.tag)) {
        return this.product.tag;
      }
      
      return [];
    },
    isDarkMode() {
      // 获取当前主题状态
      return document.documentElement.getAttribute('data-theme') === 'dark';
    },
    showRelatedProducts() {
      // 直接返回API获取的相关产品，不添加任何假数据
      return this.relatedProducts || [];
    },
    relatedEmptyCount() {
      // 使用原始数据source而不是另一个计算属性，避免循环依赖
      return 4 - (this.relatedProducts ? this.relatedProducts.length : 0);
    },
    qcImageGroups() {
      // 将QC图片按每组6张进行分组
      const groups = [];
      // 确保只使用有效的QC图片
      const validImages = this.qcImages.filter(img => img && img.imageUrl);
      for (let i = 0; i < validImages.length; i += 6) {
        groups.push(validImages.slice(i, i + 6));
      }
      return groups;
    },
    // 根据当前显示数量限制展示的QC图片组
    visibleQcImageGroups() {
      return this.qcImageGroups.slice(0, this.visibleGroupsCount);
    },
    // 计算是否还有更多组可以加载
    hasMoreGroups() {
      return this.visibleGroupsCount < this.qcImageGroups.length;
    },
    isMobile() {
      return this.windowWidth < 768;
    }
  },
  watch: {
    '$route.params.id'(newId, oldId) {
      if (newId !== oldId) {
        // 重置重试计数器
        this.retryCount = 0;
        this.productId = newId;
        this.fetchProductDetails();
        window.scrollTo({ top: 0, behavior: 'smooth' });
      }
    }
  }
}
</script>

<style scoped>
/* Custom scrollbar for the whole page */
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

/* 加载动画样式 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  backdrop-filter: blur(5px);
}

.loading-spinner {
  width: 80px;
  height: 80px;
  position: relative;
  margin-bottom: 20px;
}

.spinner-inner {
  width: 100%;
  height: 100%;
  border: 4px solid transparent;
  border-top-color: #9333ea;
  border-radius: 50%;
  animation: spin 1.5s linear infinite;
}

.spinner-inner::before,
.spinner-inner::after {
  content: '';
  position: absolute;
  border: 4px solid transparent;
  border-radius: 50%;
}

.spinner-inner::before {
  top: 5px;
  left: 5px;
  right: 5px;
  bottom: 5px;
  border-top-color: #a855f7;
  animation: spin 2s linear infinite reverse;
}

.spinner-inner::after {
  top: 15px;
  left: 15px;
  right: 15px;
  bottom: 15px;
  border-top-color: #c084fc;
  animation: spin 1s linear infinite;
}

.loading-text {
  color: white;
  font-size: 1.2rem;
  text-align: center;
  animation: pulse 1.5s ease-in-out infinite;
  text-shadow: 0 0 10px rgba(147, 51, 234, 0.7);
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
}

/* 深色主题全局滚动条 */
[data-theme="dark"] ::-webkit-scrollbar-track {
  background: rgba(20, 20, 30, 0.5);
  border-radius: 5px;
}

[data-theme="dark"] ::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #352458, #2d1e4a);
  border-radius: 5px;
  border: 1px solid rgba(120, 70, 200, 0.3);
  box-shadow: inset 0 0 6px rgba(120, 70, 200, 0.3);
}

[data-theme="dark"] ::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #42306a, #352458);
  border-color: rgba(120, 70, 200, 0.6);
}

[data-theme="dark"] ::-webkit-scrollbar-corner {
  background: rgba(20, 20, 30, 0.5);
}

/* 浅色主题全局滚动条 */
[data-theme="light"] ::-webkit-scrollbar-track {
  background: rgba(240, 240, 245, 0.5);
  border-radius: 5px;
}

[data-theme="light"] ::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #e5e7eb, #d1d5db);
  border-radius: 5px;
  border: 1px solid rgba(139, 92, 246, 0.2);
  box-shadow: inset 0 0 6px rgba(139, 92, 246, 0.1);
}

[data-theme="light"] ::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #8b5cf6, #a855f7);
  border-color: rgba(139, 92, 246, 0.4);
}

[data-theme="light"] ::-webkit-scrollbar-corner {
  background: rgba(240, 240, 245, 0.5);
}

.product-detail {
  padding: 0.8rem 2rem;
  padding-top: 80px; /* 增加顶部空间以确保内容不被导航栏遮挡 */
  background: transparent;
  background-image: none;
  min-height: 100vh;
  position: relative;
  overflow: hidden;
  z-index: 1;
  transition: color 0.3s ease;
}

/* 深色主题 */
[data-theme="dark"] .product-detail {
  color: #e0e0e0;
}

/* 浅色主题 */
[data-theme="light"] .product-detail {
  color: #333;
}

/* 移除原有的星空背景设置 */
.product-detail::before {
  display: none;
}

.floating-back-button {
  position: fixed;
  top: 100px;
  left: 20px;
  z-index: 100;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  padding: 0.7rem 1.2rem;
  border-radius: 30px;
  transition: all 0.3s ease;
  font-weight: 500;
}

/* 深色主题返回按钮 */
[data-theme="dark"] .floating-back-button {
  background: linear-gradient(135deg, #352458, #2d1e4a);
  color: #e0e0e0;
  box-shadow: 0 3px 10px rgba(120, 70, 200, 0.3);
  border: 1px solid rgba(120, 70, 200, 0.3);
}

[data-theme="dark"] .floating-back-button:hover {
  background: linear-gradient(135deg, #42306a, #352458);
  box-shadow: 0 5px 15px rgba(120, 70, 200, 0.5);
  transform: translateY(-2px);
}

/* 浅色主题返回按钮 */
[data-theme="light"] .floating-back-button {
  background: linear-gradient(135deg, #8b5cf6, #a855f7);
  color: white;
  box-shadow: 0 3px 10px rgba(139, 92, 246, 0.3);
  border: 1px solid rgba(139, 92, 246, 0.3);
}

[data-theme="light"] .floating-back-button:hover {
  background: linear-gradient(135deg, #7c3aed, #8b5cf6);
  box-shadow: 0 5px 15px rgba(139, 92, 246, 0.5);
  transform: translateY(-2px);
}

.product-container {
  display: flex;
  gap: 1.5rem; /* Increased from 0.6rem to 1.5rem for more spacing between sections */
  max-width: 1400px;
  margin: 0 auto 0.8rem;
  position: relative;
  align-items: center;
  z-index: 1;
}

  /* 图片部分样式更新 */
  .product-images {
    flex: 1.2; /* Increased from 1 to 1.2 to make it slightly wider */
    display: flex;
    flex-direction: column;
    gap: 0.3rem; /* 减小图片与历史购买信息之间的间距 */
  }

/* 主图布局 */
.product-image-container {
  width: 100%;
  margin-bottom: 15px;
  position: relative;
  border-radius: 15px;
  overflow: hidden;
}

.main-product-image {
  position: relative;
  width: 80%; /* 与下方历史购买信息框保持一致的宽度 */
  padding-bottom: 80%; /* 保持1:1比例，但基于80%宽度 */
  border-radius: 15px;
  overflow: hidden;
  cursor: zoom-in;
  margin: 0 auto; /* Center the image */
  transition: all 0.3s ease;
}

/* 视频播放区域样式 */
.main-product-video {
  position: relative;
  width: 80%;
  margin: 0 auto;
  border-radius: 15px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.product-video {
  width: 100%;
  height: auto;
  max-height: 500px;
  object-fit: contain;
  border-radius: 15px;
  background-color: #000;
}

/* 媒体切换按钮样式 */
.media-toggle-buttons {
  position: absolute;
  top: 10px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
  display: flex;
  gap: 10px;
  background: rgba(0, 0, 0, 0.4);
  padding: 5px 10px;
  border-radius: 20px;
  backdrop-filter: blur(4px);
}

.media-toggle-btn {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  cursor: pointer;
  background: rgba(25, 25, 35, 0.7);
  color: #fff;
  font-size: 16px;
  transition: all 0.3s ease;
}

.media-toggle-btn.active {
  background: #9c27b0;
  transform: scale(1.1);
  box-shadow: 0 0 10px rgba(156, 39, 176, 0.6);
}

.media-toggle-btn:hover {
  transform: scale(1.1);
  background: rgba(156, 39, 176, 0.8);
}

.hidden {
  display: none;
}

/* 深色主题图片容器 */
[data-theme="dark"] .main-product-image {
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  background: #1a1a2e;
}

/* 浅色主题图片容器 */
[data-theme="light"] .main-product-image {
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  background: #f5f5f5;
}



/* 缩略图样式 */
.image-thumbnails-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 10px;
  margin-top: 15px;
}

.thumbnail-grid-item {
  width: 100%;
  aspect-ratio: 1;
  object-fit: contain;
  border-radius: 6px;
  cursor: pointer;
  border: 1px solid rgba(80, 80, 100, 0.3);
  transition: all 0.2s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  background: rgba(25, 25, 35, 0.6);
  padding: 3px;
}

.thumbnail-grid-item:hover, .thumbnail-grid-item.active {
  border-color: #c3a3ff;
  box-shadow: 0 3px 8px rgba(120, 70, 200, 0.4);
  transform: translateY(-2px);
}

/* 商品信息区域 */
.product-info {
  position: relative;
  flex: 0.8; /* Decreased from 1 to 0.8 to make it narrower */
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  background: transparent; /* 改为透明背景 */
  padding: 0; /* 移除内边距 */
  border-radius: 10px;
  box-shadow: none;
  border: none;
}

/* 新增两部分的样式 */
.product-info-section {
  padding: 1rem;
  border-radius: 10px;
  transition: background 0.3s ease;
}

.product-info-top {
  margin-bottom: -0.8rem; /* 减小底部间距 */
  display: flex;
  flex-direction: column;
  gap: 0.4rem; /* 减小间距 */
  box-shadow: none; /* 确保无阴影 */
}

.product-info-bottom {
  padding: 1.5rem 1rem;
  box-shadow: none; /* 移除阴影 */
  border: none; /* 移除边框 */
  transition: background 0.3s ease;
}

/* 深色主题产品信息区域 */
[data-theme="dark"] .product-info-section {
  background: rgba(20, 10, 30, 0.6);
}

[data-theme="dark"] .product-info-bottom {
  background: rgba(25, 15, 35, 0.5);
}

/* 浅色主题产品信息区域 */
[data-theme="light"] .product-info-section {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

[data-theme="light"] .product-info-bottom {
  background: rgba(248, 250, 252, 0.8);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

/* 价格区域 */
.price-section {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 0rem; /* 增加价格区域底部间距 */
  position: relative;
}

.price-tag {
  display: inline-flex;
  padding: 0;
  align-self: flex-start;
  position: relative;
  margin-bottom: 0.3rem;
}

.price-amount {
  font-size: 3.5rem;
  font-weight: bold;
  font-family: 'Arial', sans-serif;
  letter-spacing: -1px;
  transition: all 0.3s ease;
}

/* 深色主题价格 */
[data-theme="dark"] .price-amount {
  color: #b76cff;
  text-shadow: 0 0 8px rgba(183, 108, 255, 0.5);
}

/* 浅色主题价格 */
[data-theme="light"] .price-amount {
  color: #8b5cf6;
  text-shadow: 0 0 8px rgba(139, 92, 246, 0.3);
}

.product-count {
  font-size: 1rem;
  font-weight: normal;
  transition: color 0.3s ease;
}

/* 深色主题产品数量 */
[data-theme="dark"] .product-count {
  color: #aaa;
}

/* 浅色主题产品数量 */
[data-theme="light"] .product-count {
  color: #666;
}

.product-tag-row {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-bottom: 0rem; /* 减小标签底部间距 */
  flex-wrap: wrap;
  gap: 0.5rem;
}

.product-tag {
  display: inline-block;
  background: #ff3e3e;
  color: white;
  padding: 0.2rem 0.6rem;
  border-radius: 20px;
  font-weight: normal;
  font-size: 0.8rem;
  margin-bottom: 0.3rem;
  box-shadow: none;
}

/* FROM 1688链接紫色背景样式 */
.from-1688-bar {
  background: linear-gradient(to right, #9c27b0, #7b1fa2);
  border-radius: 16px;
  padding: 0.4rem 0.7rem; /* 减小内边距 */
  margin: 0; /* 减小上下间距 */
  display: flex;
  align-items: center;
  gap: 0.4rem;
}

.from-1688-bar span {
  color: white;
  font-size: 0.8rem;
  font-weight: 500;
}

.from-link {
  color: white;
  text-decoration: none;
  font-size: 0.8rem;
  opacity: 0.9;
  transition: all 0.2s;
}

.from-link:hover {
  opacity: 1;
  text-decoration: underline;
}

/* 移动端原链接样式 */
@media (max-width: 768px) {
  .from-1688-bar {
    padding: 0.3rem 0.5rem;
    gap: 0.3rem;
    flex-wrap: nowrap;
    width: 100%;
    box-sizing: border-box;
  }
  
  .from-link {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: calc(100% - 100px); /* 减去标签和间距的空间 */
  }
}

@media (max-width: 480px) {
  .from-1688-bar {
    font-size: 0.7rem;
    padding: 0.25rem 0.4rem;
  }
  
  .from-1688-bar span {
    font-size: 0.7rem;
    white-space: nowrap;
  }
  
  .from-link {
    font-size: 0.7rem;
    max-width: calc(100% - 80px); /* 进一步缩小 */
  }
}

/* 社交统计 */
.social-stats-prominent {
  display: flex;
  gap: 1.5rem; /* 增大社交图标之间的间距至1.5rem */
  margin: 0.5rem 0 0.2rem; /* 减小上下间距 */
  background-color: transparent;
  padding: 0.2rem 0; /* 减小上下内边距 */
  border-radius: 0;
  border: none;
  justify-content: flex-start;
}

.stat-item-prominent {
  display: flex;
  align-items: center;
  gap: 0.4rem;
  padding: 0;
  border-radius: 0;
  background-color: transparent;
  border: none;
  min-width: auto;
  transition: all 0.2s ease;
  cursor: pointer;
}

.stat-item-prominent:hover {
  background-color: transparent;
  transform: translateY(-1px);
  opacity: 0.8;
}

.stat-item-prominent i {
  font-size: 1rem;
  transition: color 0.3s ease;
}

.stat-item-prominent span {
  font-weight: normal;
  font-size: 0.9rem;
  transition: color 0.3s ease;
}

/* 深色主题社交统计图标 */
[data-theme="dark"] .stat-item-prominent i {
  color: #ffffff;
}

[data-theme="dark"] .stat-item-prominent span {
  color: #ffffff;
}

/* 浅色主题社交统计图标 */
[data-theme="light"] .stat-item-prominent i {
  color: #333333;
}

[data-theme="light"] .stat-item-prominent span {
  color: #333333;
}

/* 代理平台选择区域 */
.agent-platforms {
  display: flex;
  gap: 1.5rem;
  margin: 1.8rem 0 1.5rem;
  width: 100%;
  margin-left: auto;
  margin-right: auto;
}

.platform-option {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1.5rem 1rem;
  border-radius: 20px;
  flex: 1;
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative;
  /* 默认浅色主题样式 */
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.95), rgba(241, 245, 249, 0.9));
  border: 1px solid rgba(139, 92, 246, 0.2);
  box-shadow: 0 8px 20px rgba(139, 92, 246, 0.1);
}

/* 深色主题样式 */
[data-theme="dark"] .platform-option {
  background: linear-gradient(135deg, rgba(35, 35, 45, 0.9), rgba(25, 25, 35, 0.8));
  border: 1px solid rgba(120, 70, 200, 0.2);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
}

.platform-option::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  /* 默认浅色主题 */
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.08), transparent);
}

/* 深色主题伪元素 */
[data-theme="dark"] .platform-option::before {
  background: linear-gradient(135deg, rgba(120, 70, 200, 0.1), transparent);
}

.platform-option:hover {
  transform: translateY(-3px);
  /* 默认浅色主题悬停 */
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.15), rgba(139, 92, 246, 0.08));
  border-color: rgba(139, 92, 246, 0.4);
  box-shadow: 0 10px 25px rgba(139, 92, 246, 0.2);
}

/* 深色主题悬停效果 */
[data-theme="dark"] .platform-option:hover {
  background: linear-gradient(135deg, rgba(45, 45, 55, 0.95), rgba(35, 35, 45, 0.85));
  border-color: rgba(120, 70, 200, 0.5);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.4);
}

.platform-value {
  font-size: 2rem;
  font-weight: 700;
  letter-spacing: 0px;
  text-align: center;
  position: relative;
  z-index: 1;
  transition: all 0.3s ease;
  /* 默认浅色主题 */
  color: #1f2937;
  text-shadow: 0 0 10px rgba(139, 92, 246, 0.3);
}

/* 深色主题文字样式 */
[data-theme="dark"] .platform-value {
  color: #ffffff;
  text-shadow: 0 0 10px rgba(120, 70, 200, 0.5);
}

/* 代理平台选择 */
.agent-platforms-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 5px;
  margin-top: 5px;
  margin-bottom: 8px;
}

.agent-platform-item {
  background: rgba(40, 40, 50, 0.6);
  border-radius: 5px;
  padding: 4px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid rgba(80, 80, 100, 0.3);
}

.agent-platform-item:hover {
  background: rgba(60, 60, 80, 0.6);
  transform: translateY(-2px);
}

.agent-platform-logo {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.agent-platform-logo img {
  width: 80%;
  height: 80%;
  object-fit: contain;
}

.agent-platform-name {
  font-size: 0.7rem;
  color: white;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  text-align: center;
}

  /* 历史购买信息样式 */
.historical-purchase-info {
  margin-top: 0; /* 移除顶部间距 */
  width: 80%; /* Match the width of the main image */
  margin-left: auto;
  margin-right: auto; /* Center the element */
}

/* 深色主题历史购买信息卡片 */
[data-theme="dark"] .historical-purchase-info :deep(.el-card__header) {
  background: #2a2a3c;
  padding: 8px 12px; /* 减小头部内边距 */
  border-bottom: none;
}

[data-theme="dark"] .historical-purchase-info :deep(.el-card__body) {
  background: #2a2a3c;
  padding: 5px 12px 12px; /* 减小内边距 */
}

[data-theme="dark"] .historical-purchase-info :deep(.el-card) {
  border: none;
  background: #2a2a3c;
  border-radius: 10px;
  box-shadow: none;
}

/* 浅色主题历史购买信息卡片 */
[data-theme="light"] .historical-purchase-info :deep(.el-card__header) {
  background: #ffffff;
  padding: 8px 12px;
  border-bottom: 1px solid #e4e7ed;
}

[data-theme="light"] .historical-purchase-info :deep(.el-card__body) {
  background: #ffffff;
  padding: 5px 12px 12px;
}

[data-theme="light"] .historical-purchase-info :deep(.el-card) {
  border: 1px solid #e4e7ed;
  background: #ffffff;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.info-header {
  color: white;
  font-size: 1rem;
  font-weight: 500;
  padding: 0;
  margin: 0;
  line-height: 1.0;
}

.info-row {
  display: flex;
  gap: 10px;
}

.info-item {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.info-label {
  color: white;
  font-size: 0.9rem;
  font-weight: 400;
  margin-bottom: 0;
  padding-left: 10px;
  opacity: 0.9;
}

/* 自定义Element Plus输入框样式 */
.info-input {
  width: 100%;
}

.info-input :deep(.el-input__wrapper) {
  background: #1d1d2d;
  border: 1px solid #3a3a4c;
  box-shadow: none !important;
  padding: 0;
  border-radius: 5px;
}

.info-input :deep(.el-input__inner) {
  color: white;
  font-size: 0.9rem;
  height: 32px;
  padding-left: 10px;
}

.info-input :deep(.el-input.is-disabled .el-input__wrapper) {
  background: #1d1d2d;
  border: 1px solid #3a3a4c;
  box-shadow: none !important;
}

.info-input :deep(.el-input.is-disabled .el-input__placeholder) {
  color: #fff;
  opacity: 0.8;
}

/* 右上角收藏按钮 */
.wishlist-btn-corner {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  width: 40px; /* 增大按钮尺寸 */
  height: 40px; /* 增大按钮尺寸 */
  border-radius: 50%;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
  font-size: 1.6rem; /* 增大图标尺寸 */
  z-index: 5;
  box-shadow: none;
}

/* 深色主题收藏按钮 */
[data-theme="dark"] .wishlist-btn-corner {
  background: rgba(35, 35, 45, 0.8);
  color: #ccc;
}

/* 浅色主题收藏按钮 */
[data-theme="light"] .wishlist-btn-corner {
  background: rgba(255, 255, 255, 0.9);
  color: #666;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.wishlist-btn-corner:hover {
  transform: scale(1.1);
}

.wishlist-btn-corner.liked {
  color: #ff6b6b;
}

/* 粉丝头像区域 */
.platform-icons-row {
  display: flex;
  gap: 0.8rem;
  margin: 1rem 0 3.5rem;
  flex-wrap: wrap;
  overflow-x: auto;
  padding-bottom: 10px;
  max-width: 100%;
}

.platform-icon-item {
  width: 35px;
  height: 35px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  cursor: pointer;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  margin: 0 3px;
  position: relative;
  flex-shrink: 0;
}

.platform-icon-item:hover {
  transform: translateY(-2px);
  border-color: rgba(255, 255, 255, 0.5);
  box-shadow: 0 5px 12px rgba(120, 70, 200, 0.4);
}

.platform-icon-item.active {
  border-color: #409EFF;
  box-shadow: 0 3px 8px rgba(64, 158, 255, 0.5);
}

.platform-icon-item.active::after {
  content: '✓';
  position: absolute;
  top: -5px;
  right: -5px;
  background-color: #409EFF;
  color: white;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.platform-logo-img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* 购买按钮区域 */
.buy-button-container {
  display: flex;
  gap: 0.4rem;
  margin: 1rem 0 0.5rem;
}

.buy-now-btn {
  width: 100%;
  padding: 1rem 0.8rem; /* 更大的内边距 */
  background: #9c27b0; /* 更明亮的紫色 */
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 1.2rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(156, 39, 176, 0.4);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  margin-top: 1rem;
  letter-spacing: 0.5px;
  position: relative;
  overflow: hidden;
}

.buy-now-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.3) 50%,
    rgba(255, 255, 255, 0) 100%);
  transition: left 0.7s;
}

.buy-now-btn:hover {
  background: #7b1fa2;
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(156, 39, 176, 0.6);
}

.buy-now-btn:hover::before {
  left: 100%;
}

.wishlist-btn {
  width: 50px;
  height: 50px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
  font-size: 1.2rem;
}

/* 深色主题收藏按钮 */
[data-theme="dark"] .wishlist-btn {
  background: rgba(35, 35, 45, 0.7);
  border: 1px solid rgba(120, 70, 200, 0.3);
  color: #c3a3ff;
}

[data-theme="dark"] .wishlist-btn:hover {
  background: rgba(120, 70, 200, 0.3);
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(120, 70, 200, 0.4);
  color: white;
}

/* 浅色主题收藏按钮 */
[data-theme="light"] .wishlist-btn {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(139, 92, 246, 0.3);
  color: #8b5cf6;
}

[data-theme="light"] .wishlist-btn:hover {
  background: rgba(139, 92, 246, 0.1);
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
  color: #7c3aed;
}

/* 放大镜功能 */
.image-zoom-lens {
  position: absolute;
  border: 2px solid rgba(120, 70, 200, 0.5);
  background: rgba(120, 70, 200, 0.1);
  backdrop-filter: blur(2px);
  border-radius: 50%;
  cursor: none;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s;
}

.zoom-container {
  position: absolute; /* 恢复为绝对定位 */
  top: 0;
  right: 0; /* 仅定位在右侧 */
  width: 500px; /* 进一步增加宽度 */
  height: 500px; /* 进一步增加高度，保持正方形 */
  background: rgba(20, 20, 30, 0.95);
  border-radius: 8px; /* 恢复圆角 */
  z-index: 50; /* 适当的z-index */
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border: 1px solid rgba(120, 70, 200, 0.4);
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.4);
}

.zoom-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.3rem 0.6rem; /* 进一步减小内边距 */
  background: linear-gradient(135deg, rgba(53, 36, 88, 0.9), rgba(45, 30, 74, 0.9));
  border-bottom: 1px solid rgba(120, 70, 200, 0.4);
  height: 30px; /* 减小高度 */
}

.zoom-title {
  display: flex;
  align-items: center;
  gap: 0.4rem; /* 减小间距 */
  color: #c3a3ff;
  font-weight: 600;
  font-size: 0.9rem; /* 减小字体大小 */
  text-shadow: 0 0 5px rgba(195, 163, 255, 0.4);
}

.zoom-close {
  background: rgba(60, 40, 80, 0.3);
  border: none;
  color: #e0e0e0;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s;
  width: 24px; /* 减小尺寸 */
  height: 24px; /* 减小尺寸 */
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.zoom-close:hover {
  background: rgba(120, 70, 200, 0.4);
  color: #fff;
  transform: rotate(90deg);
}

.image-zoom-result {
  flex: 1;
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
  cursor: zoom-out; /* 添加指针样式 */
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.2s ease; /* 添加过渡效果 */
}

.image-zoom-result:hover {
  transform: scale(0.98); /* 添加悬停效果 */
}

.zoom-hint {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  padding: 0.8rem 1.2rem;
  border-radius: 50px;
  display: flex;
  align-items: center;
  gap: 0.6rem;
  font-size: 1rem;
  backdrop-filter: blur(4px);
  border: none;
  opacity: 0.9;
  transition: opacity 0.3s;
  z-index: 10;
}

/* 深色主题zoom-hint */
[data-theme="dark"] .zoom-hint {
  background: rgba(0, 0, 0, 0.7);
  color: #fff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* 浅色主题zoom-hint */
[data-theme="light"] .zoom-hint {
  background: rgba(255, 255, 255, 0.9);
  color: #333;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.main-image:hover .zoom-hint {
    opacity: 1;
  }

/* 社交统计 */
/* 新的突出显示的社交统计样式 - 匹配图片样式 */
.social-stats-prominent {
  display: flex;
  gap: 0.5rem;
  margin: 0.2rem 0 0.3rem;
  background-color: transparent;
  padding: 0;
  border-radius: 0;
  border: none;
  justify-content: flex-start;
}

.stat-item-prominent {
  display: flex;
  align-items: center;
  gap: 0.4rem;
  padding: 0;
  border-radius: 0;
  background-color: transparent;
  border: none;
  min-width: auto;
  transition: all 0.2s ease;
}

.stat-item-prominent:hover {
  background-color: transparent;
  transform: translateY(-1px);
  opacity: 0.8;
}

/* 移动端社交统计图标样式已在上面统一定义 */

/* 保留原样式但不使用 */
.social-stats {
  display: none; /* 隐藏原社交统计样式 */
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.4rem;
  cursor: pointer;
  padding: 0.4rem;
  border-radius: 4px;
  transition: all 0.2s ease;
}

/* 深色主题stat-item */
[data-theme="dark"] .stat-item {
  color: #a9a9a9;
}

/* 浅色主题stat-item */
[data-theme="light"] .stat-item {
  color: #666666;
}

/* 深色主题stat-item悬停和激活状态 */
[data-theme="dark"] .stat-item:hover {
  background: rgba(120, 70, 200, 0.2);
  color: #c3a3ff;
}

[data-theme="dark"] .stat-item:hover {
  background: rgba(139, 92, 246, 0.1);
  color: #8b5cf6;
}

[data-theme="dark"] .stat-item.liked {
  color: #c3a3ff;
  font-weight: 500;
}

/* 移动端适配 */
@media (max-width: 1024px) {
  .product-container {
  flex-direction: column;
  }
  
  .main-image {
    height: 360px;
  }
  
  .product-info {
    width: 100%;
  }
  
  .currency-options {
    flex-wrap: wrap;
  }
  
  .fans-container {
    margin: 0.5rem 0;
  }
  
  .fans-row {
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .product-detail {
    padding: 0; /* 移除内边距 */
    padding-top: 65px; /* 减小顶部空间 */
  }
  
  .main-image {
    height: 300px;
  }
  
  .price-amount {
    font-size: 2rem;
  }
  
  .product-name {
    font-size: 1.4rem;
    margin: 0.2rem 0 0.4rem; /* 减小商品名称上下间距 */
  }
  
  .fan-avatar {
    width: 40px;
    height: 40px;
  }
  
  .buy-now-btn {
    font-size: 1rem;
    padding: 0.8rem 1.2rem;
  }
}

.product-gallery {
  max-width: 1400px;
  margin: 1rem auto;
  padding-top: 0.8rem;
  position: relative;
  z-index: 1;
  transition: border-color 0.3s ease;
}

.product-gallery h2 {
  margin-bottom: 0.8rem;
  font-size: 1.2rem;
  transition: all 0.3s ease;
}

/* 深色主题产品画廊 */
[data-theme="dark"] .product-gallery {
  border-top: 1px solid rgba(120, 70, 200, 0.2);
}

[data-theme="dark"] .product-gallery h2 {
  color: #c3a3ff;
  text-shadow: 0 0 10px rgba(120, 70, 200, 0.5);
}

/* 浅色主题产品画廊 */
[data-theme="light"] .product-gallery {
  border-top: 1px solid rgba(139, 92, 246, 0.2);
}

[data-theme="light"] .product-gallery h2 {
  color: #8b5cf6;
  text-shadow: 0 0 10px rgba(139, 92, 246, 0.3);
}

.carousel-groups-container {
  width: 100%;
}

.carousel-groups-row {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 15px;
  width: 100%;
}

.carousel-group {
  margin-bottom: 1.5rem;
  aspect-ratio: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
  width: 100%;
  height: 0;
  padding-bottom: 100%; /* 确保1:1比例 */
  border-radius: 8px;
  background-color: #333333;
}

.carousel-group-title {
  margin: 0;
  padding: 0.5rem 1rem;
  font-size: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: 10;
  width: 100%;
  font-weight: 500;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  height: 40px;
  box-sizing: border-box;
  transition: all 0.3s ease;
}

/* 深色主题轮播组标题 */
[data-theme="dark"] .carousel-group-title {
  background: linear-gradient(135deg, rgba(53, 36, 88, 0.9), rgba(45, 30, 74, 0.9));
  color: #c3a3ff;
}

/* 浅色主题轮播组标题 */
[data-theme="light"] .carousel-group-title {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.1), rgba(168, 85, 247, 0.1));
  color: #8b5cf6;
  border: 1px solid rgba(139, 92, 246, 0.2);
  border-bottom: none;
}

.product-gallery :deep(.el-carousel__container) {
  flex: 1;
  min-height: 0;
  height: 100% !important;
  border-radius: 8px;
  overflow: hidden;
  margin-top: 0;
}

/* 自定义轮播箭头样式 */
.product-gallery :deep(.el-carousel__arrow) {
  backdrop-filter: blur(3px);
  width: 36px;
  height: 36px;
  transition: all 0.3s ease;
}

/* 深色主题轮播箭头 */
[data-theme="dark"] .product-gallery :deep(.el-carousel__arrow) {
  background-color: rgba(20, 10, 30, 0.7);
  border: 1px solid rgba(120, 70, 200, 0.3);
}

[data-theme="dark"] .product-gallery :deep(.el-carousel__arrow:hover) {
  background-color: rgba(120, 70, 200, 0.5);
}

/* 浅色主题轮播箭头 */
[data-theme="light"] .product-gallery :deep(.el-carousel__arrow) {
  background-color: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(139, 92, 246, 0.3);
  color: #8b5cf6;
}

[data-theme="light"] .product-gallery :deep(.el-carousel__arrow:hover) {
  background-color: rgba(139, 92, 246, 0.1);
  border-color: rgba(139, 92, 246, 0.5);
}

.product-gallery :deep(.el-carousel__item) {
  height: 100%;
  padding: 0;
  margin: 0;
}

.carousel-item-full {
  width: 100%;
  height: 100%;
  overflow: hidden;
  padding: 0;
  margin: 0;
}

.single-carousel {
  flex: 1;
  min-height: 0;
  height: 100%;
  padding: 0;
  margin: 0;
  position: absolute;
  top: 40px;
  left: 0;
  right: 0;
  bottom: 0;
}

.qc-image-full {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
  padding: 0;
  margin: 0;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.qc-image-full:hover {
  transform: scale(1.05);
}

@media (max-width: 1200px) {
  .carousel-groups-row {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 992px) {
  .carousel-groups-row {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .product-gallery {
    margin: 0.5rem auto; /* 减小上下边距 */
    padding-top: 0.5rem; /* 减小顶部内边距 */
  }
  
  .product-gallery h2 {
    margin-bottom: 0.5rem; /* 减小标题底部间距 */
  }
  
  .carousel-groups-row {
    grid-template-columns: repeat(1, 1fr);
    gap: 15px;
  }
  
  .carousel-group {
    margin-bottom: 0.5rem; /* 减小组间距 */
    aspect-ratio: 1;
  }
  
  .carousel-group-title {
    font-size: 0.9rem; /* 减小字体大小 */
    padding: 0.4rem; /* 减小内边距 */
  }
  
  .product-gallery :deep(.el-carousel__container) {
    border-radius: 6px; /* 减小圆角 */
  }
}

@media (max-width: 576px) {
  .carousel-group-title {
    font-size: 0.8rem; /* 更小的字体 */
    padding: 0.3rem; /* 更小的内边距 */
  }
  
  .product-gallery :deep(.el-carousel__container) {
    border-radius: 4px; /* 更小的圆角 */
  }
}

.gallery-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 12px;
  width: 100%;
  padding: 10px;
}

.gallery-item {
  background: rgba(25, 25, 35, 0.7);
  border-radius: 10px;
  overflow: hidden;
  padding: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(120, 70, 200, 0.2);
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  max-width: 90%;
  margin: 0 auto;
}

.gallery-item:hover {
  box-shadow: 0 8px 20px rgba(120, 70, 200, 0.4);
  border-color: rgba(120, 70, 200, 0.5);
}

@media (max-width: 1200px) {
  .gallery-grid {
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .gallery-grid {
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(3, 1fr);
  }
}

:deep(.el-image) {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.el-image__inner) {
  object-fit: cover !important;
  width: 100% !important;
  height: 100% !important;
  border-radius: 0 !important;
  max-height: none !important;
  max-width: none !important;
}

.comments-section {
  max-width: 1400px;
  margin: 1rem auto;
  padding-top: 0.8rem;
  position: relative;
  z-index: 1;
  transition: border-color 0.3s ease;
}

.comments-card {
  border-radius: 15px !important;
  overflow: hidden;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

/* 深色主题评论区 */
[data-theme="dark"] .comments-section {
  border-top: 1px solid rgba(120, 70, 200, 0.2);
}

[data-theme="dark"] .comments-card {
  background: rgba(25, 25, 35, 0.85) !important;
  border: 1px solid rgba(120, 70, 200, 0.3) !important;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4) !important;
}

/* 浅色主题评论区 */
[data-theme="light"] .comments-section {
  border-top: 1px solid rgba(139, 92, 246, 0.2);
}

[data-theme="light"] .comments-card {
  background: rgba(255, 255, 255, 0.95) !important;
  border: 1px solid rgba(139, 92, 246, 0.3) !important;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1) !important;
}

.comments-card :deep(.el-card__header) {
  padding: 0.8rem 1.2rem;
  transition: all 0.3s ease;
}

.comments-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.comments-header h2 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.4rem;
  font-size: 1.2rem;
  transition: all 0.3s ease;
}

/* 深色主题评论头部 */
[data-theme="dark"] .comments-card :deep(.el-card__header) {
  background: linear-gradient(135deg, rgba(53, 36, 88, 0.8), rgba(45, 30, 74, 0.8));
  border-bottom: 1px solid rgba(120, 70, 200, 0.4);
}

[data-theme="dark"] .comments-header h2 {
  color: #c3a3ff;
  text-shadow: 0 0 10px rgba(120, 70, 200, 0.5);
}

/* 浅色主题评论头部 */
[data-theme="light"] .comments-card :deep(.el-card__header) {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.1), rgba(168, 85, 247, 0.1));
  border-bottom: 1px solid rgba(139, 92, 246, 0.2);
}

[data-theme="light"] .comments-header h2 {
  color: #8b5cf6;
  text-shadow: 0 0 10px rgba(139, 92, 246, 0.3);
}

.comments-card :deep(.el-card__body) {
  padding: 1.2rem;
  /* 从1.5rem减小到1.2rem */
}

.comment-sort-options {
  margin-bottom: 1rem;
  /* 从1.5rem减小到1rem */
  display: flex;
  justify-content: center;
}

.sort-buttons {
  display: flex;
  border-radius: 12px;
  padding: 4px;
  overflow: hidden;
  transition: all 0.3s ease;
}

/* 深色主题排序按钮容器 */
[data-theme="dark"] .sort-buttons {
  background: rgba(30, 30, 40, 0.5);
  border: 1px solid rgba(120, 70, 200, 0.2);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* 浅色主题排序按钮容器 */
[data-theme="light"] .sort-buttons {
  background: rgba(232, 208, 245, 0.3);
  border: 1px solid rgba(142, 45, 226, 0.2);
  box-shadow: 0 4px 12px rgba(142, 45, 226, 0.1);
}

.sort-btn {
  padding: 8px 20px;
  border: none;
  background: transparent;
  cursor: pointer;
  font-size: 0.95rem;
  font-weight: 500;
  border-radius: 10px;
  transition: all 0.3s ease;
  margin: 0 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  min-width: 110px;
  border: 1px solid transparent;
}

/* 深色主题排序按钮 */
[data-theme="dark"] .sort-btn {
  color: #a9a9a9;
}

[data-theme="dark"] .sort-btn:hover {
  color: #c3a3ff;
  background: rgba(120, 70, 200, 0.15);
  border-color: rgba(120, 70, 200, 0.3);
}

[data-theme="dark"] .sort-btn.active {
  background: linear-gradient(135deg, #352458, #2d1e4a);
  color: #c3a3ff;
  box-shadow: 0 0 12px rgba(120, 70, 200, 0.25);
  border: 1px solid rgba(120, 70, 200, 0.5);
  transform: translateY(-1px);
}

/* 浅色主题排序按钮 */
[data-theme="light"] .sort-btn {
  color: #666666;
}

[data-theme="light"] .sort-btn:hover {
  color: #8e2de2;
  background: rgba(232, 208, 245, 0.4);
  border-color: rgba(142, 45, 226, 0.3);
}

[data-theme="light"] .sort-btn.active {
  background: linear-gradient(135deg, #9d4de8, #8e2de2);
  color: #ffffff;
  box-shadow: 0 0 12px rgba(142, 45, 226, 0.25);
  border: 1px solid rgba(142, 45, 226, 0.5);
  transform: translateY(-1px);
}

.sort-btn i {
  font-size: 1em;
}

.comment-action-btn {
  border-radius: 20px !important;
  padding: 0.5rem 0.8rem !important;
  display: flex !important;
  align-items: center !important;
  gap: 0.4rem !important;
  transition: all 0.3s ease !important;
  background: rgba(30, 30, 40, 0.5) !important;
  border: 1px solid rgba(120, 70, 200, 0.2) !important;
}

.comment-action-btn:hover {
  background-color: rgba(120, 70, 200, 0.15) !important;
  transform: translateY(-2px);
  border-color: rgba(120, 70, 200, 0.4) !important;
}

.comment-action-btn:focus {
  box-shadow: 0 0 0 2px rgba(120, 70, 200, 0.2) !important;
}

.comment-action-btn.el-button--primary {
    color: #c3a3ff !important;
  background: linear-gradient(135deg, rgba(53, 36, 88, 0.7), rgba(45, 30, 74, 0.7)) !important;
}

.comment-action-btn.el-button--primary:hover {
  background: linear-gradient(135deg, rgba(66, 48, 106, 0.8), rgba(53, 36, 88, 0.8)) !important;
}

.comment-action-btn.el-button--danger {
  color: #ff9898 !important;
}

.comment-action-btn.el-button--danger:hover {
  background-color: rgba(255, 76, 76, 0.15) !important;
}

/* 移动端适配 */
@media (max-width: 600px) {
  .sort-buttons {
    width: 100%;
    max-width: 320px;
  }

  .sort-btn {
    font-size: 0.9rem;
    padding: 6px 10px;
    min-width: 0;
    flex: 1;
  }

  .sort-btn i {
    font-size: 0.9em;
  }

  .comment-action-btn {
    padding: 0.4rem 0.6rem !important;
    font-size: 0.9rem !important;
  }
}

.comment-form {
  display: flex;
  gap: 0.8rem;
  margin-bottom: 1.2rem;
  padding: 1rem;
  border-radius: 10px;
  transition: all 0.3s ease;
}

.comment-avatar-input {
  flex-shrink: 0;
  transition: all 0.3s ease;
}

/* 深色主题评论表单 */
[data-theme="dark"] .comment-form {
  background: rgba(30, 30, 40, 0.5);
  border: 1px solid rgba(120, 70, 200, 0.2);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .comment-avatar-input {
  border: 2px solid rgba(120, 70, 200, 0.4);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.4);
}

/* 浅色主题评论表单 */
[data-theme="light"] .comment-form {
  background: rgba(248, 250, 252, 0.8);
  border: 1px solid rgba(139, 92, 246, 0.2);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

[data-theme="light"] .comment-avatar-input {
  border: 2px solid rgba(139, 92, 246, 0.4);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
}

.comment-input-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
  /* 从1rem减小到0.8rem */
}

.comment-input-container {
  position: relative;
  width: 100%;
}

.comment-inline-upload-btn {
  position: absolute;
  bottom: 10px;
  right: 10px;
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 2;
}

/* 深色主题上传按钮 */
[data-theme="dark"] .comment-inline-upload-btn {
  background: rgba(45, 45, 55, 0.6);
  color: #c3a3ff;
}

[data-theme="dark"] .comment-inline-upload-btn:hover {
  background: rgba(60, 60, 80, 0.8);
  color: #ffffff;
}

/* 浅色主题上传按钮 */
[data-theme="light"] .comment-inline-upload-btn {
  background: rgba(230, 230, 250, 0.8);
  color: #8b5cf6;
}

[data-theme="light"] .comment-inline-upload-btn:hover {
  background: rgba(139, 92, 246, 0.2);
  color: #7c3aed;
}

.uploaded-images-preview {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 10px;
}

.uploaded-image-item {
  position: relative;
  width: 80px;
  height: 80px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid rgba(120, 70, 200, 0.3);
}

.uploaded-image-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-remove-btn {
  position: absolute;
  top: 2px;
  right: 2px;
  width: 20px;
  height: 20px;
  background: rgba(0, 0, 0, 0.6);
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
}

.image-remove-btn:hover {
  background: rgba(255, 0, 0, 0.8);
  transform: scale(1.1);
}

.comment-textarea :deep(.el-textarea__inner) {
  border-radius: 8px;
  transition: all 0.3s ease;
  font-size: 1rem;
  line-height: 1.5;
  padding: 0.8rem;
  resize: none;
}

/* 深色主题评论输入框 */
[data-theme="dark"] .comment-textarea :deep(.el-textarea__inner) {
  background: rgba(35, 35, 45, 0.7);
  color: #e0e0e0;
  border: 1px solid rgba(120, 70, 200, 0.3);
  box-shadow: inset 0 2px 5px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .comment-textarea :deep(.el-textarea__inner:focus) {
  border-color: #c3a3ff;
  box-shadow: 0 0 0 2px rgba(195, 163, 255, 0.2), inset 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* 浅色主题评论输入框 */
[data-theme="light"] .comment-textarea :deep(.el-textarea__inner) {
  background: rgba(241, 241, 241, 0.9);
  color: #dad5d5;
  border: 1px solid rgba(139, 92, 246, 0.3);
  box-shadow: inset 0 2px 5px rgba(0, 0, 0, 0.05);
}

[data-theme="light"] .comment-textarea :deep(.el-textarea__inner:focus) {
  border-color: #8b5cf6;
  box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.2), inset 0 2px 5px rgba(0, 0, 0, 0.05);
}

.comment-controls {
  display: flex;
  justify-content: flex-end;
}

.submit-comment-btn {
  padding: 0.7rem 1.4rem !important;
  transition: all 0.3s ease;
  min-width: 140px;
}

/* 深色主题提交按钮 */
[data-theme="dark"] .submit-comment-btn {
  background: linear-gradient(135deg, #352458, #2d1e4a) !important;
  border: 1px solid rgba(120, 70, 200, 0.5) !important;
}

[data-theme="dark"] .submit-comment-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #42306a, #352458) !important;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(120, 70, 200, 0.4);
}

[data-theme="dark"] .submit-comment-btn:disabled {
  opacity: 0.6;
  background: linear-gradient(135deg, #2a1d45, #231838) !important;
}

/* 浅色主题提交按钮 */
[data-theme="light"] .submit-comment-btn {
  background: linear-gradient(135deg, #8b5cf6, #a855f7) !important;
  border: 1px solid rgba(139, 92, 246, 0.5) !important;
  color: white !important;
}

[data-theme="light"] .submit-comment-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #7c3aed, #8b5cf6) !important;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(139, 92, 246, 0.4);
}

[data-theme="light"] .submit-comment-btn:disabled {
  opacity: 0.6;
  background: linear-gradient(135deg, #d1d5db, #9ca3af) !important;
  color: #6b7280 !important;
}

.comment-timeline-item {
  margin-bottom: 1rem;
}

.comment-card {
  border-radius: 10px !important;
  transition: all 0.3s ease;
  padding: 0.8rem !important;
}

.comment-card:hover {
  transform: translateY(-3px);
}

/* 深色主题评论卡片 */
[data-theme="dark"] .comment-card {
  background: rgba(35, 35, 45, 0.6) !important;
  border: 1px solid rgba(120, 70, 200, 0.2) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
}

[data-theme="dark"] .comment-card:hover {
  box-shadow: 0 8px 20px rgba(120, 70, 200, 0.3) !important;
  border-color: rgba(120, 70, 200, 0.4) !important;
}

/* 浅色主题评论卡片 */
[data-theme="light"] .comment-card {
  background: rgba(255, 255, 255, 0.9) !important;
  border: 1px solid rgba(139, 92, 246, 0.2) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08) !important;
}

[data-theme="light"] .comment-card:hover {
  box-shadow: 0 8px 20px rgba(139, 92, 246, 0.2) !important;
  border-color: rgba(139, 92, 246, 0.4) !important;
}

/* 移动端评论卡片样式优化 */
@media (max-width: 768px) {
  .comment-timeline-item {
    margin-bottom: 0.6rem; /* 减小评论卡片之间的间距 */
  }
  
  .comment-card {
    padding: 0.5rem !important; /* 减小卡片内部的padding */
    border-radius: 8px !important; /* 减小圆角 */
  }
}

.comment-item-content {
  display: flex;
  flex-direction: column;
  gap: 0.4rem;
}

.comment-user-info {
  display: flex;
  align-items: flex-start;
  gap: 0.8rem;
  margin-bottom: 0.2rem;
}

.comment-user-details {
  display: flex;
  flex-direction: column;
  line-height: 1.2;
}

.comment-username {
  font-weight: 600;
  font-size: 1rem;
  transition: color 0.3s ease;
}

.comment-time {
  font-size: 0.75rem;
  color: #999;
  margin-top: 0.15rem;
}

.comment-text {
  line-height: 1.4;
  margin: 0.3rem 0;
  padding: 0.6rem;
  border-radius: 6px;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

/* 移动端评论内容样式优化 */
@media (max-width: 768px) {
  .comment-item-content {
    gap: 0.3rem; /* 减小元素间距 */
  }
  
  .comment-user-info {
    gap: 0.5rem; /* 减小头像与用户信息间距 */
    margin-bottom: 0.1rem; /* 减小底部边距 */
  }
  
  .comment-user-details {
    line-height: 1.1; /* 减小行高 */
  }
  
  .comment-username {
    font-size: 0.9rem; /* 减小用户名字体 */
  }
  
  .comment-time {
    font-size: 0.7rem; /* 减小时间字体 */
    margin-top: 0.1rem; /* 减小上边距 */
  }
  
  .comment-text {
    margin: 0.2rem 0; /* 减小上下边距 */
    padding: 0.4rem; /* 减小内边距 */
    font-size: 0.85rem; /* 减小字体大小 */
    border-radius: 4px; /* 减小圆角 */
  }
}

/* 深色主题评论文本 */
[data-theme="dark"] .comment-username {
  color: #c3a3ff;
}

[data-theme="dark"] .comment-time {
  color: #7a7a8c;
}

[data-theme="dark"] .comment-text {
  color: #e0e0e0;
  background: rgba(25, 25, 35, 0.4);
  border-left: 3px solid rgba(120, 70, 200, 0.4);
}

/* 浅色主题评论文本 */
[data-theme="light"] .comment-username {
  color: #8b5cf6;
}

[data-theme="light"] .comment-time {
  color: #888;
}

[data-theme="light"] .comment-text {
  color: #333;
  background: rgba(248, 250, 252, 0.6);
  border-left: 3px solid rgba(139, 92, 246, 0.4);
}

.comment-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  padding-top: 0.4rem;
  border-top: 1px dashed rgba(120, 70, 200, 0.2);
}

.comment-action-btn {
  border-radius: 14px !important;
  padding: 0.3rem 0.5rem !important;
  display: flex !important;
  align-items: center !important;
  gap: 0.25rem !important;
  transition: all 0.3s ease !important;
}

/* 移动端评论操作按钮优化 */
@media (max-width: 768px) {
  .comment-actions {
    gap: 0.3rem; /* 减小按钮之间的间距 */
    padding-top: 0.3rem; /* 减小顶部边距 */
  }
  
  .comment-action-btn {
    padding: 0.2rem 0.4rem !important; /* 减小按钮内边距 */
    border-radius: 12px !important; /* 减小圆角 */
    font-size: 0.8rem !important; /* 减小字体大小 */
  }
  
  .comment-action-btn :deep(i), .comment-action-btn :deep(.el-icon) {
    font-size: 0.9rem !important; /* 减小图标大小 */
  }
}

.comment-action-btn:hover {
  background-color: rgba(120, 70, 200, 0.15) !important;
  transform: translateY(-2px);
}

.comment-action-btn:focus {
  box-shadow: 0 0 0 2px rgba(120, 70, 200, 0.2) !important;
}

.comment-action-btn.el-button--primary {
    color: #c3a3ff !important;
}

.comment-action-btn.el-button--primary:hover {
  background-color: rgba(120, 70, 200, 0.25) !important;
}

.comment-action-btn.el-button--danger {
  color: #ff9898 !important;
}

.comment-action-btn.el-button--danger:hover {
  background-color: rgba(255, 76, 76, 0.15) !important;
}

.login-message {
  color: #c3a3ff;
  font-size: 1.1rem;
  margin-bottom: 1rem;
}

.comments-divider {
  margin: 1.5rem 0;
  /* 从2rem 0减小到1.5rem 0 */
}

.comments-divider :deep(.el-divider__text) {
  background: rgba(30, 30, 40, 0.85);
  color: #c3a3ff;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 8px;
  padding: 0.2rem 1.2rem;
  box-shadow: 0 2px 8px rgba(120, 70, 200, 0.08);
}

.comments-divider :deep(.el-divider) {
  border-top: 1.5px solid #352458;
  opacity: 0.7;
}

@media (max-width: 600px) {
  .comment-sort-options {
    margin-bottom: 1rem;
  }

  .comment-sort-options :deep(.el-radio-group) {
    width: 100%;
    max-width: 320px;
    background: rgba(35, 35, 45, 0.7);
    border-radius: 10px;
    padding: 3px;
  }

  .comment-sort-options :deep(.el-radio-button__inner) {
    font-size: 0.9rem;
    padding: 0.4rem 0.5rem;
    min-width: 0;
    flex: 1;
  }

  .comment-sort-options :deep(.el-radio-button__inner i) {
    font-size: 1em;
    margin-right: 0.2em;
  }
}

:deep(.el-timeline-item__tail) {
  border-left: 2px solid rgba(120, 70, 200, 0.4);
}

:deep(.el-timeline-item__node--hollow) {
  background: #1a1a20;
  border: 2px solid #c3a3ff;
}

/* 不再使用时间线的默认时间戳 */
:deep(.el-timeline-item__timestamp) {
  display: none;
}

/* 移动端时间线组件优化 */
@media (max-width: 768px) {
  :deep(.el-timeline-item__tail) {
    border-left-width: 1px; /* 减小时间线宽度 */
  }
  
  :deep(.el-timeline-item__node--hollow) {
    width: 10px; /* 减小节点尺寸 */
    height: 10px; /* 减小节点尺寸 */
    border-width: 1px; /* 减小边框宽度 */
  }
  
  :deep(.el-timeline-item__wrapper) {
    padding-left: 20px; /* 减小左侧内边距 */
  }
  
  :deep(.el-timeline-item__content) {
    padding-left: 0; /* 移除内容左边距 */
  }
}

/* 小屏幕设备进一步优化 */
@media (max-width: 480px) {
  :deep(.el-timeline-item__node--hollow) {
    width: 8px; /* 进一步减小节点尺寸 */
    height: 8px; /* 进一步减小节点尺寸 */
  }
  
  :deep(.el-timeline-item__wrapper) {
    padding-left: 18px; /* 进一步减小左侧内边距 */
  }
  
  :deep(.el-timeline) {
    padding-left: 0; /* 移除时间线左边距 */
  }
}

:deep(.el-empty__description) {
  color: #c3a3ff;
}

/* 自定义消息框样式 */
:deep(.custom-message-box) {
  background: rgba(30, 30, 40, 0.95);
  border: 1px solid rgba(120, 70, 200, 0.4);
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
}

:deep(.custom-message-box .el-message-box__title) {
  color: #c3a3ff;
}

:deep(.custom-message-box .el-message-box__content) {
  color: #e0e0e0;
}

:deep(.custom-message-box .el-message-box__btns .el-button) {
  background: rgba(45, 30, 74, 0.8);
  border-color: rgba(120, 70, 200, 0.5);
  color: #e0e0e0;
}

:deep(.custom-message-box .el-button--primary) {
  background: linear-gradient(135deg, #352458, #2d1e4a);
}

.related-products {
  max-width: 1400px;
  margin: 1.5rem auto;
  padding-top: 1rem;
  position: relative;
  z-index: 1;
  transition: border-color 0.3s ease;
}

.related-products h2 {
  margin-bottom: 1rem;
  font-size: 1.4rem;
  transition: all 0.3s ease;
}

/* 深色主题相关产品 */
[data-theme="dark"] .related-products {
  border-top: 1px solid rgba(120, 70, 200, 0.2);
}

[data-theme="dark"] .related-products h2 {
  color: #c3a3ff;
  text-shadow: 0 0 10px rgba(120, 70, 200, 0.5);
}

/* 浅色主题相关产品 */
[data-theme="light"] .related-products {
  border-top: 1px solid rgba(139, 92, 246, 0.2);
}

[data-theme="light"] .related-products h2 {
  color: #8b5cf6;
  text-shadow: 0 0 10px rgba(139, 92, 246, 0.3);
}

.related-products-row {
  display: flex;
  overflow-x: auto;
  gap: 0.6rem;
  padding: 0.4rem 0.2rem;
  margin-bottom: 1rem;
  scroll-snap-type: x mandatory;
  -webkit-overflow-scrolling: touch;
  position: relative;
  cursor: grab;
}

/* 基础滚动条样式 - 默认浅色主题 */
.related-products-row::-webkit-scrollbar {
  height: 8px !important;
  width: 8px !important;
}

.related-products-row::-webkit-scrollbar-track {
  background: rgba(240, 240, 245, 0.8) !important;
  border-radius: 10px !important;
}

.related-products-row::-webkit-scrollbar-thumb {
  background: rgba(139, 92, 246, 0.5) !important;
  border-radius: 10px !important;
  border: 1px solid rgba(139, 92, 246, 0.3) !important;
}

.related-products-row::-webkit-scrollbar-thumb:hover {
  background: rgba(139, 92, 246, 0.7) !important;
  border-color: rgba(139, 92, 246, 0.5) !important;
}

.related-products-row.dragging {
  cursor: grabbing;
  scroll-behavior: auto;
}

.related-product-col {
  flex: 0 0 240px;
  min-width: 240px;
  scroll-snap-align: start;
  padding: 0.15rem;
  transition: transform 0.2s;
}

.related-card {
  border-radius: 10px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 240px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

[data-theme="dark"] .related-card {
  background: linear-gradient(135deg, #23232a, #181822);
  color: #e0e0e0;
  border: 1px solid rgba(120, 70, 200, 0.12);
  box-shadow: 0 4px 16px rgba(120, 70, 200, 0.15);
}

[data-theme="light"] .related-card {
  background: linear-gradient(135deg, #ffffff, #f8f9fa);
  color: #333333;
  border: 1px solid rgba(139, 92, 246, 0.12);
  box-shadow: 0 4px 16px rgba(139, 92, 246, 0.15);
}

.related-card:hover {
  transform: translateY(-5px);
}

[data-theme="dark"] .related-card:hover {
  box-shadow: 0 8px 25px rgba(195, 163, 255, 0.3);
  border-color: rgba(195, 163, 255, 0.5);
}

[data-theme="light"] .related-card:hover {
  box-shadow: 0 8px 25px rgba(139, 92, 246, 0.2);
  border-color: rgba(139, 92, 246, 0.5);
}

.related-card-img {
  width: 100%;
  height: 110px;
  object-fit: cover;
  border-radius: 8px;
  margin-bottom: 0.7rem;
}

.related-card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 0 2px;
}

.related-card-title {
  font-size: 1rem;
  font-weight: bold;
  margin-bottom: 0.3rem;
  line-height: 1.3;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  min-height: auto;
  display: block;
  width: 100%;
  cursor: pointer;
  transition: color 0.3s ease;
}

[data-theme="dark"] .related-card-title {
  color: #ffffff;
}

[data-theme="light"] .related-card-title {
  color: #333333;
}

.related-card-price-row {
  display: flex;
  align-items: center;
  gap: 0.4rem;
  margin-bottom: 0.2rem;
}

.related-card .current-price {
  color: #c3a3ff;
  font-weight: bold;
  font-size: 1rem;
}

.related-card .original-price {
  font-size: 0.9rem;
  text-decoration: line-through;
  color: #888;
}

.discount-tag {
  margin-left: 0.2em;
  transition: all 0.3s ease;
}

.related-card-stats {
  display: flex;
  gap: 0.7rem;
  font-size: 0.85rem;
  margin-top: 0.2rem;
  align-items: center;
  transition: color 0.3s ease;
}

[data-theme="dark"] .related-card-stats {
  color: #a9a9a9;
}

[data-theme="light"] .related-card-stats {
  color: #666666;
}

.dark-card {
  background: linear-gradient(135deg, #23232a, #181822) !important;
  color: #e0e0e0 !important;
  border: 1px solid rgba(120, 70, 200, 0.12) !important;
  box-shadow: 0 4px 16px rgba(120, 70, 200, 0.15) !important;
}

/* Update media queries for responsive design */
@media (max-width: 768px) {
  .related-product-col {
    flex: 0 0 160px; /* Reduce width for mobile */
    min-width: 160px; /* Reduce min-width for mobile */
    padding: 0.15rem;
  }
  
  .related-card {
    min-height: 220px; /* Reduce height for mobile */
    border-radius: 8px;
  }
  
  .related-card-img {
    height: 100px !important; /* Reduce image height for mobile */
    padding: 5px; /* Reduce padding for mobile */
  }
  
  .related-card-content {
    padding: 6px; /* Reduce padding for mobile */
  }
  
  .related-card-title {
    font-size: 0.9rem; /* Smaller font for mobile */
    line-height: 1.2;
  }
  
  .current-price {
    font-size: 0.9rem;
  }
  
  .original-price {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .related-product-col {
    flex: 0 0 140px; /* Further reduce for smaller screens */
    min-width: 140px;
  }
  
  .related-card {
    min-height: 180px;
  }
  
  .related-card-img {
    height: 80px !important;
    padding: 4px;
  }
  
  .related-card-content {
    padding: 5px;
  }
  
  .related-card-title {
    font-size: 0.85rem;
  }
}

/* Add mobile-specific styles for related products */
@media (max-width: 768px) {
  .related-card-img {
    width: 100% !important;
    height: 100px !important;
    border-radius: 6px !important;
    object-fit: contain !important;
    padding: 5px !important;
  }
}

@media (max-width: 480px) {
  .related-card-img {
    height: 80px !important;
    padding: 4px !important;
  }
}

/* Update mobile styles for related products */
@media (max-width: 768px) {
  .related-card-img {
    width: 100% !important;
    height: 100px !important;
    border-radius: 6px !important;
    object-fit: contain !important;
    padding: 5px !important;
  }
  
  .related-card-content {
    padding: 8px 6px !important;
  }
  
  .related-card-title {
    font-size: 0.9rem !important;
    margin-bottom: 4px !important;
    line-height: 1.2 !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
  }
  
  .related-card-price-row {
    margin-bottom: 3px !important;
    display: flex !important;
    align-items: center !important;
    flex-wrap: wrap !important;
  }
  
  .current-price {
    font-size: 0.9rem !important;
    color: #c3a3ff !important;
  }
  
  .original-price {
    font-size: 0.8rem !important;
    margin-left: 4px !important;
  }
  
  .discount-tag {
    font-size: 0.7rem !important;
    padding: 0 4px !important;
    height: 16px !important;
    line-height: 16px !important;
    margin-left: 4px !important;
  }
  
  .related-card-stats {
    font-size: 0.8rem !important;
    margin-top: 3px !important;
    color: #a9a9a9 !important;
  }
}

@media (max-width: 480px) {
  .related-card-img {
    height: 80px !important;
    padding: 4px !important;
  }
  
  .related-card-content {
    padding: 6px 5px !important;
  }
  
  .related-card-title {
    font-size: 0.85rem !important;
    margin-bottom: 3px !important;
  }
  
  .current-price {
    font-size: 0.85rem !important;
  }
  
  .original-price {
    font-size: 0.75rem !important;
  }
  
  .discount-tag {
    font-size: 0.65rem !important;
    padding: 0 3px !important;
    height: 14px !important;
    line-height: 14px !important;
  }
  
  .related-card-stats {
    font-size: 0.75rem !important;
    margin-top: 2px !important;
  }
}

.comment-images {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.comment-image-item {
  width: 100px;
  height: 100px;
  object-fit: cover;
  border-radius: 5px;
  border: 2px solid rgba(120, 70, 200, 0.3);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
}

/* 移动端评论图片展示区域优化 */
@media (max-width: 768px) {
  .comment-images {
    gap: 0.3rem; /* 减小图片之间的间距 */
    margin-top: 0.3rem; /* 减小上边距 */
  }
  
  .comment-image-item {
    width: 80px; /* 减小图片尺寸 */
    height: 80px; /* 减小图片尺寸 */
    border-width: 1px; /* 减小边框宽度 */
    border-radius: 4px; /* 减小圆角 */
  }
}

/* 小屏幕设备进一步优化 */
@media (max-width: 480px) {
  .comment-image-item {
    width: 70px; /* 进一步减小图片尺寸 */
    height: 70px; /* 进一步减小图片尺寸 */
  }
  
  /* 当有多个图片时进行优化布局 */
  .comment-images:has(.comment-image-item:nth-child(3)) .comment-image-item {
    width: 65px;
    height: 65px;
  }
}

.image-error {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100px;
  height: 100px;
  background: rgba(255, 76, 76, 0.15);
  border-radius: 5px;
  border: 2px dashed rgba(255, 76, 76, 0.4);
}

.image-error .el-icon {
  font-size: 2rem;
  color: #ff9898;
}

.image-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100px;
  height: 100px;
  background: rgba(30, 30, 40, 0.5);
  border-radius: 5px;
  border: 2px dashed rgba(120, 70, 200, 0.3);
}

.image-loading .el-icon {
  font-size: 2rem;
  color: #c3a3ff;
}

.comment-upload-section {
  margin-top: 1rem;
  padding: 1rem;
  border-radius: 8px;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  transition: all 0.3s ease;
  /* 默认浅色主题 */
  background: rgba(248, 250, 252, 0.8);
  border: 1px solid rgba(139, 92, 246, 0.2);
}

/* 深色主题 */
[data-theme="dark"] .comment-upload-section {
  background: rgba(30, 30, 40, 0.5);
  border: 1px solid rgba(120, 70, 200, 0.2);
}

/* 强制覆盖Element Plus上传框样式 */
.comment-upload-section :deep(.el-upload--picture-card) {
  transition: all 0.3s ease !important;
  width: 100px !important;
  height: 100px !important;
  position: relative !important;
  overflow: hidden !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  border-radius: 8px !important;
  /* 默认浅色主题 */
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.9), rgba(241, 245, 249, 0.8)) !important;
  border: 2px dashed rgba(139, 92, 246, 0.4) !important;
  color: #6366f1 !important;
  box-shadow: 0 3px 10px rgba(139, 92, 246, 0.15) !important;
}

/* 深色主题上传框 */
[data-theme="dark"] .comment-upload-section :deep(.el-upload--picture-card) {
  background: linear-gradient(135deg, rgba(53, 36, 88, 0.7), rgba(45, 30, 74, 0.7)) !important;
  border: 2px dashed rgba(120, 70, 200, 0.5) !important;
  color: #c3a3ff !important;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3) !important;
}

.upload-placeholder {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  height: 100% !important;
  width: 100% !important;
  transition: transform 0.3s ease, filter 0.3s ease !important;
}

.comment-upload-section :deep(.el-upload--picture-card:hover) .upload-placeholder {
  transform: scale(1.08) !important;
}

.upload-icon {
  font-size: 2.2rem !important;
  margin-bottom: 8px !important;
  animation: iconPulse 2s infinite ease-in-out !important;
  transition: transform 0.3s ease, color 0.3s ease, filter 0.3s ease !important;
  /* 默认浅色主题 */
  color: #6366f1 !important;
  filter: drop-shadow(0 0 5px rgba(139, 92, 246, 0.4)) !important;
}

/* 深色主题上传图标 */
[data-theme="dark"] .upload-icon {
  color: #c3a3ff !important;
  filter: drop-shadow(0 0 5px rgba(120, 70, 200, 0.5)) !important;
}

@keyframes iconPulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.15); }
  100% { transform: scale(1); }
}

.comment-upload-section :deep(.el-upload--picture-card:hover) .upload-icon {
  transform: scale(1.2) !important;
  /* 默认浅色主题悬停 */
  color: #4f46e5 !important;
  filter: drop-shadow(0 0 10px rgba(139, 92, 246, 0.6)) !important;
}

/* 深色主题上传图标悬停 */
[data-theme="dark"] .comment-upload-section :deep(.el-upload--picture-card:hover) .upload-icon {
  color: #ffffff !important;
  filter: drop-shadow(0 0 10px rgba(120, 70, 200, 0.8)) !important;
}

.upload-text {
  font-size: 0.85rem !important;
  transition: color 0.3s ease, text-shadow 0.3s ease !important;
  /* 默认浅色主题 */
  color: #6366f1 !important;
  text-shadow: 0 0 5px rgba(139, 92, 246, 0.4) !important;
}

/* 深色主题上传文字 */
[data-theme="dark"] .upload-text {
  color: #c3a3ff !important;
  text-shadow: 0 0 5px rgba(120, 70, 200, 0.5) !important;
}

.comment-upload-section :deep(.el-upload--picture-card:hover) .upload-text {
  /* 默认浅色主题悬停 */
  color: #4f46e5 !important;
  text-shadow: 0 0 8px rgba(139, 92, 246, 0.6) !important;
}

/* 深色主题上传文字悬停 */
[data-theme="dark"] .comment-upload-section :deep(.el-upload--picture-card:hover) .upload-text {
  color: #ffffff !important;
  text-shadow: 0 0 8px rgba(120, 70, 200, 0.8) !important;
}

.comment-upload-section :deep(.el-upload--picture-card:hover) {
  transform: translateY(-3px) !important;
  cursor: pointer !important;
  /* 默认浅色主题悬停 */
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.2), rgba(139, 92, 246, 0.1)) !important;
  box-shadow: 0 5px 15px rgba(139, 92, 246, 0.3) !important;
  border-color: rgba(139, 92, 246, 0.6) !important;
}

/* 深色主题上传框悬停 */
[data-theme="dark"] .comment-upload-section :deep(.el-upload--picture-card:hover) {
  background: linear-gradient(135deg, rgba(75, 55, 130, 0.8), rgba(65, 45, 100, 0.8)) !important;
  box-shadow: 0 5px 15px rgba(120, 70, 200, 0.4) !important;
  border-color: rgba(120, 70, 200, 0.7) !important;
}

.comment-upload-section :deep(.el-upload-list--picture-card .el-upload-list__item) {
  background: rgba(35, 35, 45, 0.7) !important;
  border: 2px solid rgba(120, 70, 200, 0.3) !important;
  width: 100px !important;
  height: 100px !important;
  transition: all 0.3s ease !important;
  overflow: hidden !important;
  border-radius: 8px !important;
}

.comment-upload-section :deep(.el-upload-list--picture-card .el-upload-list__item:hover) {
  transform: translateY(-3px) !important;
  box-shadow: 0 5px 15px rgba(120, 70, 200, 0.4) !important;
  border-color: rgba(120, 70, 200, 0.6) !important;
  background: rgba(45, 45, 55, 0.8) !important;
}

/* 预览图片样式 */
.comment-upload-section :deep(.el-upload-list__item-thumbnail) {
  object-fit: cover;
  width: 100%;
  height: 100%;
}

/* 上传图标动画 */
@keyframes uploadPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.1);
    opacity: 1;
  }
}

.comment-upload-section :deep(.el-upload--picture-card .el-icon) {
  animation: uploadPulse 2s ease-in-out infinite;
}

/* 上传信息展示 */
.upload-info {
  display: flex;
  align-items: center;
}

.upload-info :deep(.el-tag) {
  background: rgba(35, 45, 40, 0.6);
  border-color: rgba(80, 180, 100, 0.4);
  padding: 0.3rem 0.8rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.upload-info :deep(.el-tag .el-icon) {
  font-size: 1.2rem;
  color: #5ee094;
}

/* 已移除images-count样式 */

.image-zoom-indicator {
  position: absolute;
  bottom: 5px;
  right: 5px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.comment-image-item:hover .image-zoom-indicator {
  opacity: 1;
}

.image-zoom-indicator .el-icon {
  font-size: 14px;
  color: #fff;
}

/* 图片预览样式 */
.image-preview-info {
  position: absolute;
  bottom: 15px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.7);
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 14px;
  color: #fff;
}

/* 加载更多评论按钮 */
.load-more-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 1.5rem;
  margin-bottom: 1rem;
}

.load-more-btn {
  padding: 0.6rem 2rem !important;
  border-radius: 20px !important;
  transition: all 0.3s ease;
}

/* 深色主题加载更多按钮 */
[data-theme="dark"] .load-more-btn {
  background: rgba(30, 30, 40, 0.6) !important;
  border: 1px solid rgba(120, 70, 200, 0.3) !important;
  color: #c3a3ff !important;
}

/* 深色主题悬停效果 */
[data-theme="dark"] .load-more-btn:hover {
  background: rgba(53, 36, 88, 0.7) !important;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(120, 70, 200, 0.3);
  border-color: rgba(120, 70, 200, 0.5) !important;
}

/* 浅色主题加载更多按钮 */
[data-theme="light"] .load-more-btn {
  background: rgba(232, 208, 245, 0.8) !important;
  border: 1px solid rgba(142, 45, 226, 0.3) !important;
  color: #8e2de2 !important;
}

/* 浅色主题悬停效果 */
[data-theme="light"] .load-more-btn:hover {
  background: rgba(217, 181, 239, 0.9) !important;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(142, 45, 226, 0.2);
  border-color: rgba(142, 45, 226, 0.5) !important;
}

.comments-counter {
  margin-top: 0.5rem;
  font-size: 0.9rem;
  color: #888;
}

/* 未登录提示样式 */
.login-prompt-container {
  border-radius: 15px;
  padding: 2rem 1rem;
  margin-bottom: 2rem;
  text-align: center;
  transition: all 0.3s ease;
}

/* 深色主题登录提示容器 */
[data-theme="dark"] .login-prompt-container {
  background: rgba(30, 30, 40, 0.6);
  border: 1px dashed rgba(120, 70, 200, 0.3);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* 浅色主题登录提示容器 */
[data-theme="light"] .login-prompt-container {
  background: rgba(248, 250, 252, 0.8);
  border: 1px dashed rgba(142, 45, 226, 0.3);
  box-shadow: 0 4px 12px rgba(142, 45, 226, 0.1);
}

.custom-icon-container {
  display: flex;
  justify-content: center;
  margin-bottom: 1.5rem;
}

.animated-lock-icon {
  position: relative;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 深色主题锁图标 */
[data-theme="dark"] .lock-icon {
  font-size: 3rem;
  color: #c3a3ff;
  z-index: 2;
  filter: drop-shadow(0 0 10px rgba(120, 70, 200, 0.5));
}

/* 浅色主题锁图标 */
[data-theme="light"] .lock-icon {
  font-size: 3rem;
  color: #8e2de2;
  z-index: 2;
  filter: drop-shadow(0 0 10px rgba(142, 45, 226, 0.3));
}

/* 深色主题脉冲圆圈 */
[data-theme="dark"] .pulse-circle {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: rgba(120, 70, 200, 0.15);
  z-index: 1;
  animation: pulse 2s infinite;
}

/* 浅色主题脉冲圆圈 */
[data-theme="light"] .pulse-circle {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: rgba(142, 45, 226, 0.1);
  z-index: 1;
  animation: pulse 2s infinite;
}

/* 登录提示结果组件样式 */
/* 深色主题结果组件标题 */
[data-theme="dark"] .login-prompt-container :deep(.el-result__title) {
  color: #c3a3ff;
}

/* 深色主题结果组件副标题 */
[data-theme="dark"] .login-prompt-container :deep(.el-result__subtitle) {
  color: #a9a9a9;
}

/* 浅色主题结果组件标题 */
[data-theme="light"] .login-prompt-container :deep(.el-result__title) {
  color: #8e2de2;
}

/* 浅色主题结果组件副标题 */
[data-theme="light"] .login-prompt-container :deep(.el-result__subtitle) {
  color: #666666;
}

@keyframes pulse {
  0% {
    transform: scale(0.8);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.3;
  }
  100% {
    transform: scale(0.8);
    opacity: 0.7;
  }
}

.login-btn-animated {
  position: relative;
  overflow: hidden;
  min-width: 180px;
  transition: all 0.3s ease;
}

/* 深色主题登录按钮 */
[data-theme="dark"] .login-btn-animated {
  background: linear-gradient(135deg, #352458, #2d1e4a) !important;
  border: 1px solid rgba(120, 70, 200, 0.5) !important;
  color: #c3a3ff !important;
}

/* 深色主题登录按钮悬停效果 */
[data-theme="dark"] .login-btn-animated:hover {
  background: linear-gradient(135deg, #42306a, #352458) !important;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(120, 70, 200, 0.4);
}

/* 浅色主题登录按钮 */
[data-theme="light"] .login-btn-animated {
  background: linear-gradient(135deg, #8b5cf6, #a855f7) !important;
  border: 1px solid rgba(139, 92, 246, 0.5) !important;
  color: white !important;
}

/* 浅色主题登录按钮悬停效果 */
[data-theme="light"] .login-btn-animated:hover {
  background: linear-gradient(135deg, #7c3aed, #8b5cf6) !important;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(139, 92, 246, 0.4);
}

.login-icon {
  margin-right: 0.5rem;
  font-size: 1.2rem;
}

.btn-shine {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0) 100%);
  animation: shine 3s infinite;
}

@keyframes shine {
  0% {
    left: -100%;
  }
  20% {
    left: 100%;
  }
  100% {
    left: 100%;
  }
}

/* 空评论状态样式 */
.empty-comments-icon {
  margin: 2rem 0;
}

.animated-comments-icon {
  position: relative;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.comments-icon {
  font-size: 3rem;
  color: #c3a3ff;
  z-index: 2;
  filter: drop-shadow(0 0 10px rgba(120, 70, 200, 0.5));
}

.comments-pulse-circle {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: rgba(120, 70, 200, 0.15);
  z-index: 1;
  animation: commentsPulse 2s infinite;
}

@keyframes commentsPulse {
  0% {
    transform: scale(0.8);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.3;
  }
  100% {
    transform: scale(0.8);
    opacity: 0.7;
  }
}

.no-comments-subtitle {
  font-size: 1.1rem;
  margin: 1rem 0;
  transition: color 0.3s ease;
  /* 默认浅色主题 */
  color: #6366f1;
}

/* 深色主题 */
[data-theme="dark"] .no-comments-subtitle {
  color: #c3a3ff;
}

.be-first-comment-btn {
  min-width: 200px;
  margin-top: 1rem;
  transition: all 0.3s ease;
  /* 默认浅色主题 */
  background: linear-gradient(135deg, #8b5cf6, #a855f7) !important;
  border: 1px solid rgba(139, 92, 246, 0.5) !important;
  color: white !important;
}

/* 深色主题 */
[data-theme="dark"] .be-first-comment-btn {
  background: linear-gradient(135deg, #352458, #2d1e4a) !important;
  border: 1px solid rgba(120, 70, 200, 0.5) !important;
  color: #c3a3ff !important;
}

.be-first-comment-btn:hover {
  transform: translateY(-2px);
}

/* 浅色主题悬停 */
.be-first-comment-btn:hover {
  background: linear-gradient(135deg, #7c3aed, #8b5cf6) !important;
  box-shadow: 0 5px 15px rgba(139, 92, 246, 0.4);
}

/* 深色主题悬停 */
[data-theme="dark"] .be-first-comment-btn:hover {
  background: linear-gradient(135deg, #42306a, #352458) !important;
  box-shadow: 0 5px 15px rgba(120, 70, 200, 0.4);
}

.be-first-comment-btn i {
  margin-right: 0.5rem;
  font-size: 1.2rem;
}

/* All comments divider */
.all-comments-button {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  margin: 1.5rem 0;
  transition: all 0.3s ease;
  gap: 3px; /* 添加很小的间距，保持两条线之间有一点间隔 */
}

/* 深色主题 */
[data-theme="dark"] .all-comments-button {
  color: #c3a3ff;
}

.all-comments-button::before,
.all-comments-button::after {
  display: none;
}

.cyber-line {
  position: relative;
  height: 2px;
  flex: 1;
  overflow: hidden;
  /* 默认浅色主题 */
  background: linear-gradient(90deg, rgba(139, 92, 246, 0.2), rgba(139, 92, 246, 0.6), rgba(139, 92, 246, 0.2));
}

/* 深色主题 */
[data-theme="dark"] .cyber-line {
  background: linear-gradient(90deg, rgba(120, 70, 200, 0.2), rgba(120, 70, 200, 0.8), rgba(120, 70, 200, 0.2));
}

.cyber-line::after {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(195, 163, 255, 0.8) 50%, 
    transparent 100%);
  animation: cyber-line-pulse 3s infinite;
}

.cyber-line.right::after {
  animation-delay: 1.5s;
}

@keyframes cyber-line-pulse {
  0% { left: -100%; }
  50%, 100% { left: 100%; }
}

.cyber-badge {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.4rem 1.2rem;
  margin: 0 1rem;
  border-radius: 4px;
  z-index: 1;
  overflow: hidden;
  transition: all 0.3s ease;
  /* 默认浅色主题 */
  background: rgba(248, 250, 252, 0.9);
  border: 1px solid rgba(139, 92, 246, 0.4);
  box-shadow: 0 0 10px rgba(139, 92, 246, 0.2);
}

/* 深色主题 */
[data-theme="dark"] .cyber-badge {
  background: rgba(30, 30, 40, 0.8);
  border: 1px solid rgba(120, 70, 200, 0.5);
  box-shadow: 0 0 10px rgba(120, 70, 200, 0.3);
}

.cyber-badge::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  z-index: -1;
  /* 默认浅色主题 */
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(139, 92, 246, 0.6) 50%,
    transparent 100%);
}

/* 深色主题 */
[data-theme="dark"] .cyber-badge::before {
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(195, 163, 255, 0.8) 50%,
    transparent 100%);
}

.cyber-badge::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  z-index: -1;
  /* 默认浅色主题 */
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(139, 92, 246, 0.6) 50%,
    transparent 100%);
}

/* 深色主题 */
[data-theme="dark"] .cyber-badge::after {
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(195, 163, 255, 0.8) 50%,
    transparent 100%);
}

.cyber-badge span {
  position: relative;
  display: inline-block;
  padding: 0 0.5rem;
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  letter-spacing: 1px;
  text-transform: uppercase;
  /* 默认浅色主题 */
  background: linear-gradient(180deg, #6366f1, #4f46e5);
  text-shadow: 0 0 5px rgba(139, 92, 246, 0.4);
}

/* 深色主题 */
[data-theme="dark"] .cyber-badge span {
  background: linear-gradient(180deg, #c3a3ff, #352458);
  text-shadow: 0 0 5px rgba(195, 163, 255, 0.5);
}

.cyber-badge span::before {
  content: attr(data-text);
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  filter: blur(3px);
  opacity: 0.7;
  /* 默认浅色主题 */
  background: linear-gradient(180deg, #6366f1, #4f46e5);
}

/* 深色主题 */
[data-theme="dark"] .cyber-badge span::before {
  background: linear-gradient(180deg, #c3a3ff, #352458);
}

.cyber-badge-glow {
  position: absolute;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at center, rgba(120, 70, 200, 0.3) 0%, transparent 70%);
  animation: pulse-glow 2s infinite;
  z-index: -1;
}

@keyframes pulse-glow {
  0%, 100% { opacity: 0.3; transform: scale(0.9); }
  50% { opacity: 0.6; transform: scale(1.1); }
}

.cyber-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 4px;
  background: linear-gradient(135deg, #352458, #2d1e4a);
  color: #c3a3ff;
  box-shadow: 0 0 5px rgba(120, 70, 200, 0.5);
  transition: all 0.3s ease;
}

.cyber-icon.left {
  margin-right: 0.8rem;
  transform: rotate(-10deg);
  animation: cyber-icon-float 3s infinite;
}

.cyber-icon.right {
  margin-left: 0.8rem;
  transform: rotate(10deg);
  animation: cyber-icon-float 3s infinite;
  animation-delay: 1.5s;
}

.cyber-icon i {
  font-size: 0.9rem;
  filter: drop-shadow(0 0 3px rgba(195, 163, 255, 0.8));
}

@keyframes cyber-icon-float {
  0%, 100% { transform: translateY(0) rotate(-10deg); }
  50% { transform: translateY(-3px) rotate(-10deg); }
}

.cyber-icon.right i {
  animation: blink 1.5s infinite;
}

@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* 广告图片区域 */
.advertisement-section {
  background-color: #f0f0f0;
  /* padding: 20px; */
  border-radius: 10px;
  margin-bottom: 20px;
}

.advertisement-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.advertisement-image {
  max-width: 100%;
  height: auto;
  border-radius: 5px;
}

@media (max-width: 768px) {
  .related-products-row {
    gap: 1rem;
    padding: 0.6rem;
    margin-left: 0.6rem;
    margin-right: 0.6rem;
    padding-bottom: 1rem;
  }
  
  .related-product-col {
    flex: 0 0 220px;
    min-width: 220px;
    padding: 0.25rem;
    margin-bottom: 0.5rem;
  }
  
  .related-card-img {
    height: 210px !important; /* 增加图片高度，与RelatedProductCard组件保持一致 */
    padding: 8px;
    object-fit: contain !important;
  }
  
  .related-card-content {
    padding: 14px 14px 8px; /* 减少底部padding */
    min-height: 110px; /* 减小内容区域最小高度 */
  }
}

@media (max-width: 480px) {
  .related-product-col {
    flex: 0 0 200px;
    min-width: 200px;
    margin-right: 0.4rem;
  }
  
  .related-card-img {
    height: 190px !important; /* 增加图片高度，与RelatedProductCard组件保持一致 */
    padding: 5px;
  }
  
  .related-card-content {
    padding: 12px 12px 6px; /* 减少底部padding */
    min-height: 100px; /* 减小内容区域最小高度 */
  }
}

.product-name {
  font-size: 1.1rem;
  margin: 0; /* 减小上下间距 */
  color: #ffffff;
  text-shadow: none;
  white-space: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  cursor: pointer;
  font-weight: normal;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .product-detail {
    padding: 0; /* 移除内边距 */
    padding-top: 0; /* 移除顶部内边距，确保导航栏无间隙 */
  }
  
  .floating-back-button {
    position: fixed;
    top: 10px; /* 调整位置靠近顶部 */
    left: 10px;
    z-index: 1000; /* 确保在最上层 */
    padding: 0.5rem 0.8rem;
    font-size: 0.9rem;
  }
  
  .product-container {
    flex-direction: column;
    gap: 0.5rem; /* 进一步减小间距 */
    padding: 0 0.5rem; /* 保持左右内边距 */
    margin-top: 0; /* 移除顶部边距 */
  }
  
  .product-images {
    flex: none;
    width: 100%;
    margin-bottom: 0.3rem; /* 减小底部间距 */
  }
  
  .main-product-image {
    width: 100%; /* 移动端图片占满宽度 */
    padding-bottom: 100%; /* 移动端保持1:1比例 */
    margin-bottom: 0.3rem; /* 减小与历史购买信息的间距 */
  }
  
  .product-info {
    flex: none;
    width: 100%;
  }
  
  .product-info-section {
    padding: 0.1rem; /* 减小内边距 */
  }
  
  .product-info-bottom {
    padding-top: 0.1rem; /* 减小顶部内边距 */
  }
  
  .price-amount {
    font-size: 2.5rem;
  }
  
  .product-name {
    font-size: 1rem;
    -webkit-line-clamp: 2; /* 限制为两行 */
    line-clamp: 2;
  }
  
  .agent-platforms {
    flex-direction: column; /* 垂直排列货币选项 */
    gap: 0.8rem;
    width: 100%;
  }
  
  .platform-option {
    position: relative;
    padding: 0.8rem 0.5rem;
    border-radius: 8px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    /* 默认浅色主题 */
    background: rgba(248, 250, 252, 0.95);
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.15);
    border: 1px solid rgba(139, 92, 246, 0.2);
  }

  /* 移动端深色主题 */
  [data-theme="dark"] .platform-option {
    background: rgba(25, 25, 35, 0.8);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
    border: 1px solid rgba(80, 80, 100, 0.2);
  }
  
  .platform-value {
    font-size: 1.5rem;
    font-weight: 700;
    position: relative;
    z-index: 2;
    text-align: center;
    transition: all 0.3s ease;
    /* 默认浅色主题 */
    color: #1f2937;
    text-shadow: 0 0 8px rgba(139, 92, 246, 0.3);
  }

  /* 移动端深色主题文字 */
  [data-theme="dark"] .platform-value {
    color: #ffffff;
    text-shadow: 0 0 8px rgba(120, 70, 200, 0.5);
  }
  
  .platform-icons-row {
    justify-content: center;
    flex-wrap: wrap;
    gap: 0.5rem;
    padding: 0.5rem 0; /* 减少上下内边距 */
    background: rgba(25, 15, 40, 0.5);
    border-radius: 8px;
    margin-bottom: 0.33rem; /* 将1rem减少2/3，变为0.33rem */
  }
  
  .platform-icon-item {
    width: 55px;
    height: 55px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  }
  
  .platform-icon-item img {
    width: 80%;
    height: 80%;
    object-fit: contain;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.4));
  }
  
  /* 代理平台显示为现代卡片形式 */
  .agent-platforms {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.8rem;
    width: 100%;
    margin: 0.27rem 0 0.8rem; /* 将顶部间距0.8rem减少2/3，变为0.27rem，底部间距略微减小 */
  }
  
  .buy-now-btn {
    padding: 0.9rem;
    font-size: 1.1rem;
    font-weight: 600;
    background: #9c27b0;
    box-shadow: 0 4px 12px rgba(156, 39, 176, 0.5);
    border-radius: 8px;
    margin-top: 0.8rem;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
  }
  
  .buy-now-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, 
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 0.3) 50%,
      rgba(255, 255, 255, 0) 100%);
    transition: left 0.7s;
  }
  
  .buy-now-btn:active::before {
    left: 100%;
  }
  
  /* 评论区和相关产品区域样式 */
  .comments-section, 
  .related-products,
  .product-gallery {
    padding: 0 0.5rem;
    margin-top: 0.5rem; /* 减小顶部间距 */
  }
  
  .comments-card :deep(.el-card__header) {
    padding: 0.6rem 0.8rem;
  }
  
  .comments-card :deep(.el-card__body) {
    padding: 0.8rem;
  }
  
  .comment-form {
    flex-direction: column;
    gap: 0.5rem;
    padding: 0.8rem;
  }
  
  .comment-avatar-input {
    align-self: center;
  }
  
  .comment-image-item {
    width: 80px;
    height: 80px;
  }
  
  .comment-upload-section :deep(.el-upload--picture-card) {
    width: 80px !important;
    height: 80px !important;
  }
  
  .sort-buttons {
    width: 100%;
  }
  
  .sort-btn {
    padding: 6px 12px;
    font-size: 0.85rem;
    min-width: auto;
    flex: 1;
  }
  
  .comment-action-btn {
    padding: 0.3rem 0.5rem !important;
    font-size: 0.85rem !important;
  }
  
  /* 移除旧的导航栏样式，使用全局样式 */
}

/* 更小屏幕的适配 */
@media (max-width: 480px) {
  .product-container {
    padding: 0 0.3rem;
  }
  
  .price-amount {
    font-size: 2rem;
  }
  
  .product-name {
    font-size: 0.9rem;
  }
  
  .platform-icon-item {
    width: 45px;
    height: 45px;
  }
  
  /* 更小屏幕的代理平台样式 */
  .agent-platforms {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
  }
  
  .platform-option::before {
    /* 浅色主题移动端 */
    opacity: 0.15;
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.1), transparent);
  }

  /* 深色主题移动端伪元素 */
  [data-theme="dark"] .platform-option::before {
    opacity: 0.25;
    background: linear-gradient(135deg, rgba(120, 70, 200, 0.1), transparent);
  }
  
  .comment-image-item {
    width: 70px;
    height: 70px;
  }
  
  .comment-upload-section :deep(.el-upload--picture-card) {
    width: 70px !important;
    height: 70px !important;
  }
  
  .sort-btn {
    padding: 5px 8px;
    font-size: 0.8rem;
  }
  
  .comment-action-btn {
    padding: 0.25rem 0.4rem !important;
    font-size: 0.8rem !important;
  }
  
  .historical-purchase-info {
    width: 100%;
  }
}

/* 导航栏通用样式 - 适用于PC和移动端 */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  margin: 0;
  padding: 0;
  background: rgba(20, 10, 30, 0.8);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(120, 70, 200, 0.3);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

/* 确保导航栏在最顶部 */
:deep(.el-header), :deep(.app-header), :deep(header) {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  margin: 0;
  padding: 0;
}

.group-count {
  background-color: rgba(0, 0, 0, 0.5);
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  font-size: 0.9rem;
}

.image-error-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  background-color: rgba(30, 30, 40, 0.7);
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.image-error-placeholder .el-icon {
  font-size: 3rem;
  color: #ff9898;
  margin-bottom: 0.5rem;
}

.image-error-placeholder p {
  font-size: 1rem;
  color: #ffffff;
  text-shadow: 0 0 5px rgba(120, 70, 200, 0.5);
}

.image-loading-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
  background: rgba(30, 30, 40, 0.7);
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.image-loading-placeholder .el-icon {
  font-size: 3rem;
  color: #c3a3ff;
}

/* 图片加载占位符的主题适配 */
.image-loading-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  transition: all 0.3s ease;
}

[data-theme="dark"] .image-loading-placeholder {
  background: rgba(30, 30, 40, 0.7);
}

[data-theme="light"] .image-loading-placeholder {
  background: rgba(248, 250, 252, 0.8);
}

[data-theme="dark"] .image-loading-placeholder .el-icon {
  font-size: 3rem;
  color: #c3a3ff;
}

[data-theme="light"] .image-loading-placeholder .el-icon {
  font-size: 3rem;
  color: #8b5cf6;
}

/* 图片错误占位符的主题适配 */
.image-error-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  transition: all 0.3s ease;
}

[data-theme="dark"] .image-error-placeholder {
  background-color: rgba(30, 30, 40, 0.7);
}

[data-theme="light"] .image-error-placeholder {
  background-color: rgba(248, 250, 252, 0.8);
}

[data-theme="dark"] .image-error-placeholder .el-icon {
  font-size: 3rem;
  color: #ff9898;
  margin-bottom: 0.5rem;
}

[data-theme="light"] .image-error-placeholder .el-icon {
  font-size: 3rem;
  color: #f43f5e;
  margin-bottom: 0.5rem;
}

[data-theme="dark"] .image-error-placeholder p {
  color: #ffffff;
  text-shadow: 0 0 5px rgba(120, 70, 200, 0.5);
}

[data-theme="light"] .image-error-placeholder p {
  color: #333333;
  text-shadow: none;
}

/* 无画廊项目提示的主题适配 */
.no-gallery-items {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 3rem;
  border-radius: 8px;
  transition: all 0.3s ease;
}

[data-theme="dark"] .no-gallery-items {
  background: rgba(35, 35, 45, 0.8);
  border: 1px dashed rgba(120, 70, 200, 0.3);
}

[data-theme="light"] .no-gallery-items {
  background: rgba(255, 255, 255, 0.9);
  border: 1px dashed rgba(139, 92, 246, 0.3);
}

[data-theme="dark"] .no-gallery-items p {
  color: #a0a0a0;
  font-size: 1.1rem;
}

[data-theme="light"] .no-gallery-items p {
  color: #666666;
  font-size: 1.1rem;
}

/* 轮播组的主题适配 */
.carousel-group {
  background-size: cover;
  border-radius: 10px;
  overflow: hidden;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

[data-theme="dark"] .carousel-group {
  background: rgba(35, 35, 45, 0.8);
  border: 1px solid rgba(120, 70, 200, 0.3);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

[data-theme="light"] .carousel-group {
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(139, 92, 246, 0.2);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* 计数器样式的主题适配 */
.group-count {
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

[data-theme="dark"] .group-count {
  background-color: rgba(0, 0, 0, 0.5);
  color: #c3a3ff;
}

[data-theme="light"] .group-count {
  background-color: rgba(139, 92, 246, 0.1);
  color: #8b5cf6;
}

/* Element Plus轮播组件主题适配 */
.product-gallery :deep(.el-carousel__indicators) {
  transition: all 0.3s ease;
}

[data-theme="dark"] .product-gallery :deep(.el-carousel__indicators) {
  background: rgba(30, 30, 40, 0.5);
}

[data-theme="light"] .product-gallery :deep(.el-carousel__indicators) {
  background: rgba(255, 255, 255, 0.5);
}

[data-theme="dark"] .product-gallery :deep(.el-carousel__indicator--button) {
  background: rgba(120, 70, 200, 0.3);
}

[data-theme="light"] .product-gallery :deep(.el-carousel__indicator--button) {
  background: rgba(139, 92, 246, 0.3);
}

[data-theme="dark"] .product-gallery :deep(.el-carousel__indicator.is-active button) {
  background: rgba(120, 70, 200, 0.7);
}

[data-theme="light"] .product-gallery :deep(.el-carousel__indicator.is-active button) {
  background: rgba(139, 92, 246, 0.7);
}

/* 图片预览样式主题适配 */
[data-theme="dark"] .product-gallery :deep(.el-image-viewer__wrapper) {
  background: rgba(0, 0, 0, 0.9);
}

[data-theme="light"] .product-gallery :deep(.el-image-viewer__wrapper) {
  background: rgba(0, 0, 0, 0.8);
}

[data-theme="dark"] .product-gallery :deep(.el-image-viewer__btn) {
  color: #c3a3ff;
}

[data-theme="light"] .product-gallery :deep(.el-image-viewer__btn) {
  color: #8b5cf6;
}

[data-theme="dark"] .product-gallery :deep(.el-image-viewer__actions) {
  background: rgba(30, 30, 40, 0.8);
}

[data-theme="light"] .product-gallery :deep(.el-image-viewer__actions) {
  background: rgba(40, 40, 50, 0.8);
}

/* Related Products 相关产品模块主题适配 */
.related-card {
  border-radius: 10px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 240px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

[data-theme="dark"] .related-card {
  background: linear-gradient(135deg, #23232a, #181822);
  color: #e0e0e0;
  border: 1px solid rgba(120, 70, 200, 0.12);
  box-shadow: 0 4px 16px rgba(120, 70, 200, 0.15);
}

[data-theme="light"] .related-card {
  background: linear-gradient(135deg, #ffffff, #f8f9fa);
  color: #333333;
  border: 1px solid rgba(139, 92, 246, 0.12);
  box-shadow: 0 4px 16px rgba(139, 92, 246, 0.15);
}

.related-card:hover {
  transform: translateY(-5px);
}

[data-theme="dark"] .related-card:hover {
  box-shadow: 0 8px 25px rgba(195, 163, 255, 0.3);
  border-color: rgba(195, 163, 255, 0.5);
}

[data-theme="light"] .related-card:hover {
  box-shadow: 0 8px 25px rgba(139, 92, 246, 0.2);
  border-color: rgba(139, 92, 246, 0.5);
}

.related-card-title {
  font-size: 1rem;
  font-weight: bold;
  margin-bottom: 0.3rem;
  line-height: 1.3;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  min-height: auto;
  display: block;
  width: 100%;
  cursor: pointer;
  transition: color 0.3s ease;
}

[data-theme="dark"] .related-card-title {
  color: #ffffff;
}

[data-theme="light"] .related-card-title {
  color: #333333;
}

[data-theme="dark"] .related-card .current-price {
  color: #c3a3ff;
}

[data-theme="light"] .related-card .current-price {
  color: #8b5cf6;
}

[data-theme="dark"] .related-card .original-price {
  color: #888888;
}

[data-theme="light"] .related-card .original-price {
  color: #666666;
}

.related-card-stats {
  display: flex;
  gap: 0.7rem;
  font-size: 0.85rem;
  margin-top: 0.2rem;
  align-items: center;
  transition: color 0.3s ease;
}

[data-theme="dark"] .related-card-stats {
  color: #a9a9a9;
}

[data-theme="light"] .related-card-stats {
  color: #666666;
}

.discount-tag {
  margin-left: 0.2em;
  transition: all 0.3s ease;
}

[data-theme="dark"] .discount-tag {
  background: rgba(120, 70, 200, 0.25);
  color: #c3a3ff;
}

[data-theme="light"] .discount-tag {
  background: rgba(139, 92, 246, 0.15);
  color: #8b5cf6;
}

/* 相关产品标题栏主题适配 */
.related-products h2 {
  margin-bottom: 1rem;
  font-size: 1.4rem;
  transition: all 0.3s ease;
}

[data-theme="dark"] .related-products {
  border-top: 1px solid rgba(120, 70, 200, 0.2);
}

[data-theme="dark"] .related-products h2 {
  color: #c3a3ff;
  text-shadow: 0 0 10px rgba(120, 70, 200, 0.5);
}

[data-theme="light"] .related-products {
  border-top: 1px solid rgba(139, 92, 246, 0.2);
}

[data-theme="light"] .related-products h2 {
  color: #8b5cf6;
  text-shadow: 0 0 10px rgba(139, 92, 246, 0.3);
}

/* 相关产品滚动条主题适配 - 最高优先级 */
html[data-theme="dark"] body .product-detail .related-products .related-products-row::-webkit-scrollbar {
  height: 8px !important;
  width: 8px !important;
}

html[data-theme="dark"] body .product-detail .related-products .related-products-row::-webkit-scrollbar-track {
  background: rgba(30, 30, 40, 0.6) !important;
  border-radius: 10px !important;
}

html[data-theme="dark"] body .product-detail .related-products .related-products-row::-webkit-scrollbar-thumb {
  background: rgba(120, 70, 200, 0.7) !important;
  border-radius: 10px !important;
  border: 1px solid rgba(120, 70, 200, 0.4) !important;
}

html[data-theme="dark"] body .product-detail .related-products .related-products-row::-webkit-scrollbar-thumb:hover {
  background: rgba(120, 70, 200, 0.9) !important;
  border-color: rgba(120, 70, 200, 0.6) !important;
}

html[data-theme="light"] body .product-detail .related-products .related-products-row::-webkit-scrollbar {
  height: 8px !important;
  width: 8px !important;
}

html[data-theme="light"] body .product-detail .related-products .related-products-row::-webkit-scrollbar-track {
  background: rgba(240, 240, 245, 0.8) !important;
  border-radius: 10px !important;
}

html[data-theme="light"] body .product-detail .related-products .related-products-row::-webkit-scrollbar-thumb {
  background: rgba(139, 92, 246, 0.5) !important;
  border-radius: 10px !important;
  border: 1px solid rgba(139, 92, 246, 0.3) !important;
}

html[data-theme="light"] body .product-detail .related-products .related-products-row::-webkit-scrollbar-thumb:hover {
  background: rgba(139, 92, 246, 0.7) !important;
  border-color: rgba(139, 92, 246, 0.5) !important;
}

/* 备用方案 - 超高优先级选择器 */
html[data-theme="dark"] .related-products-row::-webkit-scrollbar {
  height: 8px !important;
  width: 8px !important;
}

html[data-theme="dark"] .related-products-row::-webkit-scrollbar-track {
  background: rgba(30, 30, 40, 0.6) !important;
  border-radius: 10px !important;
}

html[data-theme="dark"] .related-products-row::-webkit-scrollbar-thumb {
  background: rgba(120, 70, 200, 0.7) !important;
  border-radius: 10px !important;
  border: 1px solid rgba(120, 70, 200, 0.4) !important;
}

html[data-theme="dark"] .related-products-row::-webkit-scrollbar-thumb:hover {
  background: rgba(120, 70, 200, 0.9) !important;
  border-color: rgba(120, 70, 200, 0.6) !important;
}

html[data-theme="light"] .related-products-row::-webkit-scrollbar {
  height: 8px !important;
  width: 8px !important;
}

html[data-theme="light"] .related-products-row::-webkit-scrollbar-track {
  background: rgba(240, 240, 245, 0.8) !important;
  border-radius: 10px !important;
}

html[data-theme="light"] .related-products-row::-webkit-scrollbar-thumb {
  background: rgba(139, 92, 246, 0.5) !important;
  border-radius: 10px !important;
  border: 1px solid rgba(139, 92, 246, 0.3) !important;
}

html[data-theme="light"] .related-products-row::-webkit-scrollbar-thumb:hover {
  background: rgba(139, 92, 246, 0.7) !important;
  border-color: rgba(139, 92, 246, 0.5) !important;
}

/* 终极备用方案 - 覆盖基础样式和全局样式 */
body[data-theme="light"] .related-products-row::-webkit-scrollbar-track,
html[data-theme="light"] .related-products-row::-webkit-scrollbar-track,
[data-theme="light"] .related-products-row::-webkit-scrollbar-track {
  background: rgba(240, 240, 245, 0.8) !important;
  border-radius: 10px !important;
}

body[data-theme="light"] .related-products-row::-webkit-scrollbar-thumb,
html[data-theme="light"] .related-products-row::-webkit-scrollbar-thumb,
[data-theme="light"] .related-products-row::-webkit-scrollbar-thumb {
  background: rgba(139, 92, 246, 0.5) !important;
  border-radius: 10px !important;
  border: 1px solid rgba(139, 92, 246, 0.3) !important;
}

body[data-theme="dark"] .related-products-row::-webkit-scrollbar-track,
html[data-theme="dark"] .related-products-row::-webkit-scrollbar-track,
[data-theme="dark"] .related-products-row::-webkit-scrollbar-track {
  background: rgba(30, 30, 40, 0.6) !important;
  border-radius: 10px !important;
}

body[data-theme="dark"] .related-products-row::-webkit-scrollbar-thumb,
html[data-theme="dark"] .related-products-row::-webkit-scrollbar-thumb,
[data-theme="dark"] .related-products-row::-webkit-scrollbar-thumb {
  background: rgba(120, 70, 200, 0.7) !important;
  border-radius: 10px !important;
  border: 1px solid rgba(120, 70, 200, 0.4) !important;
}

/* 强制覆盖全局样式 - 最高优先级 */
.related-products-row::-webkit-scrollbar-track {
  background: rgba(240, 240, 245, 0.8) !important;
  border-radius: 10px !important;
}

.related-products-row::-webkit-scrollbar-thumb {
  background: rgba(139, 92, 246, 0.5) !important;
  border-radius: 10px !important;
  border: 1px solid rgba(139, 92, 246, 0.3) !important;
}

/* 深色主题强制覆盖 */
html[data-theme="dark"] .related-products-row::-webkit-scrollbar-track {
  background: rgba(30, 30, 40, 0.6) !important;
  border-radius: 10px !important;
}

html[data-theme="dark"] .related-products-row::-webkit-scrollbar-thumb {
  background: rgba(120, 70, 200, 0.7) !important;
  border-radius: 10px !important;
  border: 1px solid rgba(120, 70, 200, 0.4) !important;
}

/* 加载更多按钮容器 */
.load-more-container {
  width: 100%;
  display: flex;
  justify-content: center;
  margin: 1rem 0 2rem;
}

.load-more-btn {
  padding: 0.8rem 2rem;
  border-radius: 30px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  border: none;
}

/* 深色主题加载更多按钮 */
[data-theme="dark"] .load-more-btn {
  background: linear-gradient(135deg, #352458, #2d1e4a);
  color: #c3a3ff;
  box-shadow: 0 4px 15px rgba(120, 70, 200, 0.3);
  border: 1px solid rgba(120, 70, 200, 0.3);
}

[data-theme="dark"] .load-more-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #42306a, #352458);
  box-shadow: 0 6px 20px rgba(120, 70, 200, 0.5);
  transform: translateY(-2px);
}

/* 浅色主题加载更多按钮 */
[data-theme="light"] .load-more-btn {
  background: linear-gradient(135deg, #8b5cf6, #a855f7);
  color: white;
  box-shadow: 0 4px 15px rgba(139, 92, 246, 0.3);
  border: 1px solid rgba(139, 92, 246, 0.3);
}

[data-theme="light"] .load-more-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #7c3aed, #8b5cf6);
  box-shadow: 0 6px 20px rgba(139, 92, 246, 0.5);
  transform: translateY(-2px);
}

.load-more-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.loading-spinner-small {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 组标题中的总数样式 */
.group-total {
  font-size: 0.9rem;
  opacity: 0.7;
  font-weight: normal;
  margin-left: 8px;
}
/* 相关商品滑动栏和卡片样式增强 */

/* 滚动条样式优化 */
.product-detail .related-products .related-products-row {
  scrollbar-width: thin;
  scrollbar-color: #8b5cf6 rgba(240, 240, 255, 0.8);
}

/* 滚动条WebKit样式 */
.product-detail .related-products .related-products-row::-webkit-scrollbar {
  height: 8px;
}

.product-detail .related-products .related-products-row::-webkit-scrollbar-track {
  background: rgba(240, 240, 255, 0.8);
  border-radius: 4px;
}

.product-detail .related-products .related-products-row::-webkit-scrollbar-thumb {
  background: #8b5cf6;
  border-radius: 4px;
  border: 2px solid rgba(240, 240, 255, 0.8);
}

.product-detail .related-products .related-products-row::-webkit-scrollbar-thumb:hover {
  background: #7c3aed;
}

/* 深色主题滚动条样式 */
.product-detail[data-theme="dark"] .related-products .related-products-row {
  scrollbar-color: #9333ea rgba(25, 25, 30, 0.8);
}

.product-detail[data-theme="dark"] .related-products .related-products-row::-webkit-scrollbar-track {
  background: rgba(25, 25, 30, 0.8);
}

.product-detail[data-theme="dark"] .related-products .related-products-row::-webkit-scrollbar-thumb {
  background: #9333ea;
  border: 2px solid rgba(25, 25, 30, 0.8);
}

.product-detail[data-theme="dark"] .related-products .related-products-row::-webkit-scrollbar-thumb:hover {
  background: #a855f7;
}

/* 相关商品区域背景 */
.product-detail[data-theme="light"] .related-products {
  background: rgba(255, 255, 255, 0.85);
  border: 1px solid rgba(139, 92, 246, 0.25);
  box-shadow: 0 6px 16px rgba(139, 92, 246, 0.12);
  border-radius: 16px;
  padding: 1.5rem;
  margin: 2rem auto;
}

/* 卡片容器样式 */
.product-detail .related-products .related-product-col {
  padding: 4px;
  border-radius: 12px;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

/* 白天模式卡片轮廓 */
.product-detail[data-theme="light"] .related-products .related-product-col {
  background: white;
  box-shadow: 0 2px 8px rgba(139, 92, 246, 0.15);
  border: 1px solid rgba(139, 92, 246, 0.12);
}

.product-detail[data-theme="light"] .related-products .related-product-col:hover {
  box-shadow: 0 4px 16px rgba(139, 92, 246, 0.25);
  transform: translateY(-4px);
}

/* 深色模式卡片轮廓 */
.product-detail[data-theme="dark"] .related-products .related-product-col {
  background: rgba(25, 25, 35, 0.7);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.25);
  border: 1px solid rgba(120, 70, 200, 0.2);
}

.product-detail[data-theme="dark"] .related-products .related-product-col:hover {
  box-shadow: 0 6px 20px rgba(120, 70, 200, 0.3);
  transform: translateY(-4px);
}

/* 相关商品滑动栏和卡片样式增强 - 增加特异性和!important */

/* 卡片容器样式 - 使用更具体的选择器和!important */
.product-detail .related-products .related-products-row .related-product-col {
  padding: 4px !important;
  border-radius: 12px !important;
  overflow: hidden !important;
  transition: transform 0.3s ease, box-shadow 0.3s ease !important;
  margin: 4px !important;
}

/* 白天模式卡片轮廓 */
html[data-theme="light"] .product-detail .related-products .related-products-row .related-product-col,
body[data-theme="light"] .product-detail .related-products .related-products-row .related-product-col,
.product-detail[data-theme="light"] .related-products .related-products-row .related-product-col {
  background-color: white !important;
  box-shadow: 0 2px 8px rgba(139, 92, 246, 0.15) !important;
  border: 1px solid rgba(139, 92, 246, 0.2) !important;
}

html[data-theme="light"] .product-detail .related-products .related-products-row .related-product-col:hover,
body[data-theme="light"] .product-detail .related-products .related-products-row .related-product-col:hover,
.product-detail[data-theme="light"] .related-products .related-products-row .related-product-col:hover {
  box-shadow: 0 4px 16px rgba(139, 92, 246, 0.25) !important;
  transform: translateY(-4px) !important;
}

/* 深色模式卡片轮廓 */
html[data-theme="dark"] .product-detail .related-products .related-products-row .related-product-col,
body[data-theme="dark"] .product-detail .related-products .related-products-row .related-product-col,
.product-detail[data-theme="dark"] .related-products .related-products-row .related-product-col {
  background-color: rgba(25, 25, 35, 0.7) !important;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.25) !important;
  border: 1px solid rgba(120, 70, 200, 0.2) !important;
}

html[data-theme="dark"] .product-detail .related-products .related-products-row .related-product-col:hover,
body[data-theme="dark"] .product-detail .related-products .related-products-row .related-product-col:hover,
.product-detail[data-theme="dark"] .related-products .related-products-row .related-product-col:hover {
  box-shadow: 0 6px 20px rgba(120, 70, 200, 0.3) !important;
  transform: translateY(-4px) !important;
}

/* 主图轮播样式 */
.main-image-carousel {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.main-carousel {
  width: 100%;
  height: 100%;
  border-radius: 15px; /* 与主容器保持一致的圆角 */
  overflow: hidden;
  position: relative;
}

.main-carousel-item {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent; /* 移除背景色以去除白边 */
}

.main-image-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0; /* 移除内边距以去除白边 */
  box-sizing: border-box;
}

.main-image-wrapper img {
  width: 100%;
  height: 100%;
  object-fit: cover; /* 改为cover以填充整个1:1区域 */
  border-radius: 15px; /* 与主容器保持一致的圆角 */
  box-shadow: none; /* 移除阴影 */
}

.single-main-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent; /* 移除背景色 */
  border-radius: 15px; /* 与主容器保持一致的圆角 */
  padding: 0; /* 移除内边距以去除白边 */
  box-sizing: border-box;
}

.single-main-image img {
  width: 100%;
  height: 100%;
  object-fit: cover; /* 改为cover以填充整个1:1区域 */
  border-radius: 15px; /* 与主容器保持一致的圆角 */
  box-shadow: none; /* 移除阴影 */
}

/* 页码指示器样式 */
.carousel-counter {
  position: absolute;
  bottom: 15px;
  right: 15px;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  backdrop-filter: blur(4px);
  z-index: 10;
}

/* 隐藏原生轮播指示器 */
.main-carousel .el-carousel__indicators {
  display: none;
}

/* 轮播箭头样式 */
.main-carousel .el-carousel__arrow {
  background-color: rgba(139, 92, 246, 0.8);
  border: none;
  color: white;
}

.main-carousel .el-carousel__arrow:hover {
  background-color: #8b5cf6;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .main-image-wrapper,
  .single-main-image {
    padding: 0; /* 移动端也去除内边距 */
  }

  .custom-indicators {
    margin-top: 10px;
    gap: 6px;
  }

  .custom-indicator {
    width: 10px;
    height: 10px;
  }
}
</style>
