<template>
  <div class="user-posts-container">
    <div class="page-header">
      <h1>My Posts</h1>
      <p>View all the posts you have published</p>
    </div>

    <!-- Loading state -->
    <div v-if="isLoading" class="loading-container">
      <div class="loading-spinner"></div>
      <p>Loading your posts...</p>
    </div>

    <!-- Error message -->
    <div v-if="error && !isLoading" class="error-message">
      <p>{{ error }}</p>
      <button @click="fetchUserPosts">Retry</button>
    </div>

    <!-- Empty state -->
    <div v-if="posts.length === 0 && !isLoading && !error" class="empty-state">
      <div class="empty-content">
        <i class="fas fa-file-alt empty-icon"></i>
        <p>You haven't published any posts yet</p>
        <router-link to="/community/post/create" class="create-post-btn">
          <i class="fas fa-plus"></i> Create New Post
        </router-link>
      </div>
    </div>

    <!-- Post list -->
    <div class="posts-grid">
      <div v-for="post in posts" :key="post.postId || post.title" class="post-card" @click="viewPostDetail(post.postId)">
        <div class="post-image" v-if="post.firstImage">
          <img :src="post.firstImage" alt="Post Image" class="card-image">
        </div>
        <div class="post-header">
          <div class="post-author" v-if="post.avatarUrl">
            <img :src="post.avatarUrl" alt="User Avatar" class="author-avatar">
            <span class="author-name">{{ post.username }}</span>
          </div>
          <div class="post-date">
            <i class="far fa-calendar-alt"></i>
            {{ formatDate(post.postCreateTime) }}
          </div>
        </div>
        <h3 class="post-title">{{ post.title }}</h3>
        <p class="post-excerpt">{{ formatExcerpt(post.content) }}</p>
        <div class="post-footer">
          <div class="post-stats">
            <span class="post-stat"><i class="far fa-eye"></i> {{ post.views || 0 }}</span>
            <span class="post-stat"><i class="far fa-thumbs-up"></i> {{ post.likes || 0 }}</span>
            <span class="post-stat"><i class="far fa-comment"></i> {{ post.commentsCount || 0 }}</span>
            <span class="post-stat"><i class="far fa-bookmark"></i> {{ post.favorites || 0 }}</span>
          </div>
          <div class="post-actions">
            <!-- <button class="edit-btn" @click.stop="editPost(post)" title="编辑帖子">
              <i class="fas fa-pencil-alt"></i>
            </button> -->
            <button class="delete-btn" @click.stop="deletePost(post)" title="Delete Post">
              <i class="fas fa-trash-alt"></i>
            </button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Loading overlay for delete action -->
    <div v-if="isDeleting" class="delete-overlay">
      <div class="delete-spinner"></div>
      <p>Deleting post...</p>
    </div>
  </div>
</template>

<script>
import postsApi from '@/api/posts';
import { ElMessageBox, ElMessage } from 'element-plus';

export default {
  name: 'UserPostsView',
  data() {
    return {
      posts: [],
      isLoading: false,
      isDeleting: false,
      error: null
    }
  },
  created() {
    this.fetchUserPosts();
  },
  methods: {
    // Fetch user's published posts
    async fetchUserPosts() {
      this.isLoading = true;
      this.error = null;
      
      try {
        // Get user ID from localStorage
        const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
        const userId = userInfo.userId;
        
        if (!userId) {
          this.error = 'Not logged in or user info missing';
          this.$router.push('/login');
          return;
        }
        
        const response = await postsApi.getUserPosts(userId);
        
        if (response.code === 200 && Array.isArray(response.data)) {
          this.posts = response.data;
          console.log('Fetched user published posts:', this.posts);
        } else {
          this.error = response.msg || 'Failed to fetch posts';
        }
      } catch (error) {
        console.error('Error fetching user posts:', error);
        this.error = 'Failed to fetch posts, please try again later';
      } finally {
        this.isLoading = false;
      }
    },
    
    // View post detail
    viewPostDetail(postId) {
      this.$router.push(`/community/post/${postId}`);
    },
    
    // Edit post
    editPost(post) {
      this.$router.push(`/community/post/edit/${post.postId || post.id}`);
    },
    
    // Delete post
    async deletePost(post) {
      const postId = post.postId || post.id;
      if (!postId) {
        ElMessage.error('Could not get post ID');
        return;
      }
      
      // Use confirmation dialog
      ElMessageBox.confirm(
        'Are you sure you want to delete this post? This action cannot be undone.',
        'Delete Post',
        {
          confirmButtonText: 'Confirm Delete',
          cancelButtonText: 'Cancel',
          type: 'warning'
        }
      )
        .then(async () => {
          try {
            this.isDeleting = true; // Show loading state
            
            // Call the API to delete the post
            const response = await postsApi.deletePost(postId);
            
            if (response.code === 200) {
              // Remove the post from the list
              this.posts = this.posts.filter(p => (p.postId || p.id || p.title) !== postId);
              ElMessage({
                message: 'Post deleted successfully',
                type: 'success',
                duration: 2000
              });
            } else {
              ElMessage({
                message: response.msg || 'Failed to delete post',
                type: 'error',
                duration: 3000
              });
            }
          } catch (error) {
            console.error('Failed to delete post:', error);
            ElMessage({
              message: 'Operation failed, please try again later',
              type: 'error',
              duration: 3000
            });
          } finally {
            this.isDeleting = false; // Hide loading state
          }
        })
        .catch(() => {
          // Cancel operation
          ElMessage({
            type: 'info',
            message: 'Operation cancelled',
            duration: 1500
          });
        });
    },
    
    // Format date
    formatDate(dateString) {
      if (!dateString) return 'Unknown time';
      
      const date = new Date(dateString);
      const now = new Date();
      const diff = now - date;
      
      // If within an hour
      if (diff < 3600000) {
        const minutes = Math.floor(diff / 60000);
        return `${minutes === 0 ? 'Just now' : minutes + ' minutes ago'}`;
      }
      
      // If within today
      if (date.toDateString() === now.toDateString()) {
        return `Today ${date.getHours()}:${String(date.getMinutes()).padStart(2, '0')}`;
      }
      
      // If yesterday
      const yesterday = new Date(now);
      yesterday.setDate(yesterday.getDate() - 1);
      if (date.toDateString() === yesterday.toDateString()) {
        return `Yesterday ${date.getHours()}:${String(date.getMinutes()).padStart(2, '0')}`;
      }
      
      // Other times
      return `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`;
    },
    
    // Format excerpt
    formatExcerpt(content) {
      if (!content) return 'No content';
      if (content.length <= 100) return content;
      return content.substring(0, 100) + '...';
    }
  }
}
</script>

<style scoped>
.user-posts-container {
  width: 100%;
  max-width: 1400px; /* Increased max-width to match products page */
  margin: 0 auto;
  padding: 2rem 5rem 32px; /* Adjusted padding to match products page */
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  background: #121212; /* Dark background */
  background-image: radial-gradient(circle at 50% 50%, #1a1a1a 0%, #0a0a0a 100%); /* Gradient background */
  color: #e0e0e0; /* Light text color */
  min-height: 100vh;
}

.page-header {
  text-align: center;
  margin-bottom: 2rem;
}

.page-header h1 {
  font-size: 2.5rem; /* Increased font size */
  color: #c3a3ff; /* Purple color */
  margin-bottom: 0.8rem; /* Adjusted margin */
  font-weight: 700;
  text-shadow: 0 0 10px rgba(195, 163, 255, 0.5); /* Purple text shadow */
}

.page-header p {
  color: #a0a0a0; /* Slightly lighter grey for body text */
  font-size: 1.1rem; /* Increased font size */
}

/* Post grid layout - 4 per row */
.posts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)); /* Default 4 columns, responsive */
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.post-card {
  background: linear-gradient(135deg, #242428, #1a1a20); /* Dark card background */
  border-radius: 15px; /* Larger border radius */
  overflow: hidden;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4); /* Darker shadow */
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275); /* Bounce transition */
  cursor: pointer;
  border: 1px solid rgba(80, 80, 100, 0.3); /* Grey border */
  display: flex;
  flex-direction: column;
  position: relative; /* For glow effect */
}

.post-card:hover {
  transform: translateY(-5px) scale(1.03); /* Lift and scale on hover */
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.5), 0 0 20px rgba(120, 70, 200, 0.4); /* Darker and purple shadow */
  border-color: rgba(120, 70, 200, 0.5); /* Purple border on hover */
  background: linear-gradient(135deg, #2d2d35, #22222a); /* Darker gradient on hover */
  z-index: 2;
}

/* Mystical glow effect */
.post-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 50% 0%, rgba(120, 70, 200, 0.15), transparent 70%); /* Purple radial gradient */
  opacity: 0;
  transition: opacity 0.4s ease;
  pointer-events: none;
  border-radius: 15px;
  z-index: 1;
}

.post-card:hover::before {
  opacity: 1;
}

.post-image {
  margin: 0; /* Removed negative margins */
  border-radius: 15px 15px 0 0; /* Match card border radius */
  overflow: hidden;
  aspect-ratio: 16 / 9;
  position: relative;
  background-color: #1a1a1a; /* Dark background for cover area */
  border-bottom: 1px solid rgba(80, 80, 100, 0.3); /* Grey border */
  display: flex; /* Center image */
  align-items: center;
  justify-content: center;
}

.card-image {
  max-width: 100%; /* Ensure image fits */
  max-height: 100%; /* Ensure image fits */
  width: auto; /* Allow image to scale */
  height: auto; /* Allow image to scale */
  object-fit: contain; /* Use contain to show full image */
  transition: transform 0.3s ease;
  filter: drop-shadow(0 0 8px rgba(120, 70, 200, 0.3)); /* Purple drop shadow */
}

.post-card:hover .card-image {
  transform: scale(1.05);
  filter: drop-shadow(0 0 12px rgba(120, 70, 200, 0.5)); /* Darker purple drop shadow on hover */
}

.post-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  background: rgba(35, 35, 40, 0.8); /* Darker background for header */
  padding: 0 1.25rem 10px 1.25rem; /* Adjusted padding */
  border-bottom: 1px solid rgba(80, 80, 100, 0.3); /* Grey border */
  margin-top: 1.25rem; /* Added top margin */
}

.post-author {
  display: flex;
  align-items: center;
  gap: 0.6rem; /* Increased gap */
}

.author-avatar {
  width: 35px; /* Increased size */
  height: 35px; /* Increased size */
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid rgba(120, 70, 200, 0.3); /* Purple border */
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s;
}

.post-author:hover .author-avatar {
  transform: scale(1.1);
}

.author-name {
  font-size: 0.9rem; /* Increased font size */
  color: #e0e0e0; /* Light text color */
}

.post-date {
  font-size: 0.8rem; /* Increased font size */
  color: #888; /* Darker grey for date */
  display: flex;
  align-items: center;
  gap: 0.4rem; /* Increased gap */
}

.post-date i {
  color: #c3a3ff; /* Purple icon */
}

.post-title {
  font-size: 1.1rem; /* Increased font size */
  font-weight: 700; /* Bolder */
  margin-bottom: 0.8rem; /* Adjusted margin */
  line-height: 1.5; /* Increased line height */
  color: #e0e0e0; /* Light text color */
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  cursor: pointer;
  padding: 0 1.25rem; /* Adjusted padding */
  transition: color 0.3s;
}

.post-title:hover {
  color: #c3a3ff; /* Purple on hover */
}

.post-excerpt {
  font-size: 0.95rem; /* Increased font size */
  color: #a0a0a0; /* Slightly lighter grey for body text */
  margin-bottom: 1.2rem; /* Increased margin */
  line-height: 1.6; /* Increased line height */
  flex-grow: 1;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-word; /* Ensure long words break */
  padding: 0 1.25rem; /* Adjusted padding */
}

.post-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.85rem; /* Increased font size */
  color: #a9a9a9; /* Grey color */
  border-top: 1px solid rgba(80, 80, 100, 0.3); /* Grey border */
  padding: 0.8rem 1.25rem 1.25rem 1.25rem; /* Adjusted padding */
  margin-top: auto;
  background: rgba(35, 35, 40, 0.8); /* Darker background for footer */
}

.post-stats {
  display: flex;
  gap: 0rem;
  align-items: center;
  flex-wrap: wrap; /* Allow wrapping on smaller screens */
}

.post-actions {
  display: flex;
  align-items: center;
  gap: 0.8rem; /* Increased gap */
}

.post-stat {
  display: flex;
  align-items: center;
  gap: 0.4rem; /* Increased gap */
  transition: all 0.2s;
  padding: 4px 8px; /* Adjusted padding */
  border-radius: 20px; /* Pill shape */
  color: #a9a9a9; /* Grey color */
  font-size: 13px;
}

.post-stat:hover {
  background-color: rgba(120, 70, 200, 0.2); /* Light purple background on hover */
  color: #c3a3ff; /* Purple text on hover */
}

.post-stat i {
  color: #c3a3ff; /* Purple icon color */
}

.edit-btn, .delete-btn {
  width: 28px; /* Increased size */
  height: 28px; /* Increased size */
  border-radius: 50%;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem; /* Increased font size */
  padding: 0;
  flex-shrink: 0; /* Prevent shrinking */
}

.edit-btn {
  background-color: rgba(64, 158, 255, 0.1); /* Light blue background */
  color: #409EFF; /* Blue text */
  border: 1px solid rgba(64, 158, 255, 0.3); /* Blue border */
}

.edit-btn:hover {
  background-color: #409EFF; /* Blue background on hover */
  color: white;
  transform: scale(1.1); /* Scale on hover */
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.4); /* Blue shadow */
}

.delete-btn {
  width: 32px; /* Size remains the same */
  height: 32px; /* Size remains the same */
  border-radius: 50%;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem; /* Font size remains the same */
  padding: 0;
  background-color: rgba(245, 108, 108, 0.1); /* Light red background */
  color: #F56C6C; /* Red text */
  min-width: 32px; /* Ensure minimum size */
  min-height: 32px; /* Ensure minimum size */
  flex-shrink: 0; /* Prevent shrinking */
}

.delete-btn:hover {
  background-color: #F56C6C; /* Red background on hover */
  color: white;
  transform: scale(1.1); /* Scale on hover */
  box-shadow: 0 2px 8px rgba(245, 108, 108, 0.4); /* Red shadow */
}

/* Loading state */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60px 0; /* Increased padding */
  color: #c3a3ff; /* Purple text */
}

.loading-spinner {
  width: 40px; /* Size remains the same */
  height: 40px; /* Size remains the same */
  border: 4px solid rgba(195, 163, 255, 0.2); /* Light purple border */
  border-radius: 50%;
  border-top-color: #c3a3ff; /* Purple border top */
  animation: spin 1s linear infinite;
  margin-bottom: 16px; /* Margin remains the same */
  box-shadow: 0 0 20px rgba(120, 70, 200, 0.5); /* Purple shadow */
}

@keyframes spin {
  to { transform: rotate(360deg) }
}

/* Error message */
.error-message {
  background-color: rgba(245, 108, 108, 0.1); /* Light red background */
  border: 1px solid rgba(245, 108, 108, 0.3); /* Red border */
  border-radius: 12px; /* Larger border radius */
  padding: 24px; /* Increased padding */
  color: #f56c6c; /* Red text */
  text-align: center;
  margin: 24px 0; /* Increased margin */
  box-shadow: 0 4px 12px rgba(245, 108, 108, 0.2); /* Red shadow */
}

.error-message button {
  margin-top: 16px; /* Increased margin */
  padding: 10px 24px; /* Increased padding */
  background: linear-gradient(135deg, #f56c6c, #e05151); /* Red gradient */
  color: white; /* White text */
  border: none;
  border-radius: 30px; /* Pill shape */
  font-size: 14px; /* Font size remains the same */
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  box-shadow: 0 4px 12px rgba(245, 108, 108, 0.25);
}

.error-message button:hover {
  background: linear-gradient(135deg, #e05151, #c03030); /* Darker red gradient on hover */
  transform: translateY(-2px); /* Subtle lift */
  box-shadow: 0 6px 16px rgba(245, 108, 108, 0.3);
}

/* Empty state */
.empty-state {
  display: flex;
  justify-content: center;
  padding: 80px 20px; /* Increased padding */
  background-color: rgba(30, 30, 35, 0.9); /* Darker background */
  border-radius: 12px; /* Larger border radius */
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.4); /* Darker shadow */
  text-align: center;
  border: 1px solid rgba(80, 80, 100, 0.3); /* Grey border */
  color: #e0e0e0; /* Light text */
  margin: 24px auto; /* Center and add margin */
  max-width: 500px; /* Increased max-width */
}

.empty-content {
  max-width: 400px;
}

.empty-icon {
  font-size: 64px; /* Increased size */
  color: #c3a3ff; /* Purple icon */
  margin-bottom: 20px; /* Increased margin */
  opacity: 0.7;
}

.empty-state p {
  font-size: 1.2rem; /* Increased font size */
  color: #a0a0a0; /* Slightly lighter grey for body text */
  margin-bottom: 24px; /* Increased margin */
}

.create-post-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.8rem; /* Increased gap */
  padding: 10px 24px; /* Adjusted padding */
  background: linear-gradient(135deg, #323232, #252525); /* Dark gradient background */
  color: #c3a3ff; /* Purple text */
  border: 1px solid rgba(120, 70, 200, 0.3); /* Purple border */
  border-radius: 30px; /* Pill shape */
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s;
  box-shadow: 0 2px 8px rgba(70, 20, 100, 0.3); /* Purple shadow */
  font-size: 16px; /* Increased font size */
}

.create-post-btn:hover {
  background: linear-gradient(135deg, #404040, #303030); /* Darker gradient on hover */
  transform: translateY(-3px); /* Subtle lift */
  box-shadow: 0 5px 15px rgba(120, 70, 200, 0.4); /* Darker purple shadow */
  border-color: rgba(120, 70, 200, 0.6);
}

/* Responsive layout */
@media (max-width: 1400px) {
  .user-posts-container {
    padding: 2rem 3rem 32px; /* Adjusted padding */
  }
}

@media (max-width: 1200px) {
  .posts-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1.2rem; /* Adjusted gap */
  }
}

@media (max-width: 992px) {
  .posts-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1rem; /* Adjusted gap */
  }
  
  .page-header h1 {
    font-size: 2.2rem; /* Adjusted font size */
  }
}

@media (max-width: 576px) {
  .user-posts-container {
    padding: 1rem 1rem 32px; /* Adjusted padding */
  }

  .posts-grid {
    grid-template-columns: 1fr; /* Single column on mobile */
    gap: 0.8rem; /* Adjusted gap */
  }
  
  .page-header h1 {
    font-size: 1.8rem; /* Adjusted font size */
  }
  
  .page-header p {
    font-size: 1rem; /* Adjusted font size */
  }
  
  .post-card {
    border-radius: 10px; /* Adjusted border radius */
    padding: 0; /* Set padding to 0 for card */
  }

  .post-header,
  .post-title,
  .post-excerpt,
  .post-footer {
    padding: 0 1rem; /* Apply horizontal padding to inner elements */
  }

  .post-header {
    margin-top: 1rem; /* Add top margin to header */
  }

  .post-footer {
     padding-bottom: 1rem; /* Add bottom padding to footer */
  }

  .post-image {
    margin: 0; /* Ensure no margins */
    border-radius: 10px 10px 0 0; /* Match card border radius top */
  }


  .author-avatar {
    width: 30px; /* Adjusted size */
    height: 30px; /* Adjusted size */
  }
  
  .author-name {
    font-size: 0.8rem; /* Adjusted font size */
  }
  
  .post-date {
    font-size: 0.7rem; /* Adjusted font size */
  }
  
  .post-title {
    font-size: 1rem; /* Adjusted font size */
    margin-bottom: 0.6rem; /* Adjusted margin */
  }
  
  .post-excerpt {
    font-size: 0.9rem; /* Adjusted font size */
    margin-bottom: 1rem; /* Adjusted margin */
  }
  
  .post-footer {
    font-size: 0.75rem; /* Adjusted font size */
    padding-top: 0.6rem; /* Adjusted padding */
  }
  
  .post-stats {
    gap: 0.8rem; /* Adjusted gap */
  }
  
  .post-actions {
    gap: 0.6rem; /* Adjusted gap */
  }
  
  .edit-btn,
  .delete-btn {
    width: 24px; /* Adjusted size */
    height: 24px; /* Adjusted size */
    min-width: 24px; /* Ensure minimum size */
    min-height: 24px; /* Ensure minimum size */
    font-size: 0.8rem; /* Adjusted font size */
  }
  
  .empty-state {
    padding: 30px 10px; /* Adjusted padding */
  }
  
  .empty-icon {
    font-size: 50px; /* Adjusted size */
    margin-bottom: 15px; /* Adjusted margin */
  }
  
  .empty-state p {
    font-size: 1rem; /* Adjusted font size */
    margin-bottom: 15px; /* Adjusted margin */
  }
  
  .create-post-btn {
    padding: 8px 16px; /* Adjusted padding */
    gap: 0.6rem; /* Adjusted gap */
    font-size: 14px; /* Adjusted font size */
  }
}

/* For touch devices */
@media (hover: none) {
  .post-card {
    transform: none !important;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4) !important;
    background: linear-gradient(135deg, #242428, #1a1a20) !important;
    border-radius: 15px !important;
    border: 1px solid rgba(80, 80, 100, 0.3) !important;
  }

  .post-stat {
    min-width: 30px; /* Increased tap area */
  }

  .edit-btn,
  .delete-btn {
    min-width: 30px; /* Increased tap area */
    min-height: 30px; /* Increased tap area */
  }
}

/* Delete overlay styles */
.delete-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7); /* Darker overlay */
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  color: #e0e0e0; /* Light text color */
}

.delete-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(195, 163, 255, 0.3); /* Light purple border */
  border-radius: 50%;
  border-top-color: #c3a3ff; /* Purple border top */
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
  box-shadow: 0 0 20px rgba(120, 70, 200, 0.5); /* Purple shadow */
}

</style> 