import apiClient from '@/services/api';

/**
 * 获取商品评论列表
 * @param {string|number} productId - 商品ID
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.limit - 每页数量
 * @param {string} params.sort - 排序方式 (newest, oldest, most_liked)
 * @returns {Promise} - 返回评论列表Promise对象
 */
export const getProductComments = (productId, params = {}) => {
  return apiClient.get(`/products/${productId}/comments`, { params });
};

/**
 * 添加商品评论
 * @param {string|number} productId - 商品ID
 * @param {Object} commentData - 评论数据
 * @param {string} commentData.content - 评论内容
 * @param {number} commentData.rating - 评分(1-5)
 * @param {string|number} commentData.userId - 用户ID
 * @param {Array} commentData.images - 评论图片数组[可选]
 * @returns {Promise} - 返回添加的评论Promise对象
 */
export const addProductComment = (productId, commentData) => {
  return apiClient.post(`/omg/comments/${productId}/add`, commentData);
};

/**
 * 回复评论
 * @param {string|number} commentId - 评论ID
 * @param {Object} replyData - 回复数据
 * @param {string} replyData.content - 回复内容
 * @returns {Promise} - 返回回复的评论Promise对象
 */
export const replyToComment = (commentId, replyData) => {
  return apiClient.post(`/comments/${commentId}/replies`, replyData);
};

/**
 * 获取评论的回复列表
 * @param {string|number} commentId - 评论ID
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.limit - 每页数量
 * @returns {Promise} - 返回回复列表Promise对象
 */
export const getCommentReplies = (commentId, params = {}) => {
  return apiClient.get(`/comments/${commentId}/replies`, { params });
};

/**
 * 点赞评论
 * @param {string|number} commentId - 评论ID
 * @param {string|number} userId - 用户ID
 * @returns {Promise} - 返回操作结果Promise对象
 */
export const likeComment = (commentId, userId) => {
  console.log(`API调用: 点赞评论 ID=${commentId}, 用户ID=${userId}`);
  return apiClient.post(`/omg/comments/${commentId}/like/${userId}`);
};

/**
 * 取消点赞评论
 * @param {string|number} commentId - 评论ID
 * @param {string|number} userId - 用户ID
 * @returns {Promise} - 返回操作结果Promise对象
 */
export const unlikeComment = (commentId, userId) => {
  console.log(`API调用: 取消点赞评论 ID=${commentId}, 用户ID=${userId}`);
  return apiClient.post(`/omg/comments/${commentId}/unlike/${userId}`);
};

/**
 * 删除评论
 * @param {string|number} commentId - 评论ID
 * @returns {Promise} - 返回操作结果Promise对象
 */
export const deleteComment = (commentId) => {
  return apiClient.delete(`/omg/comments/${commentId}`);
};

/**
 * 批量检查评论点赞状态
 * @param {Object} params - 请求参数
 * @param {string|number} params.userId - 用户ID
 * @param {Array<string|number>} params.commentIds - 评论ID数组
 * @returns {Promise} - 返回点赞状态Promise对象
 */
export const checkCommentsLikeStatus = (params) => {
  console.log('API调用: 检查评论点赞状态', params);
  return apiClient.post('/omg/comments/check-like-status', params);
};

// 商品详情页面所需的评论相关接口
export default {
  getProductComments,    // 获取商品评论
  addProductComment,     // 添加商品评论
  replyToComment,        // 回复评论
  getCommentReplies,     // 获取评论回复
  deleteComment,         // 删除评论
  likeComment,           // 点赞评论
  unlikeComment,         // 取消点赞评论
  checkCommentsLikeStatus // 检查评论点赞状态
};